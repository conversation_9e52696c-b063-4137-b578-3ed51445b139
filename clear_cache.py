#!/usr/bin/env python3
"""
清理缓存脚本 - 清除可能存在的错误K线缓存数据
"""

import os
import shutil
from pathlib import Path

def clear_klines_cache():
    """清理K线相关的缓存文件"""
    cache_dir = Path("cache")
    
    if not cache_dir.exists():
        print("缓存目录不存在")
        return
    
    # 清理K线缓存文件
    klines_files = list(cache_dir.glob("klines_*.pkl"))
    
    if not klines_files:
        print("没有找到K线缓存文件")
        return
    
    print(f"找到 {len(klines_files)} 个K线缓存文件")
    
    for cache_file in klines_files:
        try:
            cache_file.unlink()
            print(f"已删除: {cache_file.name}")
        except Exception as e:
            print(f"删除失败 {cache_file.name}: {e}")
    
    print("K线缓存清理完成")

def clear_strategy_optimizer_cache():
    """清理策略优化器的内存缓存"""
    try:
        # 导入策略优化器
        from strategy_optimizer import get_strategy_optimizer
        
        optimizer = get_strategy_optimizer()
        if optimizer and optimizer.cache_manager:
            optimizer.cache_manager.clear()
            print("策略优化器内存缓存已清理")
        else:
            print("策略优化器未初始化或无缓存管理器")
    except Exception as e:
        print(f"清理策略优化器缓存失败: {e}")

if __name__ == "__main__":
    print("开始清理缓存...")
    clear_klines_cache()
    clear_strategy_optimizer_cache()
    print("缓存清理完成！")