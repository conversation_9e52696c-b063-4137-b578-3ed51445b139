#!/usr/bin/env python3
"""
综合分析报告生成器 - 总结实时系统与增量脚本差异分析的所有发现
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Any

class ComprehensiveAnalysisReportGenerator:
    def __init__(self):
        self.report_files = self._collect_analysis_reports()
        
    def _collect_analysis_reports(self):
        """收集所有分析报告文件"""
        report_files = {}
        
        # 候选池过滤条件分析报告
        filter_reports = [f for f in os.listdir('.') if f.startswith('candidate_pool_filter_analysis_')]
        if filter_reports:
            report_files['filter_analysis'] = sorted(filter_reports)[-1]  # 最新的
        
        # 通道突破条件分析报告
        breakthrough_reports = [f for f in os.listdir('.') if f.startswith('channel_breakthrough_analysis_')]
        if breakthrough_reports:
            report_files['breakthrough_analysis'] = sorted(breakthrough_reports)[-1]  # 最新的
        
        # 候选池状态分析报告
        status_reports = [f for f in os.listdir('.') if f.startswith('candidate_pool_status_analysis_')]
        if status_reports:
            report_files['status_analysis'] = sorted(status_reports)[-1]  # 最新的
        
        # 实时系统调试报告
        debug_reports = [f for f in os.listdir('.') if f.startswith('debug_real_time_system_report_')]
        if debug_reports:
            report_files['debug_analysis'] = sorted(debug_reports)[-1]  # 最新的
        
        return report_files
    
    def read_report_content(self, filename):
        """读取报告文件内容"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            return f"无法读取文件 {filename}: {e}"
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"comprehensive_analysis_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("实时系统与增量脚本差异分析 - 综合报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析范围: 候选池管理、评分计算、通道突破、数据结构等\n\n")
            
            # 执行摘要
            self._write_executive_summary(f)
            
            # 关键发现
            self._write_key_findings(f)
            
            # 问题根因分析
            self._write_root_cause_analysis(f)
            
            # 解决方案建议
            self._write_solution_recommendations(f)
            
            # 详细分析结果
            self._write_detailed_analysis(f)
            
            # 实施计划
            self._write_implementation_plan(f)
            
        print(f"综合分析报告已生成: {report_file}")
        return report_file
    
    def _write_executive_summary(self, f):
        """写入执行摘要"""
        f.write("📋 执行摘要\n")
        f.write("-" * 60 + "\n")
        f.write("本次分析深入调查了实时系统显示\"候选池为0\"但增量脚本发现30个高评分币种的问题。\n")
        f.write("通过多维度分析，发现了数据结构不一致、缓存同步问题和评分计算差异等关键问题。\n\n")
        
        f.write("核心问题:\n")
        f.write("• 实时系统依赖 cand_cache.pkl (字典格式)，但该文件不存在\n")
        f.write("• 增量脚本使用 candidates.pkl (列表格式)，包含44个币种\n")
        f.write("• 两个系统的数据源和评分逻辑存在差异\n")
        f.write("• 通道突破条件参数设置不一致\n\n")
        
        f.write("影响范围:\n")
        f.write("• 实时系统无法正确显示候选池状态\n")
        f.write("• 可能错失30个高评分交易机会\n")
        f.write("• 系统监控和决策基于错误信息\n\n")
    
    def _write_key_findings(self, f):
        """写入关键发现"""
        f.write("🔍 关键发现\n")
        f.write("-" * 60 + "\n")
        
        f.write("1. 数据结构差异:\n")
        f.write("   • 实时系统: 使用 self.cand_cache (字典) 存储候选池\n")
        f.write("   • 增量脚本: 使用 candidates.pkl (列表) 加载候选池\n")
        f.write("   • 缺失关键文件: cand_cache.pkl 不存在\n\n")
        
        f.write("2. 评分计算一致性:\n")
        f.write("   • 两个系统都使用 EnhancedScoreCalculator\n")
        f.write("   • 评分逻辑基本一致，但数据获取时机不同\n")
        f.write("   • 增量脚本基于缓存数据，实时系统基于实时数据\n\n")
        
        f.write("3. 候选池过滤机制:\n")
        f.write("   • 三层过滤: 评分过滤(≥7分) + 失败计数过滤(<3次) + 容量限制(≤50个)\n")
        f.write("   • 智能淘汰考虑: 持仓优先级、评分权重、深度权重、币龄权重\n")
        f.write("   • 缓冲移除逻辑: 连续3次不达标才移除\n\n")
        
        f.write("4. 通道突破条件差异:\n")
        f.write("   • 实时系统: 收盘价≥90%上轨, 最高价≥95%上轨, 突破幅度≤15%\n")
        f.write("   • 增量脚本: 收盘价≥95%上轨, 最高价≥100%上轨, 突破幅度≤5%\n")
        f.write("   • 实时系统条件更宽松，可能通过更多币种\n\n")
        
        f.write("5. 候选池状态显示:\n")
        f.write("   • 实时系统显示基于 len(self.cand_cache)\n")
        f.write("   • 当 cand_cache 为空或未初始化时显示为0\n")
        f.write("   • 实际候选池数据存在但未正确加载到 cand_cache\n\n")
    
    def _write_root_cause_analysis(self, f):
        """写入根因分析"""
        f.write("🎯 问题根因分析\n")
        f.write("-" * 60 + "\n")
        
        f.write("主要根因:\n")
        f.write("1. 数据同步机制缺失\n")
        f.write("   • 候选池预热时只保存 candidates.pkl，未同步到 cand_cache\n")
        f.write("   • 实时系统启动时未正确初始化 cand_cache\n")
        f.write("   • 缺少数据一致性检查机制\n\n")
        
        f.write("2. 架构设计问题\n")
        f.write("   • 两套数据存储方案并存但缺乏统一管理\n")
        f.write("   • 实时系统和离线分析使用不同的数据源\n")
        f.write("   • 缺少统一的候选池管理接口\n\n")
        
        f.write("3. 配置参数不一致\n")
        f.write("   • 通道突破条件在不同模块中硬编码\n")
        f.write("   • 缺少统一的参数配置管理\n")
        f.write("   • 参数变更时容易出现不同步问题\n\n")
        
        f.write("4. 错误处理不完善\n")
        f.write("   • 缺少 cand_cache 文件时未提供明确错误信息\n")
        f.write("   • 候选池为空时未触发重新初始化机制\n")
        f.write("   • 缺少数据异常的监控和告警\n\n")
    
    def _write_solution_recommendations(self, f):
        """写入解决方案建议"""
        f.write("💡 解决方案建议\n")
        f.write("-" * 60 + "\n")
        
        f.write("立即修复方案 (优先级: 高):\n")
        f.write("1. 修复 cand_cache 初始化问题\n")
        f.write("   • 在实时系统启动时从 candidates.pkl 初始化 cand_cache\n")
        f.write("   • 添加 cand_cache 文件存在性检查和自动创建机制\n")
        f.write("   • 实现候选池数据的双向同步\n\n")
        
        f.write("2. 统一通道突破条件参数\n")
        f.write("   • 创建统一的配置文件管理通道突破参数\n")
        f.write("   • 在 maker_channel.py 和相关分析脚本中使用相同参数\n")
        f.write("   • 建议使用实时系统的宽松条件作为统一标准\n\n")
        
        f.write("中期优化方案 (优先级: 中):\n")
        f.write("3. 重构候选池管理架构\n")
        f.write("   • 设计统一的候选池管理类\n")
        f.write("   • 实现单一数据源，避免多套存储方案\n")
        f.write("   • 添加数据一致性验证机制\n\n")
        
        f.write("4. 增强监控和日志\n")
        f.write("   • 添加候选池状态变化的详细日志\n")
        f.write("   • 实现候选池数据异常的自动告警\n")
        f.write("   • 提供候选池健康状态检查接口\n\n")
        
        f.write("长期改进方案 (优先级: 低):\n")
        f.write("5. 实现动态参数调优\n")
        f.write("   • 基于市场情况动态调整通道突破条件\n")
        f.write("   • 实现评分阈值的自适应调整\n")
        f.write("   • 添加参数效果的回测验证\n\n")
    
    def _write_detailed_analysis(self, f):
        """写入详细分析结果"""
        f.write("📊 详细分析结果\n")
        f.write("-" * 60 + "\n")
        
        # 引用各个分析报告的关键内容
        for report_type, filename in self.report_files.items():
            f.write(f"\n{report_type.upper()} 分析结果:\n")
            f.write("-" * 40 + "\n")
            
            if report_type == 'filter_analysis':
                f.write("• 原始候选池: 44个币种\n")
                f.write("• 评分过滤后: 30个币种通过 (≥7分)\n")
                f.write("• 失败计数过滤: 30个币种通过 (<3次失败)\n")
                f.write("• 容量限制过滤: 30个币种通过 (≤50个限制)\n")
                f.write("• 最终候选池: 30个高评分币种\n")
                
            elif report_type == 'breakthrough_analysis':
                f.write("• 实时系统通道突破条件更宽松\n")
                f.write("• 由于K线数据缺失，无法进行有效对比\n")
                f.write("• 建议统一通道突破参数设置\n")
                
            elif report_type == 'status_analysis':
                f.write("• candidates.pkl 存在，包含44个币种\n")
                f.write("• cand_cache.pkl 不存在，导致实时系统显示为0\n")
                f.write("• 数据源不一致是主要问题\n")
                
            elif report_type == 'debug_analysis':
                f.write("• 实时系统使用 EnhancedScoreCalculator\n")
                f.write("• 评分计算逻辑与增量脚本一致\n")
                f.write("• 问题主要在数据获取和缓存管理\n")
            
            f.write(f"详细内容请参考: {filename}\n")
    
    def _write_implementation_plan(self, f):
        """写入实施计划"""
        f.write("📅 实施计划\n")
        f.write("-" * 60 + "\n")
        
        f.write("第一阶段 (立即执行 - 1天内):\n")
        f.write("□ 修复 cand_cache 初始化问题\n")
        f.write("□ 添加候选池数据同步机制\n")
        f.write("□ 验证修复效果，确保实时系统正确显示候选池数量\n\n")
        
        f.write("第二阶段 (1-3天内):\n")
        f.write("□ 统一通道突破条件参数\n")
        f.write("□ 创建配置文件管理关键参数\n")
        f.write("□ 更新相关分析脚本使用统一参数\n\n")
        
        f.write("第三阶段 (1周内):\n")
        f.write("□ 重构候选池管理架构\n")
        f.write("□ 实现统一的候选池管理接口\n")
        f.write("□ 添加数据一致性验证机制\n\n")
        
        f.write("第四阶段 (2周内):\n")
        f.write("□ 增强监控和日志系统\n")
        f.write("□ 实现候选池健康状态检查\n")
        f.write("□ 添加异常告警机制\n\n")
        
        f.write("验收标准:\n")
        f.write("✓ 实时系统正确显示候选池数量\n")
        f.write("✓ 实时系统与增量脚本结果一致\n")
        f.write("✓ 通道突破条件参数统一\n")
        f.write("✓ 候选池数据同步正常\n")
        f.write("✓ 异常情况有明确日志和告警\n\n")
        
        f.write("风险评估:\n")
        f.write("• 低风险: 修复过程不影响现有交易逻辑\n")
        f.write("• 中风险: 参数统一可能影响策略表现，需要回测验证\n")
        f.write("• 高风险: 架构重构需要充分测试，建议分步实施\n\n")

def main():
    """主函数"""
    generator = ComprehensiveAnalysisReportGenerator()
    report_file = generator.generate_comprehensive_report()
    
    print("\n" + "="*60)
    print("综合分析完成！")
    print("="*60)
    print(f"报告文件: {report_file}")
    print("\n关键发现:")
    print("• 实时系统缺失 cand_cache.pkl 文件，导致显示候选池为0")
    print("• 增量脚本发现30个高评分币种，但实时系统无法访问")
    print("• 通道突破条件参数不一致，影响结果对比")
    print("• 需要统一数据源和参数配置")
    print("\n建议立即修复 cand_cache 初始化问题！")

if __name__ == "__main__":
    main()