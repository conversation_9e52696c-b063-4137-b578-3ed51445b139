"""
批次处理系统测试脚本
验证监控、可视化和配置管理功能
"""

import os
import sys
import time
import random
import threading
from datetime import datetime

# 添加策略目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy'))

from strategy.batch_integration import BatchProcessingSystem, create_batch_system
from strategy.batch_config import BatchConfig, ConfigTemplates

class BatchSystemTester:
    """批次处理系统测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.system = None
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始批次处理系统测试")
        print("="*60)
        
        tests = [
            ("配置管理测试", self.test_config_management),
            ("系统启动停止测试", self.test_system_lifecycle),
            ("批次扫描测试", self.test_batch_scanning),
            ("进度监控测试", self.test_progress_monitoring),
            ("异常处理测试", self.test_error_handling),
            ("性能压力测试", self.test_performance),
            ("可视化功能测试", self.test_visualization)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 {test_name}")
            print("-" * 40)
            
            try:
                start_time = time.time()
                result = test_func()
                duration = time.time() - start_time
                
                self.test_results[test_name] = {
                    "status": "✅ 通过" if result else "❌ 失败",
                    "duration": duration,
                    "result": result
                }
                
                print(f"结果: {self.test_results[test_name]['status']} ({duration:.2f}秒)")
                
            except Exception as e:
                self.test_results[test_name] = {
                    "status": "❌ 异常",
                    "duration": 0,
                    "error": str(e)
                }
                print(f"结果: ❌ 异常 - {e}")
        
        self._print_test_summary()
    
    def test_config_management(self) -> bool:
        """测试配置管理功能"""
        try:
            # 测试默认配置
            config = BatchConfig()
            assert config.validate(), "默认配置验证失败"
            print("✓ 默认配置验证通过")
            
            # 测试配置保存和加载
            test_config_file = "test_batch_config.json"
            config.save_to_file(test_config_file)
            loaded_config = BatchConfig.load_from_file(test_config_file)
            assert config.to_dict() == loaded_config.to_dict(), "配置保存加载失败"
            print("✓ 配置保存加载通过")
            
            # 测试配置模板
            templates = {
                "fast": ConfigTemplates.get_fast_scan_config(),
                "stable": ConfigTemplates.get_stable_scan_config(),
                "conservative": ConfigTemplates.get_conservative_scan_config()
            }
            
            for name, template in templates.items():
                assert template.validate(), f"{name}模板验证失败"
            print("✓ 配置模板验证通过")
            
            # 清理测试文件
            if os.path.exists(test_config_file):
                os.remove(test_config_file)
            
            return True
            
        except Exception as e:
            print(f"✗ 配置管理测试失败: {e}")
            return False
    
    def test_system_lifecycle(self) -> bool:
        """测试系统生命周期"""
        try:
            # 创建系统
            self.system = create_batch_system("fast_scan")
            print("✓ 系统创建成功")
            
            # 启动系统
            self.system.start_system()
            time.sleep(2)  # 等待启动完成
            print("✓ 系统启动成功")
            
            # 检查系统状态
            status = self.system.get_progress_status()
            assert isinstance(status, dict), "状态获取失败"
            print("✓ 状态获取成功")
            
            # 停止系统
            self.system.stop_system()
            time.sleep(1)  # 等待停止完成
            print("✓ 系统停止成功")
            
            return True
            
        except Exception as e:
            print(f"✗ 系统生命周期测试失败: {e}")
            return False
    
    def test_batch_scanning(self) -> bool:
        """测试批次扫描功能"""
        try:
            if not self.system:
                self.system = create_batch_system("fast_scan")
                self.system.start_system()
            
            # 生成测试币种
            test_symbols = [f"TEST{i}USDT" for i in range(1, 101)]
            
            # 开始批次扫描
            cycle_id = self.system.start_batch_scan(test_symbols)
            assert cycle_id, "扫描周期创建失败"
            print(f"✓ 扫描周期创建成功: {cycle_id}")
            
            # 模拟批次处理
            batch_size = self.system.config.batch_size
            total_batches = (len(test_symbols) + batch_size - 1) // batch_size
            
            for batch_index in range(total_batches):
                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, len(test_symbols))
                batch_symbols = test_symbols[start_idx:end_idx]
                
                # 开始批次处理
                batch_id = self.system.start_batch_processing(cycle_id, batch_index, batch_symbols)
                
                # 模拟处理时间
                time.sleep(0.1)
                
                # 模拟处理结果
                processed = batch_symbols[:int(len(batch_symbols) * 0.8)]  # 80%成功
                failed = batch_symbols[len(processed):]
                
                # 完成批次处理
                self.system.complete_batch_processing(batch_id, processed, failed)
                
                print(f"✓ 批次 {batch_index + 1}/{total_batches} 处理完成")
            
            # 完成扫描周期
            self.system.complete_scan_cycle(cycle_id)
            print("✓ 扫描周期完成")
            
            return True
            
        except Exception as e:
            print(f"✗ 批次扫描测试失败: {e}")
            return False
    
    def test_progress_monitoring(self) -> bool:
        """测试进度监控功能"""
        try:
            if not self.system:
                self.system = create_batch_system("fast_scan")
                self.system.start_system()
            
            # 获取进度状态
            status = self.system.get_progress_status()
            assert isinstance(status, dict), "进度状态获取失败"
            print("✓ 进度状态获取成功")
            
            # 获取扫描统计
            stats = self.system.get_scan_statistics()
            assert isinstance(stats, dict), "扫描统计获取失败"
            print("✓ 扫描统计获取成功")
            
            # 导出进度报告
            report_file = self.system.export_progress_report("test_progress_report.json")
            assert os.path.exists(report_file), "进度报告导出失败"
            print("✓ 进度报告导出成功")
            
            # 清理测试文件
            if os.path.exists(report_file):
                os.remove(report_file)
            
            return True
            
        except Exception as e:
            print(f"✗ 进度监控测试失败: {e}")
            return False
    
    def test_error_handling(self) -> bool:
        """测试异常处理功能"""
        try:
            if not self.system:
                self.system = create_batch_system("fast_scan")
                self.system.start_system()
            
            # 测试重复扫描检测
            test_symbols = ["DUPLICATE1USDT", "DUPLICATE2USDT"]
            cycle_id = self.system.start_batch_scan(test_symbols)
            
            # 模拟重复扫描
            batch_id1 = self.system.start_batch_processing(cycle_id, 0, test_symbols)
            batch_id2 = self.system.start_batch_processing(cycle_id, 0, test_symbols)  # 重复
            
            # 检查是否检测到重复
            status = self.system.get_progress_status()
            duplicate_count = status.get('duplicate_scans', 0)
            
            print(f"✓ 重复扫描检测: {duplicate_count} 个币种")
            
            # 完成处理
            self.system.complete_batch_processing(batch_id1, test_symbols, [])
            self.system.complete_batch_processing(batch_id2, [], test_symbols)  # 标记为失败
            self.system.complete_scan_cycle(cycle_id)
            
            return True
            
        except Exception as e:
            print(f"✗ 异常处理测试失败: {e}")
            return False
    
    def test_performance(self) -> bool:
        """测试性能压力"""
        try:
            if not self.system:
                self.system = create_batch_system("fast_scan")
                self.system.start_system()
            
            # 生成大量测试币种
            large_symbol_list = [f"PERF{i}USDT" for i in range(1, 501)]  # 500个币种
            
            start_time = time.time()
            
            # 开始大批量扫描
            cycle_id = self.system.start_batch_scan(large_symbol_list)
            
            # 快速处理所有批次
            batch_size = self.system.config.batch_size
            total_batches = (len(large_symbol_list) + batch_size - 1) // batch_size
            
            for batch_index in range(total_batches):
                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, len(large_symbol_list))
                batch_symbols = large_symbol_list[start_idx:end_idx]
                
                batch_id = self.system.start_batch_processing(cycle_id, batch_index, batch_symbols)
                self.system.complete_batch_processing(batch_id, batch_symbols, [])
            
            self.system.complete_scan_cycle(cycle_id)
            
            duration = time.time() - start_time
            throughput = len(large_symbol_list) / duration
            
            print(f"✓ 性能测试完成: {len(large_symbol_list)} 个币种, {duration:.2f}秒")
            print(f"✓ 处理速度: {throughput:.1f} 币种/秒")
            
            # 性能要求：至少10币种/秒
            return throughput >= 10
            
        except Exception as e:
            print(f"✗ 性能测试失败: {e}")
            return False
    
    def test_visualization(self) -> bool:
        """测试可视化功能"""
        try:
            if not self.system:
                self.system = create_batch_system("fast_scan")
                self.system.start_system()
            
            # 启动可视化
            self.system.visualizer.start_display()
            time.sleep(2)  # 让可视化运行一段时间
            
            # 生成HTML报告
            html_file = self.system.visualizer.generate_html_report()
            assert os.path.exists(html_file), "HTML报告生成失败"
            print(f"✓ HTML报告生成成功: {html_file}")
            
            # 检查HTML内容
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
                assert "批次处理进度监控" in html_content, "HTML内容不完整"
            print("✓ HTML内容验证通过")
            
            # 停止可视化
            self.system.visualizer.stop_display()
            
            return True
            
        except Exception as e:
            print(f"✗ 可视化测试失败: {e}")
            return False
    
    def _print_test_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("📊 测试结果摘要")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r["status"] == "✅ 通过")
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = result["status"]
            duration = result.get("duration", 0)
            print(f"  {status} {test_name} ({duration:.2f}秒)")
            
            if "error" in result:
                print(f"    错误: {result['error']}")
        
        print("="*60)
        
        # 清理系统
        if self.system:
            self.system.stop_system()
        
        # 清理测试文件
        test_files = ["batch_progress.html", "test_progress_report.json", "batch_config.json"]
        for file in test_files:
            if os.path.exists(file):
                os.remove(file)


def run_quick_test():
    """运行快速测试"""
    print("🚀 快速测试模式")
    
    # 创建系统
    system = create_batch_system("fast_scan")
    system.start_system()
    
    try:
        # 简单扫描测试
        test_symbols = [f"QUICK{i}USDT" for i in range(1, 21)]
        cycle_id = system.start_batch_scan(test_symbols)
        
        print(f"✓ 扫描周期启动: {cycle_id}")
        
        # 等待一段时间观察
        for i in range(10):
            status = system.get_progress_status()
            print(f"进度: {status.get('progress_percentage', 0):.1f}%")
            time.sleep(1)
        
        print("✓ 快速测试完成")
        
    finally:
        system.stop_system()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="批次处理系统测试")
    parser.add_argument("--quick", action="store_true", help="运行快速测试")
    parser.add_argument("--full", action="store_true", help="运行完整测试")
    
    args = parser.parse_args()
    
    if args.quick:
        run_quick_test()
    elif args.full:
        tester = BatchSystemTester()
        tester.run_all_tests()
    else:
        print("请选择测试模式:")
        print("  --quick  快速测试")
        print("  --full   完整测试")
        
        choice = input("\n选择测试模式 (quick/full): ").strip().lower()
        if choice == "quick":
            run_quick_test()
        elif choice == "full":
            tester = BatchSystemTester()
            tester.run_all_tests()
        else:
            print("无效选择")