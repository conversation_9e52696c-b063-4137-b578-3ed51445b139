#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批次处理系统演示脚本
用于展示批次监控、可视化和配置管理功能
"""

import time
import random
from strategy.batch_integration import BatchProcessingSystem, create_batch_system

def simulate_batch_processing(system):
    """模拟批次处理过程"""
    print("\n🚀 开始模拟批次处理...")
    
    # 模拟币种列表
    symbols = [f"SYMBOL{i}USDT" for i in range(1, 21)]  # 20个模拟币种
    
    # 启动扫描周期
    cycle_id = system.start_batch_scan(symbols)
    print(f"✓ 扫描周期已启动: {cycle_id}")
    
    # 获取进度状态以确定实际批次数量
    progress = system.get_progress_status()
    total_batches = progress.get('total_batches', 1)
    
    print(f"📊 总批次数: {total_batches}")
    
    # 模拟处理所有批次
    for batch_index in range(total_batches):
        print(f"\n📦 开始处理批次 {batch_index + 1}/{total_batches}")
        
        # 开始批次处理
        batch_id = system.start_batch_processing(cycle_id, batch_index, [])
        
        if batch_id:
            print(f"   批次ID: {batch_id}")
            
            # 模拟处理时间
            processing_time = random.uniform(2, 5)
            time.sleep(processing_time)
            
            # 获取当前批次的币种信息
            progress = system.get_progress_status()
            batch_details = progress.get('current_batch_details', [])
            
            # 找到当前批次的币种
            current_batch_symbols = []
            for batch_detail in batch_details:
                if batch_detail.get('batch_index') == batch_index:
                    current_batch_symbols = batch_detail.get('symbols_in_batch', [])
                    break
            
            # 模拟处理结果
            processed_symbols = current_batch_symbols  # 假设全部处理成功
            failed_symbols = []  # 无失败币种
            
            # 完成批次处理
            system.complete_batch_processing(batch_id, processed_symbols, failed_symbols)
            
            print(f"   ✓ 批次完成 - 处理: {len(processed_symbols)}, 失败: {len(failed_symbols)}")
        else:
            print(f"   ❌ 批次启动失败")
        
        # 短暂等待以观察进度更新
        time.sleep(1)
    
    # 完成扫描周期
    system.complete_scan_cycle(cycle_id)
    print(f"\n✅ 扫描周期完成: {cycle_id}")

def demo_configuration_management():
    """演示配置管理功能"""
    print("\n" + "="*60)
    print("📋 配置管理演示")
    print("="*60)
    
    # 创建系统
    system = create_batch_system()
    
    # 显示当前配置
    print("\n📊 当前配置:")
    system.config.print_summary()
    
    # 获取配置模板
    templates = system.get_config_templates()
    print(f"\n📝 可用配置模板: {list(templates.keys())}")
    
    # 应用快速扫描模板
    print("\n🚀 应用快速扫描配置...")
    system.apply_config_template('fast_scan')
    system.config.print_summary()
    
    return system

def demo_monitoring_and_visualization():
    """演示监控和可视化功能"""
    print("\n" + "="*60)
    print("📊 监控和可视化演示")
    print("="*60)
    
    # 创建系统
    system = create_batch_system()
    
    # 启动系统
    print("\n🔧 启动批次处理系统...")
    system.start_system()
    
    # 等待系统初始化
    time.sleep(2)
    
    # 模拟批次处理
    simulate_batch_processing(system)
    
    # 等待一段时间观察监控
    print("\n⏳ 观察监控状态 (10秒)...")
    time.sleep(10)
    
    # 生成报告
    print("\n📄 生成HTML报告...")
    try:
        report_file = system.visualizer.generate_html_report()
        print(f"✓ HTML报告已生成: {report_file}")
    except Exception as e:
        print(f"❌ HTML报告生成失败: {e}")
        report_file = None
    
    # 停止系统
    print("\n🛑 停止系统...")
    system.stop_system()
    
    return report_file

def demo_health_check():
    """演示系统健康检查"""
    print("\n" + "="*60)
    print("🏥 系统健康检查演示")
    print("="*60)
    
    system = create_batch_system()
    system.start_system()
    
    # 等待系统运行
    time.sleep(3)
    
    # 执行健康检查
    print("\n🔍 系统健康检查演示...")
    print("   ✓ 系统组件状态检查")
    print("   ✓ 配置参数验证")
    print("   ✓ 监控线程状态")
    print("   ✓ 可视化组件状态")
    
    # 模拟健康状态
    health_status = {
        "system_status": "healthy",
        "monitor_active": system.monitor.monitoring_active,
        "visualizer_active": system.visualizer.display_active,
        "config_valid": True,
        "last_check": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    print("\n📋 健康检查结果:")
    print(f"   📊 系统状态: {'✅ 健康' if health_status['system_status'] == 'healthy' else '❌ 异常'}")
    print(f"   📈 监控状态: {'✅ 活跃' if health_status['monitor_active'] else '❌ 停止'}")
    print(f"   📊 可视化状态: {'✅ 活跃' if health_status['visualizer_active'] else '❌ 停止'}")
    print(f"   ⚙️ 配置状态: {'✅ 有效' if health_status['config_valid'] else '❌ 无效'}")
    
    system.stop_system()
    return health_status

def main():
    """主演示函数"""
    print("🎯 批次处理系统完整演示")
    print("="*60)
    
    try:
        # 1. 配置管理演示
        system = demo_configuration_management()
        
        # 2. 监控和可视化演示
        report_file = demo_monitoring_and_visualization()
        
        # 3. 健康检查演示
        health_status = demo_health_check()
        
        # 演示总结
        print("\n" + "="*60)
        print("🎉 演示完成总结")
        print("="*60)
        print("✅ 配置管理: 成功")
        print("✅ 批次处理: 成功")
        print("✅ 监控可视化: 成功")
        print(f"✅ HTML报告: {report_file}")
        print(f"✅ 健康检查: {'全部正常' if all(health_status.values()) else '存在异常'}")
        
        print("\n🔍 系统特性:")
        print("• 实时批次进度监控")
        print("• 可视化进度显示")
        print("• 配置模板管理")
        print("• 异常检测和恢复")
        print("• HTML报告导出")
        print("• 系统健康检查")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()