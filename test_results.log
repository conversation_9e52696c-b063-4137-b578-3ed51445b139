2025-09-25 19:39:11,705 - StrategyTester - INFO - 开始运行优化策略测试套件...
2025-09-25 19:39:11,705 - StrategyTester - INFO - === 测试配置管理器 ===
2025-09-25 19:39:11,724 - StrategyTester - ERROR - 配置管理器测试失败: 'NoneType' object has no attribute 'get_config'
2025-09-25 19:39:11,725 - StrategyTester - INFO - === 测试增强评分计算器 ===
2025-09-25 19:39:11,749 - StrategyTester - ERROR - 增强评分计算器测试失败: EnhancedScoreCalculator.calculate_comprehensive_score() got an unexpected keyword argument 'age_days'
2025-09-25 19:39:11,749 - StrategyTester - INFO - === 测试监控系统 ===
2025-09-25 19:39:11,750 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 19:39:11,751 - StrategyTester - ERROR - 监控系统测试失败: 'MetricsCollector' object has no attribute 'record_order_metric'
2025-09-25 19:39:11,751 - StrategyTester - INFO - === 测试策略优化器 ===
2025-09-25 19:39:11,754 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 19:39:11,754 - StrategyTester - ERROR - 策略优化器测试失败: CacheManager.set() got an unexpected keyword argument 'ttl'
2025-09-25 19:39:11,755 - StrategyTester - INFO - === 测试系统集成 ===
2025-09-25 19:39:11,755 - StrategyTester - ERROR - 系统集成测试失败: name 'MockHttpClient' is not defined
2025-09-25 19:39:11,755 - StrategyTester - INFO - === 性能测试 ===
2025-09-25 19:39:11,759 - StrategyTester - ERROR - 性能测试失败: EnhancedScoreCalculator.calculate_comprehensive_score() got an unexpected keyword argument 'age_days'
2025-09-25 19:39:11,760 - StrategyTester - INFO - === 生成测试报告 ===
2025-09-25 19:39:11,761 - StrategyTester - INFO - 测试报告已保存到 test_report.json
2025-09-25 19:39:11,764 - StrategyTester - INFO - 测试总结:
2025-09-25 19:39:11,764 - StrategyTester - INFO - - 总测试数: 6
2025-09-25 19:39:11,765 - StrategyTester - INFO - - 通过测试: 0
2025-09-25 19:39:11,765 - StrategyTester - INFO - - 失败测试: 6
2025-09-25 19:39:11,766 - StrategyTester - INFO - - 成功率: 0.0%
2025-09-25 19:42:35,964 - StrategyTester - INFO - 开始运行优化策略测试套件...
2025-09-25 19:42:35,965 - StrategyTester - INFO - === 测试配置管理器 ===
2025-09-25 19:42:35,965 - config_manager - INFO - 开始加载配置文件...
2025-09-25 19:42:35,965 - config_manager - INFO - 配置文件不存在，尝试从模板创建: config.json
2025-09-25 19:42:35,965 - config_manager - WARNING - 环境变量未设置: BINANCE_API_KEY
2025-09-25 19:42:35,965 - config_manager - WARNING - 环境变量未设置: BINANCE_API_SECRET
2025-09-25 19:42:35,984 - config_manager - INFO - 从模板创建配置文件: config\config.json
2025-09-25 19:42:35,985 - config_manager - INFO - 成功加载配置: config.json
2025-09-25 19:42:35,986 - config_manager - INFO - 配置文件不存在，尝试从模板创建: trading_config.json
2025-09-25 19:42:35,988 - config_manager - INFO - 从模板创建配置文件: config\trading_config.json
2025-09-25 19:42:35,990 - config_manager - INFO - 成功加载配置: trading_config.json
2025-09-25 19:42:35,990 - config_manager - INFO - 配置文件不存在，尝试从模板创建: risk_config.json
2025-09-25 19:42:35,993 - config_manager - INFO - 从模板创建配置文件: config\risk_config.json
2025-09-25 19:42:35,993 - config_manager - INFO - 成功加载配置: risk_config.json
2025-09-25 19:42:35,995 - config_manager - INFO - 配置文件不存在，尝试从模板创建: api_config.json
2025-09-25 19:42:35,996 - config_manager - WARNING - 环境变量未设置: BINANCE_API_KEY
2025-09-25 19:42:35,996 - config_manager - WARNING - 环境变量未设置: BINANCE_API_SECRET
2025-09-25 19:42:35,998 - config_manager - INFO - 从模板创建配置文件: config\api_config.json
2025-09-25 19:42:35,998 - config_manager - INFO - 成功加载配置: api_config.json
2025-09-25 19:42:35,998 - config_manager - INFO - 开始验证配置...
2025-09-25 19:42:35,999 - config_manager - WARNING - 仓位大小无效，使用默认值
2025-09-25 19:42:36,000 - config_manager - WARNING - 杠杆倍数无效，使用默认值
2025-09-25 19:42:36,000 - config_manager - WARNING - 最大回撤设置无效，使用默认值
2025-09-25 19:42:36,006 - config_manager - INFO - 配置验证完成
2025-09-25 19:42:36,008 - config_manager - INFO - 配置加载完成
2025-09-25 19:42:36,008 - StrategyTester - INFO - 主配置加载成功: 7 个配置项
2025-09-25 19:42:36,008 - config_manager - INFO - 开始验证配置...
2025-09-25 19:42:36,008 - config_manager - INFO - 配置验证完成
2025-09-25 19:42:36,008 - StrategyTester - INFO - 配置验证结果: None
2025-09-25 19:42:36,008 - StrategyTester - INFO - === 测试增强评分计算器 ===
2025-09-25 19:42:36,058 - StrategyTester - INFO - 综合评分: 6.48
2025-09-25 19:42:36,060 - StrategyTester - INFO - depth: 0.00
2025-09-25 19:42:36,061 - StrategyTester - INFO - volume: 0.50
2025-09-25 19:42:36,063 - StrategyTester - INFO - age: 0.50
2025-09-25 19:42:36,064 - StrategyTester - INFO - momentum: 1.00
2025-09-25 19:42:36,064 - StrategyTester - INFO - channel: 2.00
2025-09-25 19:42:36,064 - StrategyTester - INFO - volatility: 0.50
2025-09-25 19:42:36,065 - StrategyTester - INFO - liquidity: 0.00
2025-09-25 19:42:36,066 - StrategyTester - INFO - 数据质量评分: 0.70
2025-09-25 19:42:36,067 - StrategyTester - INFO - === 测试监控系统 ===
2025-09-25 19:42:36,068 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 19:42:36,069 - 核心指标监控系统 - INFO - 记录末位淘汰: ADAUSDT
2025-09-25 19:42:38,072 - StrategyTester - INFO - 当前指标: {
  "timestamp": "2025-09-25T19:42:38.072956",
  "order_metrics": {
    "total_orders": 2,
    "successful_orders": 1,
    "failed_orders": 0,
    "cancelled_orders": 1,
    "success_rate": 0.5,
    "avg_execution_time": 100.0,
    "total_execution_time": 100.0
  },
  "position_metrics": {
    "total_positions": 1,
    "active_positions": 1,
    "closed_positions": 0,
    "profitable_positions": 0,
    "losing_positions": 0,
    "win_rate": 0.0,
    "avg_holding_time": 0.0,
    "total_pnl": 0.0,
    "avg_pnl": 0.0
  },
  "elimination_metrics": {
    "total_eliminations": 1,
    "elimination_frequency": 1,
    "eliminated_symbols": [
      "ADAUSDT"
    ],
    "avg_elimination_interval": 0.0,
    "last_elimination_time": "2025-09-25T19:42:36.069303"
  },
  "system_metrics": {
    "uptime": 0.05620241165161133,
    "cpu_usage": 0.0,
    "memory_usage": 67.8,
    "network_latency": 50,
    "api_call_count": 2,
    "api_error_count": 1,
    "api_success_rate": 0.5
  },
  "performance_metrics": {
    "total_return": 0.0,
    "daily_return": 0.0,
    "max_drawdown": 0.0,
    "sharpe_ratio": 0.0,
    "win_rate": 0.0,
    "profit_factor": 0.0,
    "avg_trade_duration": 0.0
  }
}
2025-09-25 19:42:38,072 - StrategyTester - INFO - 监控摘要: 
=== 核心指标监控摘要 ===
更新时间: 2025-09-25T19:42:38.072956

【订单指标】
- 总订单数: 2
- 成功率: 50.00%
- 平均执行时间: 100.00秒

【持仓指标】
- 活跃持仓: 1
- 已平仓: 0
- 胜率: 0.00%
- 总盈亏: 0.00

【末位淘汰指标】
- 总淘汰次数: 1
- 淘汰频率: 1.0/小时
- 平均间隔: 0.0分钟

【系统指标】
- 运行时间: 0秒
- API成功率: 50.00%
- API调用数: 2

2025-09-25 19:42:38,080 - 核心指标监控系统 - INFO - 核心指标监控系统停止
2025-09-25 19:42:38,081 - StrategyTester - INFO - === 测试策略优化器 ===
2025-09-25 19:42:38,082 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 19:42:38,083 - StrategyTester - INFO - 缓存测试: {'data': 'test_value'}
2025-09-25 19:42:43,088 - StrategyTester - INFO - 批处理结果: None
2025-09-25 19:42:43,088 - StrategyTester - INFO - 优化器性能指标: {
  "task_count": 0,
  "success_count": 0,
  "error_count": 0,
  "total_execution_time": 0.0,
  "avg_execution_time": 0.0,
  "peak_memory": 0.6829999999999999,
  "peak_cpu": 0.5720000000000001,
  "uptime": 5.00636100769043,
  "success_rate": 0,
  "tasks_per_second": 0.0,
  "cache_size": 1,
  "cache_hit_rate": 0,
  "running_tasks": 0,
  "queue_size": 0
}
2025-09-25 19:42:43,088 - strategy_optimizer - INFO - 策略优化器停止
2025-09-25 19:42:43,088 - StrategyTester - INFO - === 测试系统集成 ===
2025-09-25 19:42:43,088 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 19:42:43,100 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 19:42:43,101 - ReferenceFolder.symbol_selector - INFO - 币种选择器初始化完成，选择前50个币种，最低评分要求大于:7
2025-09-25 19:42:43,104 - MakerChannel - INFO - 监控系统和策略优化器已启动
2025-09-25 19:42:43,106 - MakerChannel - INFO - 执行轻量启动预热（仅基础信息）...
2025-09-25 19:42:43,107 - MakerChannel - INFO - 启动时无持仓
2025-09-25 19:42:43,108 - MakerChannel - INFO - 交易所有效交易对数量: 2, 过滤后有效交易对数量: 2
2025-09-25 19:42:43,109 - MakerChannel - INFO - 预热完成，候选池包含 2 个交易对
2025-09-25 19:42:43,110 - MakerChannel - ERROR - 启动预热失败: 'MetricsCollector' object has no attribute 'record_order_metric'
2025-09-25 19:42:43,112 - StrategyTester - ERROR - 系统集成测试失败: 'MetricsCollector' object has no attribute 'record_order_metric'
2025-09-25 19:42:43,114 - StrategyTester - INFO - === 性能测试 ===
2025-09-25 19:42:43,496 - StrategyTester - INFO - 性能测试完成:
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 处理了100次大数据集评分
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 每个数据集包含1000个数据点
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 总耗时: 0.38秒
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 平均每次评分耗时: 0.0038秒
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 评分范围: 5.43 - 5.43
2025-09-25 19:42:43,496 - StrategyTester - INFO - === 生成测试报告 ===
2025-09-25 19:42:43,496 - StrategyTester - INFO - 测试报告已保存到 test_report.json
2025-09-25 19:42:43,496 - StrategyTester - INFO - 测试总结:
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 总测试数: 6
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 通过测试: 5
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 失败测试: 1
2025-09-25 19:42:43,496 - StrategyTester - INFO - - 成功率: 83.3%
2025-09-25 20:48:27,497 - StrategyTester - INFO - 开始运行优化策略测试套件...
2025-09-25 20:48:27,497 - StrategyTester - INFO - === 测试配置管理器 ===
2025-09-25 20:48:27,497 - config_manager - INFO - 开始加载配置文件...
2025-09-25 20:48:27,497 - config_manager - INFO - 成功加载配置: config.json
2025-09-25 20:48:27,497 - config_manager - INFO - 成功加载配置: trading_config.json
2025-09-25 20:48:27,497 - config_manager - INFO - 成功加载配置: risk_config.json
2025-09-25 20:48:27,497 - config_manager - INFO - 成功加载配置: api_config.json
2025-09-25 20:48:27,497 - config_manager - INFO - 开始验证配置...
2025-09-25 20:48:27,497 - config_manager - WARNING - 仓位大小无效，使用默认值
2025-09-25 20:48:27,497 - config_manager - WARNING - 杠杆倍数无效，使用默认值
2025-09-25 20:48:27,497 - config_manager - WARNING - 最大回撤设置无效，使用默认值
2025-09-25 20:48:27,497 - config_manager - INFO - 配置验证完成
2025-09-25 20:48:27,497 - config_manager - INFO - 配置加载完成
2025-09-25 20:48:27,497 - StrategyTester - INFO - 主配置加载成功: 7 个配置项
2025-09-25 20:48:27,497 - config_manager - INFO - 开始验证配置...
2025-09-25 20:48:27,497 - config_manager - INFO - 配置验证完成
2025-09-25 20:48:27,497 - StrategyTester - INFO - 配置验证结果: None
2025-09-25 20:48:27,497 - StrategyTester - INFO - === 测试增强评分计算器 ===
2025-09-25 20:48:27,568 - StrategyTester - INFO - 综合评分: 6.48
2025-09-25 20:48:27,571 - StrategyTester - INFO - depth: 0.00
2025-09-25 20:48:27,571 - StrategyTester - INFO - volume: 0.50
2025-09-25 20:48:27,571 - StrategyTester - INFO - age: 0.50
2025-09-25 20:48:27,571 - StrategyTester - INFO - momentum: 1.00
2025-09-25 20:48:27,571 - StrategyTester - INFO - channel: 2.00
2025-09-25 20:48:27,571 - StrategyTester - INFO - volatility: 0.50
2025-09-25 20:48:27,571 - StrategyTester - INFO - liquidity: 0.00
2025-09-25 20:48:27,574 - StrategyTester - INFO - 数据质量评分: 0.70
2025-09-25 20:48:27,576 - StrategyTester - INFO - === 测试监控系统 ===
2025-09-25 20:48:27,576 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 20:48:27,593 - 核心指标监控系统 - INFO - 记录末位淘汰: ADAUSDT
2025-09-25 20:48:29,599 - StrategyTester - INFO - 当前指标: {
  "timestamp": "2025-09-25T20:48:29.599650",
  "order_metrics": {
    "total_orders": 2,
    "successful_orders": 1,
    "failed_orders": 0,
    "cancelled_orders": 1,
    "success_rate": 0.5,
    "avg_execution_time": 100.0,
    "total_execution_time": 100.0
  },
  "position_metrics": {
    "total_positions": 1,
    "active_positions": 1,
    "closed_positions": 0,
    "profitable_positions": 0,
    "losing_positions": 0,
    "win_rate": 0.0,
    "avg_holding_time": 0.0,
    "total_pnl": 0.0,
    "avg_pnl": 0.0
  },
  "elimination_metrics": {
    "total_eliminations": 1,
    "elimination_frequency": 1,
    "eliminated_symbols": [
      "ADAUSDT"
    ],
    "avg_elimination_interval": 0.0,
    "last_elimination_time": "2025-09-25T20:48:27.593827"
  },
  "system_metrics": {
    "uptime": 0.051969289779663086,
    "cpu_usage": 0.0,
    "memory_usage": 77.9,
    "network_latency": 50,
    "api_call_count": 2,
    "api_error_count": 1,
    "api_success_rate": 0.5
  },
  "performance_metrics": {
    "total_return": 0.0,
    "daily_return": 0.0,
    "max_drawdown": 0.0,
    "sharpe_ratio": 0.0,
    "win_rate": 0.0,
    "profit_factor": 0.0,
    "avg_trade_duration": 0.0
  }
}
2025-09-25 20:48:29,605 - StrategyTester - INFO - 监控摘要: 
=== 核心指标监控摘要 ===
更新时间: 2025-09-25T20:48:29.605667

【订单指标】
- 总订单数: 2
- 成功率: 50.00%
- 平均执行时间: 100.00秒

【持仓指标】
- 活跃持仓: 1
- 已平仓: 0
- 胜率: 0.00%
- 总盈亏: 0.00

【末位淘汰指标】
- 总淘汰次数: 1
- 淘汰频率: 1.0/小时
- 平均间隔: 0.0分钟

【系统指标】
- 运行时间: 0秒
- API成功率: 50.00%
- API调用数: 2

2025-09-25 20:48:29,613 - 核心指标监控系统 - INFO - 核心指标监控系统停止
2025-09-25 20:48:29,613 - StrategyTester - INFO - === 测试策略优化器 ===
2025-09-25 20:48:29,614 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 20:48:29,615 - StrategyTester - INFO - 缓存测试: {'data': 'test_value'}
2025-09-25 20:48:34,623 - StrategyTester - INFO - 批处理结果: {'processed': True, 'data': {'test': 'data'}, 'processing_time': 5.0075507164001465}
2025-09-25 20:48:34,623 - StrategyTester - INFO - 优化器性能指标: {
  "task_count": 0,
  "success_count": 0,
  "error_count": 0,
  "total_execution_time": 0.0,
  "avg_execution_time": 0.0,
  "peak_memory": 0.7859999999999999,
  "peak_cpu": 0.586,
  "uptime": 5.010554075241089,
  "success_rate": 0,
  "tasks_per_second": 0.0,
  "cache_size": 1,
  "cache_hit_rate": 0,
  "running_tasks": 0,
  "queue_size": 0
}
2025-09-25 20:48:34,628 - strategy_optimizer - INFO - 策略优化器停止
2025-09-25 20:48:34,628 - StrategyTester - INFO - === 测试系统集成 ===
2025-09-25 20:48:34,628 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 20:48:34,647 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 20:48:34,647 - ReferenceFolder.symbol_selector - INFO - 币种选择器初始化完成，选择前50个币种，最低评分要求大于:7
2025-09-25 20:48:34,649 - MakerChannel - INFO - 监控系统和策略优化器已启动
2025-09-25 20:48:34,649 - MakerChannel - INFO - 执行轻量启动预热（仅基础信息）...
2025-09-25 20:48:34,650 - MakerChannel - INFO - 启动时无持仓
2025-09-25 20:48:34,652 - MakerChannel - INFO - 交易所有效交易对数量: 2, 过滤后有效交易对数量: 2
2025-09-25 20:48:34,652 - MakerChannel - INFO - 预热完成，候选池包含 2 个交易对
2025-09-25 20:48:34,652 - StrategyTester - INFO - 策略预热完成
2025-09-25 20:48:34,656 - MakerChannel - INFO - 异步打分批次 1: 处理 2 个币种
2025-09-25 20:48:34,657 - StrategyTester - INFO - 异步打分测试完成
2025-09-25 20:48:34,657 - MakerChannel - ERROR - 网络状态检查失败: 'MetricsCollector' object has no attribute 'update_system_metrics'
2025-09-25 20:48:34,657 - StrategyTester - INFO - 网络状态: {'connected': False, 'latency': 0, 'last_error': "'MetricsCollector' object has no attribute 'update_system_metrics'"}
2025-09-25 20:48:34,657 - StrategyTester - INFO - 订单生命周期管理测试完成
2025-09-25 20:48:34,657 - StrategyTester - INFO - 策略监控指标: {
  "timestamp": "2025-09-25T20:48:34.657036",
  "order_metrics": {
    "total_orders": 1,
    "successful_orders": 0,
    "failed_orders": 1,
    "cancelled_orders": 0,
    "success_rate": 0.0,
    "avg_execution_time": 0.0,
    "total_execution_time": 0.0
  },
  "position_metrics": {
    "total_positions": 0,
    "active_positions": 0,
    "closed_positions": 0,
    "profitable_positions": 0,
    "losing_positions": 0,
    "win_rate": 0.0,
    "avg_holding_time": 0.0,
    "total_pnl": 0.0,
    "avg_pnl": 0.0
  },
  "elimination_metrics": {
    "total_eliminations": 0,
    "elimination_frequency": 0.0,
    "eliminated_symbols": [],
    "avg_elimination_interval": 0.0,
    "last_elimination_time": null
  },
  "system_metrics": {
    "uptime": 0.012821435928344727,
    "cpu_usage": 100.0,
    "memory_usage": 78.6,
    "network_latency": 0.0,
    "api_call_count": 0,
    "api_error_count": 0,
    "api_success_rate": 0.0
  },
  "performance_metrics": {
    "total_return": 0.0,
    "daily_return": 0.0,
    "max_drawdown": 0.0,
    "sharpe_ratio": 0.0,
    "win_rate": 0.0,
    "profit_factor": 0.0,
    "avg_trade_duration": 0.0
  }
}
2025-09-25 20:48:34,667 - 核心指标监控系统 - INFO - 核心指标监控系统停止
2025-09-25 20:48:34,667 - strategy_optimizer - INFO - 策略优化器停止
2025-09-25 20:48:34,667 - StrategyTester - INFO - === 性能测试 ===
2025-09-25 20:48:35,188 - StrategyTester - INFO - 性能测试完成:
2025-09-25 20:48:35,189 - StrategyTester - INFO - - 处理了100次大数据集评分
2025-09-25 20:48:35,191 - StrategyTester - INFO - - 每个数据集包含1000个数据点
2025-09-25 20:48:35,192 - StrategyTester - INFO - - 总耗时: 0.52秒
2025-09-25 20:48:35,195 - StrategyTester - INFO - - 平均每次评分耗时: 0.0052秒
2025-09-25 20:48:35,199 - StrategyTester - INFO - - 评分范围: 5.43 - 5.43
2025-09-25 20:48:35,202 - StrategyTester - INFO - === 生成测试报告 ===
2025-09-25 20:48:35,206 - StrategyTester - INFO - 测试报告已保存到 test_report.json
2025-09-25 20:48:35,210 - StrategyTester - INFO - 测试总结:
2025-09-25 20:48:35,212 - StrategyTester - INFO - - 总测试数: 6
2025-09-25 20:48:35,217 - StrategyTester - INFO - - 通过测试: 6
2025-09-25 20:48:35,223 - StrategyTester - INFO - - 失败测试: 0
2025-09-25 20:48:35,223 - StrategyTester - INFO - - 成功率: 100.0%
2025-09-25 20:54:06,020 - StrategyTester - INFO - 开始运行优化策略测试套件...
2025-09-25 20:54:06,020 - StrategyTester - INFO - === 测试配置管理器 ===
2025-09-25 20:54:06,037 - config_manager - INFO - 开始加载配置文件...
2025-09-25 20:54:06,037 - config_manager - INFO - 成功加载配置: config.json
2025-09-25 20:54:06,037 - config_manager - INFO - 成功加载配置: trading_config.json
2025-09-25 20:54:06,037 - config_manager - INFO - 成功加载配置: risk_config.json
2025-09-25 20:54:06,043 - config_manager - INFO - 成功加载配置: api_config.json
2025-09-25 20:54:06,044 - config_manager - INFO - 开始验证配置...
2025-09-25 20:54:06,044 - config_manager - WARNING - 仓位大小无效，使用默认值
2025-09-25 20:54:06,045 - config_manager - WARNING - 杠杆倍数无效，使用默认值
2025-09-25 20:54:06,045 - config_manager - WARNING - 最大回撤设置无效，使用默认值
2025-09-25 20:54:06,046 - config_manager - INFO - 配置验证完成
2025-09-25 20:54:06,046 - config_manager - INFO - 配置加载完成
2025-09-25 20:54:06,046 - StrategyTester - INFO - 主配置加载成功: 7 个配置项
2025-09-25 20:54:06,047 - config_manager - INFO - 开始验证配置...
2025-09-25 20:54:06,048 - config_manager - INFO - 配置验证完成
2025-09-25 20:54:06,049 - StrategyTester - INFO - 配置验证结果: None
2025-09-25 20:54:06,049 - StrategyTester - INFO - === 测试增强评分计算器 ===
2025-09-25 20:54:06,062 - StrategyTester - INFO - 综合评分: 6.48
2025-09-25 20:54:06,064 - StrategyTester - INFO - depth: 0.00
2025-09-25 20:54:06,064 - StrategyTester - INFO - volume: 0.50
2025-09-25 20:54:06,065 - StrategyTester - INFO - age: 0.50
2025-09-25 20:54:06,065 - StrategyTester - INFO - momentum: 1.00
2025-09-25 20:54:06,065 - StrategyTester - INFO - channel: 2.00
2025-09-25 20:54:06,066 - StrategyTester - INFO - volatility: 0.50
2025-09-25 20:54:06,066 - StrategyTester - INFO - liquidity: 0.00
2025-09-25 20:54:06,066 - StrategyTester - INFO - 数据质量评分: 0.70
2025-09-25 20:54:06,067 - StrategyTester - INFO - === 测试监控系统 ===
2025-09-25 20:54:06,067 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 20:54:06,067 - 核心指标监控系统 - INFO - 记录末位淘汰: ADAUSDT
2025-09-25 20:54:08,070 - StrategyTester - INFO - 当前指标: {
  "timestamp": "2025-09-25T20:54:08.070445",
  "order_metrics": {
    "total_orders": 2,
    "successful_orders": 1,
    "failed_orders": 0,
    "cancelled_orders": 1,
    "success_rate": 0.5,
    "avg_execution_time": 100.0,
    "total_execution_time": 100.0
  },
  "position_metrics": {
    "total_positions": 1,
    "active_positions": 1,
    "closed_positions": 0,
    "profitable_positions": 0,
    "losing_positions": 0,
    "win_rate": 0.0,
    "avg_holding_time": 0.0,
    "total_pnl": 0.0,
    "avg_pnl": 0.0
  },
  "elimination_metrics": {
    "total_eliminations": 1,
    "elimination_frequency": 1,
    "eliminated_symbols": [
      "ADAUSDT"
    ],
    "avg_elimination_interval": 0.0,
    "last_elimination_time": "2025-09-25T20:54:06.067406"
  },
  "system_metrics": {
    "uptime": 0.04645252227783203,
    "cpu_usage": 100.0,
    "memory_usage": 74.1,
    "network_latency": 50,
    "api_call_count": 2,
    "api_error_count": 1,
    "api_success_rate": 0.5
  },
  "performance_metrics": {
    "total_return": 0.0,
    "daily_return": 0.0,
    "max_drawdown": 0.0,
    "sharpe_ratio": 0.0,
    "win_rate": 0.0,
    "profit_factor": 0.0,
    "avg_trade_duration": 0.0
  }
}
2025-09-25 20:54:08,072 - StrategyTester - INFO - 监控摘要: 
=== 核心指标监控摘要 ===
更新时间: 2025-09-25T20:54:08.070445

【订单指标】
- 总订单数: 2
- 成功率: 50.00%
- 平均执行时间: 100.00秒

【持仓指标】
- 活跃持仓: 1
- 已平仓: 0
- 胜率: 0.00%
- 总盈亏: 0.00

【末位淘汰指标】
- 总淘汰次数: 1
- 淘汰频率: 1.0/小时
- 平均间隔: 0.0分钟

【系统指标】
- 运行时间: 0秒
- API成功率: 50.00%
- API调用数: 2

2025-09-25 20:54:08,074 - 核心指标监控系统 - INFO - 核心指标监控系统停止
2025-09-25 20:54:08,074 - StrategyTester - INFO - === 测试策略优化器 ===
2025-09-25 20:54:08,076 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 20:54:08,076 - StrategyTester - INFO - 缓存测试: {'data': 'test_value'}
2025-09-25 20:54:13,088 - StrategyTester - INFO - 批处理结果: None
2025-09-25 20:54:13,088 - StrategyTester - INFO - 优化器性能指标: {
  "task_count": 0,
  "success_count": 0,
  "error_count": 0,
  "total_execution_time": 0.0,
  "avg_execution_time": 0.0,
  "peak_memory": 0.742,
  "peak_cpu": 0.612,
  "uptime": 5.014529466629028,
  "success_rate": 0,
  "tasks_per_second": 0.0,
  "cache_size": 1,
  "cache_hit_rate": 0,
  "running_tasks": 0,
  "queue_size": 0
}
2025-09-25 20:54:13,088 - strategy_optimizer - INFO - 策略优化器停止
2025-09-25 20:54:13,088 - StrategyTester - INFO - === 测试系统集成 ===
2025-09-25 20:54:13,088 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 20:54:13,088 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 20:54:13,088 - ReferenceFolder.symbol_selector - INFO - 币种选择器初始化完成，选择前50个币种，最低评分要求大于:7
2025-09-25 20:54:13,088 - MakerChannel - INFO - 监控系统和策略优化器已启动
2025-09-25 20:54:13,088 - MakerChannel - INFO - 执行轻量启动预热（仅基础信息）...
2025-09-25 20:54:13,088 - MakerChannel - INFO - 启动时无持仓
2025-09-25 20:54:13,088 - MakerChannel - INFO - 交易所有效交易对数量: 2, 过滤后有效交易对数量: 2
2025-09-25 20:54:13,094 - MakerChannel - INFO - 预热完成，候选池包含 2 个交易对
2025-09-25 20:54:13,095 - StrategyTester - INFO - 策略预热完成
2025-09-25 20:54:13,096 - MakerChannel - INFO - 异步打分批次 1: 处理 2 个币种
2025-09-25 20:54:13,096 - StrategyTester - INFO - 异步打分测试完成
2025-09-25 20:54:13,096 - MakerChannel - ERROR - 网络状态检查失败: 'MetricsCollector' object has no attribute 'update_system_metrics'
2025-09-25 20:54:13,097 - StrategyTester - INFO - 网络状态: {'connected': False, 'latency': 0, 'last_error': "'MetricsCollector' object has no attribute 'update_system_metrics'"}
2025-09-25 20:54:13,098 - StrategyTester - INFO - 订单生命周期管理测试完成
2025-09-25 20:54:13,100 - StrategyTester - INFO - 策略监控指标: {
  "timestamp": "2025-09-25T20:54:13.100147",
  "order_metrics": {
    "total_orders": 1,
    "successful_orders": 0,
    "failed_orders": 1,
    "cancelled_orders": 0,
    "success_rate": 0.0,
    "avg_execution_time": 0.0,
    "total_execution_time": 0.0
  },
  "position_metrics": {
    "total_positions": 0,
    "active_positions": 0,
    "closed_positions": 0,
    "profitable_positions": 0,
    "losing_positions": 0,
    "win_rate": 0.0,
    "avg_holding_time": 0.0,
    "total_pnl": 0.0,
    "avg_pnl": 0.0
  },
  "elimination_metrics": {
    "total_eliminations": 0,
    "elimination_frequency": 0.0,
    "eliminated_symbols": [],
    "avg_elimination_interval": 0.0,
    "last_elimination_time": null
  },
  "system_metrics": {
    "uptime": 0.0,
    "cpu_usage": 25.0,
    "memory_usage": 74.0,
    "network_latency": 0.0,
    "api_call_count": 0,
    "api_error_count": 0,
    "api_success_rate": 0.0
  },
  "performance_metrics": {
    "total_return": 0.0,
    "daily_return": 0.0,
    "max_drawdown": 0.0,
    "sharpe_ratio": 0.0,
    "win_rate": 0.0,
    "profit_factor": 0.0,
    "avg_trade_duration": 0.0
  }
}
2025-09-25 20:54:13,106 - 核心指标监控系统 - INFO - 核心指标监控系统停止
2025-09-25 20:54:13,106 - strategy_optimizer - INFO - 策略优化器停止
2025-09-25 20:54:13,106 - StrategyTester - INFO - === 性能测试 ===
2025-09-25 20:54:13,480 - StrategyTester - INFO - 性能测试完成:
2025-09-25 20:54:13,480 - StrategyTester - INFO - - 处理了100次大数据集评分
2025-09-25 20:54:13,480 - StrategyTester - INFO - - 每个数据集包含1000个数据点
2025-09-25 20:54:13,480 - StrategyTester - INFO - - 总耗时: 0.37秒
2025-09-25 20:54:13,480 - StrategyTester - INFO - - 平均每次评分耗时: 0.0037秒
2025-09-25 20:54:13,480 - StrategyTester - INFO - - 评分范围: 5.43 - 5.43
2025-09-25 20:54:13,480 - StrategyTester - INFO - === 生成测试报告 ===
2025-09-25 20:54:13,480 - StrategyTester - INFO - 测试报告已保存到 test_report.json
2025-09-25 20:54:13,480 - StrategyTester - INFO - 测试总结:
2025-09-25 20:54:13,480 - StrategyTester - INFO - - 总测试数: 6
2025-09-25 20:54:13,480 - StrategyTester - INFO - - 通过测试: 6
2025-09-25 20:54:13,480 - StrategyTester - INFO - - 失败测试: 0
2025-09-25 20:54:13,486 - StrategyTester - INFO - - 成功率: 100.0%
2025-09-25 20:56:18,195 - StrategyTester - INFO - 开始运行优化策略测试套件...
2025-09-25 20:56:18,195 - StrategyTester - INFO - === 测试配置管理器 ===
2025-09-25 20:56:18,200 - config_manager - INFO - 开始加载配置文件...
2025-09-25 20:56:18,211 - config_manager - INFO - 成功加载配置: config.json
2025-09-25 20:56:18,212 - config_manager - INFO - 成功加载配置: trading_config.json
2025-09-25 20:56:18,215 - config_manager - INFO - 成功加载配置: risk_config.json
2025-09-25 20:56:18,232 - config_manager - INFO - 成功加载配置: api_config.json
2025-09-25 20:56:18,236 - config_manager - INFO - 开始验证配置...
2025-09-25 20:56:18,242 - config_manager - WARNING - 仓位大小无效，使用默认值
2025-09-25 20:56:18,243 - config_manager - WARNING - 杠杆倍数无效，使用默认值
2025-09-25 20:56:18,251 - config_manager - WARNING - 最大回撤设置无效，使用默认值
2025-09-25 20:56:18,251 - config_manager - INFO - 配置验证完成
2025-09-25 20:56:18,404 - config_manager - INFO - 配置加载完成
2025-09-25 20:56:18,469 - StrategyTester - INFO - 主配置加载成功: 7 个配置项
2025-09-25 20:56:18,518 - config_manager - INFO - 开始验证配置...
2025-09-25 20:56:18,537 - config_manager - INFO - 配置验证完成
2025-09-25 20:56:18,553 - StrategyTester - INFO - 配置验证结果: None
2025-09-25 20:56:18,560 - StrategyTester - INFO - === 测试增强评分计算器 ===
2025-09-25 20:56:18,574 - StrategyTester - INFO - 综合评分: 6.48
2025-09-25 20:56:18,582 - StrategyTester - INFO - depth: 0.00
2025-09-25 20:56:18,586 - StrategyTester - INFO - volume: 0.50
2025-09-25 20:56:18,589 - StrategyTester - INFO - age: 0.50
2025-09-25 20:56:18,590 - StrategyTester - INFO - momentum: 1.00
2025-09-25 20:56:18,591 - StrategyTester - INFO - channel: 2.00
2025-09-25 20:56:18,601 - StrategyTester - INFO - volatility: 0.50
2025-09-25 20:56:18,610 - StrategyTester - INFO - liquidity: 0.00
2025-09-25 20:56:18,611 - StrategyTester - INFO - 数据质量评分: 0.70
2025-09-25 20:56:18,613 - StrategyTester - INFO - === 测试监控系统 ===
2025-09-25 20:56:18,614 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 20:56:18,619 - 核心指标监控系统 - INFO - 记录末位淘汰: ADAUSDT
2025-09-25 20:56:20,620 - StrategyTester - INFO - 当前指标: {
  "timestamp": "2025-09-25T20:56:20.620582",
  "order_metrics": {
    "total_orders": 2,
    "successful_orders": 1,
    "failed_orders": 0,
    "cancelled_orders": 1,
    "success_rate": 0.5,
    "avg_execution_time": 100.0,
    "total_execution_time": 100.0
  },
  "position_metrics": {
    "total_positions": 1,
    "active_positions": 1,
    "closed_positions": 0,
    "profitable_positions": 0,
    "losing_positions": 0,
    "win_rate": 0.0,
    "avg_holding_time": 0.0,
    "total_pnl": 0.0,
    "avg_pnl": 0.0
  },
  "elimination_metrics": {
    "total_eliminations": 1,
    "elimination_frequency": 1,
    "eliminated_symbols": [
      "ADAUSDT"
    ],
    "avg_elimination_interval": 0.0,
    "last_elimination_time": "2025-09-25T20:56:18.619824"
  },
  "system_metrics": {
    "uptime": 0.16486811637878418,
    "cpu_usage": 100.0,
    "memory_usage": 74.5,
    "network_latency": 50,
    "api_call_count": 2,
    "api_error_count": 1,
    "api_success_rate": 0.5
  },
  "performance_metrics": {
    "total_return": 0.0,
    "daily_return": 0.0,
    "max_drawdown": 0.0,
    "sharpe_ratio": 0.0,
    "win_rate": 0.0,
    "profit_factor": 0.0,
    "avg_trade_duration": 0.0
  }
}
2025-09-25 20:56:20,627 - StrategyTester - INFO - 监控摘要: 
=== 核心指标监控摘要 ===
更新时间: 2025-09-25T20:56:20.627864

【订单指标】
- 总订单数: 2
- 成功率: 50.00%
- 平均执行时间: 100.00秒

【持仓指标】
- 活跃持仓: 1
- 已平仓: 0
- 胜率: 0.00%
- 总盈亏: 0.00

【末位淘汰指标】
- 总淘汰次数: 1
- 淘汰频率: 1.0/小时
- 平均间隔: 0.0分钟

【系统指标】
- 运行时间: 0秒
- API成功率: 50.00%
- API调用数: 2

2025-09-25 20:56:20,635 - 核心指标监控系统 - INFO - 核心指标监控系统停止
2025-09-25 20:56:20,635 - StrategyTester - INFO - === 测试策略优化器 ===
2025-09-25 20:56:20,652 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 20:56:20,652 - StrategyTester - INFO - 缓存测试: {'data': 'test_value'}
2025-09-25 20:56:25,655 - StrategyTester - INFO - 批处理结果: {'processed': True, 'data': {'test': 'data'}, 'processing_time': 5.0022687911987305}
2025-09-25 20:56:25,655 - StrategyTester - INFO - 优化器性能指标: {
  "task_count": 0,
  "success_count": 0,
  "error_count": 0,
  "total_execution_time": 0.0,
  "avg_execution_time": 0.0,
  "peak_memory": 0.7440000000000001,
  "peak_cpu": 0.583,
  "uptime": 5.019071102142334,
  "success_rate": 0,
  "tasks_per_second": 0.0,
  "cache_size": 1,
  "cache_hit_rate": 0,
  "running_tasks": 0,
  "queue_size": 0
}
2025-09-25 20:56:25,655 - strategy_optimizer - INFO - 策略优化器停止
2025-09-25 20:56:25,655 - StrategyTester - INFO - === 测试系统集成 ===
2025-09-25 20:56:25,655 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-25 20:56:25,655 - strategy_optimizer - INFO - 策略优化器启动
2025-09-25 20:56:25,655 - ReferenceFolder.symbol_selector - INFO - 币种选择器初始化完成，选择前50个币种，最低评分要求大于:7
2025-09-25 20:56:25,655 - MakerChannel - INFO - 监控系统和策略优化器已启动
2025-09-25 20:56:25,655 - MakerChannel - INFO - 执行轻量启动预热（仅基础信息）...
2025-09-25 20:56:25,655 - MakerChannel - INFO - 启动时无持仓
2025-09-25 20:56:25,662 - MakerChannel - INFO - 交易所有效交易对数量: 2, 过滤后有效交易对数量: 2
2025-09-25 20:56:25,662 - MakerChannel - INFO - 预热完成，候选池包含 2 个交易对
2025-09-25 20:56:25,662 - StrategyTester - INFO - 策略预热完成
2025-09-25 20:56:25,663 - MakerChannel - INFO - 异步打分批次 1: 处理 2 个币种
2025-09-25 20:56:25,663 - StrategyTester - INFO - 异步打分测试完成
2025-09-25 20:56:25,663 - MakerChannel - ERROR - 网络状态检查失败: 'MetricsCollector' object has no attribute 'update_system_metrics'
2025-09-25 20:56:25,663 - StrategyTester - INFO - 网络状态: {'connected': False, 'latency': 0, 'last_error': "'MetricsCollector' object has no attribute 'update_system_metrics'"}
2025-09-25 20:56:25,664 - StrategyTester - INFO - 订单生命周期管理测试完成
2025-09-25 20:56:25,665 - StrategyTester - INFO - 策略监控指标: {
  "timestamp": "2025-09-25T20:56:25.665147",
  "order_metrics": {
    "total_orders": 1,
    "successful_orders": 0,
    "failed_orders": 1,
    "cancelled_orders": 0,
    "success_rate": 0.0,
    "avg_execution_time": 0.0,
    "total_execution_time": 0.0
  },
  "position_metrics": {
    "total_positions": 0,
    "active_positions": 0,
    "closed_positions": 0,
    "profitable_positions": 0,
    "losing_positions": 0,
    "win_rate": 0.0,
    "avg_holding_time": 0.0,
    "total_pnl": 0.0,
    "avg_pnl": 0.0
  },
  "elimination_metrics": {
    "total_eliminations": 0,
    "elimination_frequency": 0.0,
    "eliminated_symbols": [],
    "avg_elimination_interval": 0.0,
    "last_elimination_time": null
  },
  "system_metrics": {
    "uptime": 0.0,
    "cpu_usage": 0.0,
    "memory_usage": 74.4,
    "network_latency": 0.0,
    "api_call_count": 0,
    "api_error_count": 0,
    "api_success_rate": 0.0
  },
  "performance_metrics": {
    "total_return": 0.0,
    "daily_return": 0.0,
    "max_drawdown": 0.0,
    "sharpe_ratio": 0.0,
    "win_rate": 0.0,
    "profit_factor": 0.0,
    "avg_trade_duration": 0.0
  }
}
2025-09-25 20:56:25,673 - 核心指标监控系统 - INFO - 核心指标监控系统停止
2025-09-25 20:56:25,674 - strategy_optimizer - INFO - 策略优化器停止
2025-09-25 20:56:25,675 - StrategyTester - INFO - === 性能测试 ===
2025-09-25 20:56:26,049 - StrategyTester - INFO - 性能测试完成:
2025-09-25 20:56:26,049 - StrategyTester - INFO - - 处理了100次大数据集评分
2025-09-25 20:56:26,049 - StrategyTester - INFO - - 每个数据集包含1000个数据点
2025-09-25 20:56:26,049 - StrategyTester - INFO - - 总耗时: 0.37秒
2025-09-25 20:56:26,049 - StrategyTester - INFO - - 平均每次评分耗时: 0.0037秒
2025-09-25 20:56:26,049 - StrategyTester - INFO - - 评分范围: 5.43 - 5.43
2025-09-25 20:56:26,049 - StrategyTester - INFO - === 生成测试报告 ===
2025-09-25 20:56:26,049 - StrategyTester - INFO - 测试报告已保存到 test_report.json
2025-09-25 20:56:26,056 - StrategyTester - INFO - 测试总结:
2025-09-25 20:56:26,056 - StrategyTester - INFO - - 总测试数: 6
2025-09-25 20:56:26,056 - StrategyTester - INFO - - 通过测试: 6
2025-09-25 20:56:26,056 - StrategyTester - INFO - - 失败测试: 0
2025-09-25 20:56:26,056 - StrategyTester - INFO - - 成功率: 100.0%
