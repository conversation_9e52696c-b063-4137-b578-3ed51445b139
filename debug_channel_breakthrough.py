#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通道突破条件调试脚本
用于诊断为什么候选池被清空的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pickle
import json
import pandas as pd
import yaml
from pathlib import Path
import logging
from binance_trader import BinanceTrader
from cache_manager import CacheManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def dynamic_tf_for_channel(age_days):
    """动态时间框架计算（复制自原代码）"""
    if age_days <= 30:
        return 10
    elif age_days <= 90:
        return 15
    elif age_days <= 180:
        return 20
    elif age_days <= 365:
        return 25
    else:
        return 30

def load_channel_config():
    """加载通道配置"""
    try:
        config_path = Path('config/channel_config.json')
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        else:
            logger.warning("配置文件不存在，使用默认配置")
            return {
                "channel_breakthrough": {
                    "close_ratio_threshold": 1.002,
                    "high_ratio_threshold": 1.008,
                    "max_breakthrough_pct": 0.15,
                    "max_consecutive_near_upper": 8,
                    "min_volume_ratio": 1.2
                }
            }
    except Exception as e:
        logger.error(f"加载配置失败: {e}")
        return {}

def check_channel_breakthrough_debug(symbol, df_d, age_days, config):
    """调试版本的通道突破检查"""
    try:
        # 获取配置参数
        breakthrough_config = config.get('channel_breakthrough', {})
        close_ratio_threshold = breakthrough_config.get('close_ratio_threshold', 1.002)
        high_ratio_threshold = breakthrough_config.get('high_ratio_threshold', 1.008)
        max_breakthrough_pct = breakthrough_config.get('max_breakthrough_pct', 0.15)
        max_consecutive_near_upper = breakthrough_config.get('max_consecutive_near_upper', 8)
        min_volume_ratio = breakthrough_config.get('min_volume_ratio', 1.2)
        
        # 使用动态时间框架计算通道周期
        n = dynamic_tf_for_channel(age_days)
        n = max(2, int(n))
        
        # 支持新旧列名格式
        high_col = 'high' if 'high' in df_d.columns else 'h'
        close_col = 'close' if 'close' in df_d.columns else 'c'
        volume_col = 'volume' if 'volume' in df_d.columns else 'v'
        
        # 计算通道上轨（历史最高价）
        upper_band = df_d[high_col].rolling(n).max().iloc[-1]
        current_price = df_d[close_col].iloc[-1]
        high_price = df_d[high_col].iloc[-1]
        current_volume = df_d[volume_col].iloc[-1]
        avg_volume = df_d[volume_col].rolling(n).mean().iloc[-1]
        
        # 条件检查
        close_ratio = current_price / upper_band
        high_ratio = high_price / upper_band
        
        # 检查突破条件
        breakthrough_condition = (close_ratio >= close_ratio_threshold) or (high_ratio >= high_ratio_threshold)
        
        # 突破幅度检查
        breakthrough_pct_ok = True
        breakthrough_pct = 0
        if high_ratio >= 1.0:
            breakthrough_pct = (high_price - upper_band) / upper_band
            breakthrough_pct_ok = breakthrough_pct <= max_breakthrough_pct
        
        # 连续接近上轨检查
        recent_above_count = 0
        check_range = min(max_consecutive_near_upper, len(df_d))
        for i in range(-check_range, 0):
            if i >= -len(df_d):
                historical_upper = df_d[high_col].rolling(n).max().iloc[i]
                if df_d[high_col].iloc[i] >= historical_upper * 0.95:
                    recent_above_count += 1
        
        consecutive_ok = recent_above_count < max_consecutive_near_upper
        
        # 成交量检查
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
        volume_ok = volume_ratio >= min_volume_ratio
        
        # 最终结果
        final_result = breakthrough_condition and breakthrough_pct_ok and consecutive_ok and volume_ok
        
        return {
            'symbol': symbol,
            'final_result': final_result,
            'close_ratio': close_ratio,
            'high_ratio': high_ratio,
            'breakthrough_condition': breakthrough_condition,
            'breakthrough_pct': breakthrough_pct,
            'breakthrough_pct_ok': breakthrough_pct_ok,
            'recent_above_count': recent_above_count,
            'consecutive_ok': consecutive_ok,
            'volume_ratio': volume_ratio,
            'volume_ok': volume_ok,
            'n': n,
            'upper_band': upper_band,
            'current_price': current_price,
            'thresholds': {
                'close_ratio_threshold': close_ratio_threshold,
                'high_ratio_threshold': high_ratio_threshold,
                'max_breakthrough_pct': max_breakthrough_pct,
                'max_consecutive_near_upper': max_consecutive_near_upper,
                'min_volume_ratio': min_volume_ratio
            }
        }
        
    except Exception as e:
        logger.error(f"通道突破检查失败 {symbol}: {e}")
        return None

def main():
    """主函数"""
    logger.info("开始通道突破条件调试...")
    
    # 加载配置
    config = load_channel_config()
    logger.info(f"当前配置: {config}")
    
    # 加载配置文件
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    # 初始化交易器和缓存管理器
    trader = BinanceTrader(cfg)
    cache_manager = CacheManager()
    
    # 加载候选池
    cand_cache_path = Path('cache/cand_cache.pkl')
    candidates_path = Path('cache/candidates.pkl')
    
    candidates = []
    if cand_cache_path.exists():
        try:
            with open(cand_cache_path, 'rb') as f:
                candidates_data = pickle.load(f)
            # 如果是字典格式，提取symbol列表
            if isinstance(candidates_data, dict):
                candidates = list(candidates_data.keys())
            else:
                candidates = candidates_data
            logger.info(f"从 cand_cache.pkl 加载了 {len(candidates)} 个候选币种")
        except Exception as e:
            logger.error(f"加载 cand_cache.pkl 失败: {e}")
    
    if not candidates and candidates_path.exists():
        try:
            with open(candidates_path, 'rb') as f:
                candidates = pickle.load(f)
            logger.info(f"从 candidates.pkl 加载了 {len(candidates)} 个候选币种")
        except Exception as e:
            logger.error(f"加载 candidates.pkl 失败: {e}")
    
    if not candidates:
        logger.error("候选池为空，无法进行调试")
        return
    
    logger.info(f"加载到 {len(candidates)} 个候选币种")
    
    # 测试前10个币种
    test_symbols = candidates[:10]
    results = []
    
    for symbol in test_symbols:
        logger.info(f"测试币种: {symbol}")
        
        try:
            # 获取日线数据
            klines_data = trader.get_klines(symbol, '1d', limit=100)
            if not klines_data or len(klines_data) < 10:
                logger.warning(f"{symbol}: 数据不足")
                continue
            
            # 转换为DataFrame格式
            df = pd.DataFrame(klines_data, columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # 转换数据类型
            for col in ['open', 'high', 'low', 'close', 'volume', 'quote_asset_volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 假设币龄为30天（可以根据实际情况调整）
            age_days = 30
            
            # 执行通道突破检查
            result = check_channel_breakthrough_debug(symbol, df, age_days, config)
            if result:
                results.append(result)
                
                # 输出详细结果
                logger.info(f"{symbol} 检查结果:")
                logger.info(f"  最终结果: {result['final_result']}")
                logger.info(f"  收盘比例: {result['close_ratio']:.4f} (阈值: {result['thresholds']['close_ratio_threshold']})")
                logger.info(f"  最高价比例: {result['high_ratio']:.4f} (阈值: {result['thresholds']['high_ratio_threshold']})")
                logger.info(f"  突破条件: {result['breakthrough_condition']}")
                logger.info(f"  突破幅度: {result['breakthrough_pct']:.4f} (限制: {result['thresholds']['max_breakthrough_pct']})")
                logger.info(f"  突破幅度OK: {result['breakthrough_pct_ok']}")
                logger.info(f"  连续接近次数: {result['recent_above_count']} (限制: {result['thresholds']['max_consecutive_near_upper']})")
                logger.info(f"  连续检查OK: {result['consecutive_ok']}")
                logger.info(f"  成交量比例: {result['volume_ratio']:.2f} (最小: {result['thresholds']['min_volume_ratio']})")
                logger.info(f"  成交量OK: {result['volume_ok']}")
                logger.info(f"  动态周期: {result['n']}根K线")
                logger.info("")
                
        except Exception as e:
            logger.error(f"测试 {symbol} 失败: {e}")
    
    # 统计结果
    total_tested = len(results)
    passed_count = sum(1 for r in results if r['final_result'])
    
    logger.info("=" * 60)
    logger.info(f"测试总结:")
    logger.info(f"  测试币种数: {total_tested}")
    logger.info(f"  通过数量: {passed_count}")
    logger.info(f"  通过率: {passed_count/total_tested*100:.1f}%" if total_tested > 0 else "  通过率: 0%")
    
    # 分析失败原因
    if total_tested > 0:
        failed_breakthrough = sum(1 for r in results if not r['breakthrough_condition'])
        failed_pct = sum(1 for r in results if not r['breakthrough_pct_ok'])
        failed_consecutive = sum(1 for r in results if not r['consecutive_ok'])
        failed_volume = sum(1 for r in results if not r['volume_ok'])
        
        logger.info(f"失败原因分析:")
        logger.info(f"  未满足突破条件: {failed_breakthrough} 个")
        logger.info(f"  突破幅度过大: {failed_pct} 个")
        logger.info(f"  连续接近时间过长: {failed_consecutive} 个")
        logger.info(f"  成交量不足: {failed_volume} 个")
    
    # 建议
    if passed_count == 0:
        logger.warning("所有币种都未通过通道突破检查！")
        logger.warning("建议:")
        logger.warning("1. 降低 close_ratio_threshold 和 high_ratio_threshold")
        logger.warning("2. 增加 max_breakthrough_pct")
        logger.warning("3. 降低 min_volume_ratio")
        logger.warning("4. 增加 max_consecutive_near_upper")

if __name__ == '__main__':
    main()