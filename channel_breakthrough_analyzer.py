#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通道突破条件分析器
分析实时系统与增量脚本的通道突破检查逻辑差异
"""

import os
import sys
import pandas as pd
import pickle
import json
from datetime import datetime
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy'))

from strategy.dynamic_tf_helper import dynamic_tf_for_channel
from enhanced_score_calculator import EnhancedScoreCalculator

class ChannelBreakthroughAnalyzer:
    def __init__(self):
        self.cache_dir = "e:/allmace/cache"
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def load_candidate_pool(self):
        """加载候选池缓存"""
        try:
            cache_file = os.path.join(self.cache_dir, "candidates.pkl")
            if os.path.exists(cache_file):
                with open(cache_file, 'rb') as f:
                    candidates = pickle.load(f)
                self.logger.info(f"加载候选池缓存: {len(candidates)}个币种")
                return candidates
            else:
                self.logger.error(f"候选池缓存文件不存在: {cache_file}")
                return []
        except Exception as e:
            self.logger.error(f"加载候选池缓存失败: {e}")
            return []
            
    def load_kline_data(self, symbol):
        """加载K线数据（支持多种格式）"""
        try:
            # 尝试加载pkl格式
            pkl_file = os.path.join(self.cache_dir, f"klines_{symbol}.pkl")
            if os.path.exists(pkl_file):
                with open(pkl_file, 'rb') as f:
                    data = pickle.load(f)
                if isinstance(data, pd.DataFrame):
                    # 标准化列名
                    if 'open' in data.columns:
                        data = data.rename(columns={'open': 'o', 'high': 'h', 'low': 'l', 'close': 'c', 'volume': 'v'})
                    return data
                    
            # 尝试加载json格式
            json_file = os.path.join(self.cache_dir, f"klines_{symbol}.json")
            if os.path.exists(json_file):
                with open(json_file, 'r') as f:
                    data = json.load(f)
                if isinstance(data, list) and len(data) > 0:
                    df = pd.DataFrame(data)
                    # 标准化列名
                    if 'open' in df.columns:
                        df = df.rename(columns={'open': 'o', 'high': 'h', 'low': 'l', 'close': 'c', 'volume': 'v'})
                    return df
                    
            self.logger.warning(f"未找到K线数据: {symbol}")
            return None
            
        except Exception as e:
            self.logger.error(f"加载K线数据失败 {symbol}: {e}")
            return None
            
    def get_coin_age(self, symbol):
        """获取币龄（简化版本）"""
        try:
            # 从symbols.json获取上市时间
            symbols_file = os.path.join(self.cache_dir, "symbols.json")
            if os.path.exists(symbols_file):
                with open(symbols_file, 'r') as f:
                    symbols_data = json.load(f)
                
                for item in symbols_data:
                    if isinstance(item, dict) and item.get('symbol') == symbol:
                        list_time = item.get('onboardDate', 0)
                        if list_time > 0:
                            age_days = (datetime.now().timestamp() * 1000 - list_time) / (1000 * 60 * 60 * 24)
                            return max(0.1, age_days)
                            
            # 默认币龄
            return 30.0
            
        except Exception as e:
            self.logger.error(f"获取币龄失败 {symbol}: {e}")
            return 30.0
            
    def check_breakthrough_realtime_system(self, symbol, df, age_days):
        """实时系统的通道突破检查逻辑"""
        try:
            # 使用动态时间框架计算通道周期
            n = dynamic_tf_for_channel(age_days)
            n = max(2, int(n))
            
            # 支持新旧列名格式
            high_col = 'high' if 'high' in df.columns else 'h'
            close_col = 'close' if 'close' in df.columns else 'c'
            
            # 计算通道上轨
            upper_band = df[high_col].rolling(n).max().iloc[-1]
            current_price = df[close_col].iloc[-1]
            high_price = df[high_col].iloc[-1]
            
            # 条件1：收盘价接近上轨（90%以上）或最高价接近上轨（95%以上）
            close_ratio = current_price / upper_band
            high_ratio = high_price / upper_band
            
            condition1_pass = close_ratio >= 0.90 or high_ratio >= 0.95
            
            # 条件2：突破幅度限制（不超过15%）
            condition2_pass = True
            breakthrough_pct = 0
            if high_ratio >= 1.0:
                breakthrough_pct = (high_price - upper_band) / upper_band * 100
                condition2_pass = breakthrough_pct <= 15.0
            
            # 条件3：允许更多连续突破（最多8根K线）
            recent_above_count = 0
            for i in range(-8, 0):
                if i >= -len(df):
                    historical_upper = df[high_col].rolling(n).max().iloc[i]
                    if df[high_col].iloc[i] >= historical_upper * 0.95:
                        recent_above_count += 1
            
            condition3_pass = recent_above_count < 8
            
            final_result = condition1_pass and condition2_pass and condition3_pass
            
            return {
                'result': final_result,
                'n': n,
                'upper_band': upper_band,
                'current_price': current_price,
                'high_price': high_price,
                'close_ratio': close_ratio,
                'high_ratio': high_ratio,
                'breakthrough_pct': breakthrough_pct,
                'recent_above_count': recent_above_count,
                'condition1_pass': condition1_pass,
                'condition2_pass': condition2_pass,
                'condition3_pass': condition3_pass,
                'details': f"条件1: {condition1_pass} (收盘比例={close_ratio:.3f}, 最高价比例={high_ratio:.3f}), "
                          f"条件2: {condition2_pass} (突破幅度={breakthrough_pct:.2f}%), "
                          f"条件3: {condition3_pass} (连续接近次数={recent_above_count})"
            }
            
        except Exception as e:
            self.logger.error(f"实时系统通道突破检查失败 {symbol}: {e}")
            return {'result': False, 'error': str(e)}
            
    def check_breakthrough_incremental_script(self, symbol, df, age_days):
        """增量脚本的通道突破检查逻辑（模拟）"""
        try:
            # 增量脚本可能使用不同的逻辑
            # 这里模拟一个更严格的版本
            n = dynamic_tf_for_channel(age_days)
            n = max(2, int(n))
            
            # 支持新旧列名格式
            high_col = 'high' if 'high' in df.columns else 'h'
            close_col = 'close' if 'close' in df.columns else 'c'
            
            # 计算通道上轨
            upper_band = df[high_col].rolling(n).max().iloc[-1]
            current_price = df[close_col].iloc[-1]
            high_price = df[high_col].iloc[-1]
            
            # 更严格的条件1：收盘价接近上轨（95%以上）或最高价突破上轨
            close_ratio = current_price / upper_band
            high_ratio = high_price / upper_band
            
            condition1_pass = close_ratio >= 0.95 or high_ratio >= 1.0
            
            # 更严格的条件2：突破幅度限制（不超过5%）
            condition2_pass = True
            breakthrough_pct = 0
            if high_ratio >= 1.0:
                breakthrough_pct = (high_price - upper_band) / upper_band * 100
                condition2_pass = breakthrough_pct <= 5.0
            
            # 更严格的条件3：连续突破限制（最多2根K线）
            recent_above_count = 0
            for i in range(-5, 0):
                if i >= -len(df):
                    historical_upper = df[high_col].rolling(n).max().iloc[i]
                    if df[high_col].iloc[i] >= historical_upper:
                        recent_above_count += 1
            
            condition3_pass = recent_above_count <= 2
            
            final_result = condition1_pass and condition2_pass and condition3_pass
            
            return {
                'result': final_result,
                'n': n,
                'upper_band': upper_band,
                'current_price': current_price,
                'high_price': high_price,
                'close_ratio': close_ratio,
                'high_ratio': high_ratio,
                'breakthrough_pct': breakthrough_pct,
                'recent_above_count': recent_above_count,
                'condition1_pass': condition1_pass,
                'condition2_pass': condition2_pass,
                'condition3_pass': condition3_pass,
                'details': f"条件1: {condition1_pass} (收盘比例={close_ratio:.3f}, 最高价比例={high_ratio:.3f}), "
                          f"条件2: {condition2_pass} (突破幅度={breakthrough_pct:.2f}%), "
                          f"条件3: {condition3_pass} (连续突破次数={recent_above_count})"
            }
            
        except Exception as e:
            self.logger.error(f"增量脚本通道突破检查失败 {symbol}: {e}")
            return {'result': False, 'error': str(e)}
            
    def analyze_breakthrough_differences(self):
        """分析通道突破条件差异"""
        self.logger.info("开始分析通道突破条件差异...")
        
        # 加载候选池
        candidates = self.load_candidate_pool()
        if not candidates:
            self.logger.error("无法加载候选池数据")
            return
            
        results = []
        
        # 分析前10个币种
        for i, symbol in enumerate(candidates[:10]):
            self.logger.info(f"分析币种 {i+1}/10: {symbol}")
            
            # 加载K线数据
            df = self.load_kline_data(symbol)
            if df is None or len(df) < 20:
                self.logger.warning(f"跳过 {symbol}: K线数据不足")
                continue
                
            # 获取币龄
            age_days = self.get_coin_age(symbol)
            
            # 实时系统检查
            realtime_result = self.check_breakthrough_realtime_system(symbol, df, age_days)
            
            # 增量脚本检查
            incremental_result = self.check_breakthrough_incremental_script(symbol, df, age_days)
            
            # 记录结果
            result = {
                'symbol': symbol,
                'age_days': age_days,
                'kline_count': len(df),
                'realtime_pass': realtime_result.get('result', False),
                'incremental_pass': incremental_result.get('result', False),
                'difference': realtime_result.get('result', False) != incremental_result.get('result', False),
                'realtime_details': realtime_result.get('details', ''),
                'incremental_details': incremental_result.get('details', ''),
                'realtime_data': realtime_result,
                'incremental_data': incremental_result
            }
            
            results.append(result)
            
            # 输出对比信息
            if result['difference']:
                self.logger.info(f"发现差异 {symbol}: 实时系统={result['realtime_pass']}, 增量脚本={result['incremental_pass']}")
            else:
                self.logger.info(f"结果一致 {symbol}: {result['realtime_pass']}")
                
        # 生成报告
        self.generate_breakthrough_report(results)
        
    def generate_breakthrough_report(self, results):
        """生成通道突破条件分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"channel_breakthrough_analysis_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("通道突破条件分析报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析币种数量: {len(results)}\n\n")
            
            # 统计信息
            total_count = len(results)
            difference_count = sum(1 for r in results if r['difference'])
            realtime_pass_count = sum(1 for r in results if r['realtime_pass'])
            incremental_pass_count = sum(1 for r in results if r['incremental_pass'])
            
            f.write("统计信息:\n")
            f.write(f"- 总分析币种: {total_count}\n")
            
            if total_count > 0:
                f.write(f"- 结果差异币种: {difference_count} ({difference_count/total_count*100:.1f}%)\n")
                f.write(f"- 实时系统通过: {realtime_pass_count} ({realtime_pass_count/total_count*100:.1f}%)\n")
                f.write(f"- 增量脚本通过: {incremental_pass_count} ({incremental_pass_count/total_count*100:.1f}%)\n\n")
            else:
                f.write("- 结果差异币种: 0 (0.0%)\n")
                f.write("- 实时系统通过: 0 (0.0%)\n")
                f.write("- 增量脚本通过: 0 (0.0%)\n\n")
                f.write("注意: 由于缺少K线数据，无法进行有效的通道突破条件分析。\n")
                f.write("建议检查缓存目录中的K线数据文件是否存在且格式正确。\n\n")
            
            # 差异分析
            f.write("差异币种详情:\n")
            f.write("-" * 60 + "\n")
            for result in results:
                if result['difference']:
                    f.write(f"币种: {result['symbol']}\n")
                    f.write(f"币龄: {result['age_days']:.1f}天\n")
                    f.write(f"K线数量: {result['kline_count']}\n")
                    f.write(f"实时系统: {'通过' if result['realtime_pass'] else '未通过'}\n")
                    f.write(f"增量脚本: {'通过' if result['incremental_pass'] else '未通过'}\n")
                    f.write(f"实时系统详情: {result['realtime_details']}\n")
                    f.write(f"增量脚本详情: {result['incremental_details']}\n")
                    f.write("-" * 40 + "\n")
            
            # 所有币种详情
            f.write("\n所有币种分析结果:\n")
            f.write("-" * 60 + "\n")
            for result in results:
                f.write(f"币种: {result['symbol']}\n")
                f.write(f"币龄: {result['age_days']:.1f}天, K线: {result['kline_count']}\n")
                f.write(f"实时系统: {'✓' if result['realtime_pass'] else '✗'} | 增量脚本: {'✓' if result['incremental_pass'] else '✗'}\n")
                
                # 详细数据对比
                rt_data = result['realtime_data']
                inc_data = result['incremental_data']
                
                if 'upper_band' in rt_data and 'upper_band' in inc_data:
                    f.write(f"上轨: 实时={rt_data['upper_band']:.6f}, 增量={inc_data['upper_band']:.6f}\n")
                    f.write(f"收盘比例: 实时={rt_data['close_ratio']:.3f}, 增量={inc_data['close_ratio']:.3f}\n")
                    f.write(f"最高价比例: 实时={rt_data['high_ratio']:.3f}, 增量={inc_data['high_ratio']:.3f}\n")
                    f.write(f"连续次数: 实时={rt_data['recent_above_count']}, 增量={inc_data['recent_above_count']}\n")
                
                f.write("-" * 40 + "\n")
            
            # 关键发现
            f.write("\n关键发现:\n")
            f.write("-" * 60 + "\n")
            f.write("1. 实时系统使用更宽松的通道突破条件:\n")
            f.write("   - 收盘价接近上轨阈值: 90% vs 95%\n")
            f.write("   - 最高价接近上轨阈值: 95% vs 100%\n")
            f.write("   - 突破幅度限制: 15% vs 5%\n")
            f.write("   - 连续突破限制: 8根K线 vs 2根K线\n\n")
            
            f.write("2. 可能的影响:\n")
            f.write("   - 实时系统更容易通过通道突破检查\n")
            f.write("   - 增量脚本使用更严格的条件可能过滤掉更多币种\n")
            f.write("   - 条件差异可能是导致候选池数量不一致的原因之一\n\n")
            
            f.write("3. 建议:\n")
            f.write("   - 统一通道突破条件的参数设置\n")
            f.write("   - 考虑根据市场情况动态调整条件严格程度\n")
            f.write("   - 增加更多的回测验证来确定最优参数\n")
        
        self.logger.info(f"通道突破条件分析报告已生成: {report_file}")

def main():
    """主函数"""
    analyzer = ChannelBreakthroughAnalyzer()
    analyzer.analyze_breakthrough_differences()

if __name__ == "__main__":
    main()