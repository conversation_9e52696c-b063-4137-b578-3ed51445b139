#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
sys.path.append('.')
from http_client import HttpClient

def test_kline_api():
    """测试K线数据API调用"""
    
    # 从配置文件读取API密钥
    try:
        with open('config/api_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f'无法读取配置文件: {e}')
        return
    
    # 创建HTTP客户端
    client = HttpClient(
        api_key=config.get('api_key', ''),
        api_secret=config.get('api_secret', ''),
        base_url='https://fapi.binance.com'
    )
    
    # 测试不同的参数组合
    test_cases = [
        {'symbol': 'BCHUSDT', 'interval': '15m', 'limit': 200},
        {'symbol': 'BCHUSDT', 'interval': '15m', 'limit': 20},
        {'symbol': 'FLOWUSDT', 'interval': '15m', 'limit': 200},
        {'symbol': 'FLOWUSDT', 'interval': '15m', 'limit': 20},
    ]
    
    for i, params in enumerate(test_cases, 1):
        print(f'\n=== 测试用例 {i}: {params} ===')
        
        result = client.get('/fapi/v1/klines', params)
        
        if isinstance(result, list):
            print(f'✓ 成功获取K线数据，条数: {len(result)}')
            if len(result) > 0:
                print(f'  第一条时间戳: {result[0][0]}')
                print(f'  最后一条时间戳: {result[-1][0]}')
            else:
                print('  ⚠️ 返回数据为空')
        elif isinstance(result, dict):
            if 'error' in result:
                print(f'✗ 获取失败: {result["error"]}')
            else:
                print(f'? 未知响应格式: {result}')
        else:
            print(f'? 未知响应类型: {type(result)}, {result}')

if __name__ == '__main__':
    test_kline_api()