from http_client import HttpClient
import logging
import time
import queue
from threading import Lock
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ApiRateLimiter:
    """API调用限流器，使用令牌桶算法"""
    def __init__(self, max_rate=15, max_burst=30):
        """
        初始化限流器
        :param max_rate: 最大每秒调用次数
        :param max_burst: 最大突发量
        """
        self.max_rate = max_rate  # 最大每秒请求数
        self.max_burst = max_burst  # 最大令牌数
        self.tokens = max_burst  # 当前令牌数
        self.last_refill_time = time.time()  # 上次填充时间
        self.lock = Lock()  # 线程锁，保证线程安全
        
    def _refill(self):
        """补充令牌"""
        current_time = time.time()
        elapsed = current_time - self.last_refill_time
        
        # 计算应补充的令牌数
        new_tokens = elapsed * self.max_rate
        if new_tokens > 0:
            self.tokens = min(self.max_burst, self.tokens + new_tokens)
            self.last_refill_time = current_time
    
    def acquire(self, count=1):
        """获取令牌，阻塞直到成功"""
        with self.lock:
            while True:
                self._refill()
                if self.tokens >= count:
                    self.tokens -= count
                    return
                # 计算需要等待的时间
                wait_time = (count - self.tokens) / self.max_rate
                time.sleep(min(wait_time, 0.5))  # 最多等待0.5秒

class OrderOperationQueue:
    """订单操作队列，确保订单操作顺序执行且频率可控"""
    def __init__(self, limiter):
        """
        初始化订单操作队列
        :param limiter: API限流器实例
        """
        self.limiter = limiter
        self.queue = queue.Queue()
        self.running = False
        self.lock = Lock()
        self.logger = logging.getLogger('OrderOperationQueue')
        self.thread = None  # 初始化线程属性
        
    def start(self):
        """启动订单操作处理线程"""
        if not self.running:
            self.running = True
            import threading
            self.thread = threading.Thread(target=self._process_queue, daemon=True)
            self.thread.start()
            self.logger.info("订单操作队列已启动")
    
    def stop(self):
        """停止订单操作处理线程"""
        self.running = False
        if hasattr(self, 'thread') and self.thread.is_alive():
            self.thread.join(timeout=2.0)
            self.logger.info("订单操作队列已停止")
    
    def add_operation(self, operation, callback=None, retry_count=3, retry_delay=2):
        """
        添加订单操作到队列
        :param operation: 要执行的操作函数
        :param callback: 操作完成后的回调函数
        :param retry_count: 最大重试次数
        :param retry_delay: 重试间隔（秒）
        """
        self.queue.put((operation, callback, retry_count, retry_delay))
    
    def _process_queue(self):
        """处理订单操作队列"""
        while self.running:
            try:
                # 非阻塞获取队列中的操作，超时1秒
                try:
                    operation, callback, retry_count, retry_delay = self.queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                result = None
                success = False
                attempts = 0
                
                # 尝试执行操作，支持重试
                while attempts < retry_count and not success:
                    attempts += 1
                    try:
                        # 获取API限流令牌
                        self.limiter.acquire()
                        
                        # 执行操作
                        result = operation()
                        success = True
                        self.logger.debug(f"操作执行成功，尝试次数: {attempts}")
                    except Exception as e:
                        self.logger.error(f"操作执行失败 (尝试 {attempts}/{retry_count}): {str(e)}")
                        if attempts < retry_count:
                            self.logger.info(f"{retry_delay}秒后重试...")
                            time.sleep(retry_delay)
                        else:
                            self.logger.error(f"操作执行失败，已达到最大重试次数")
                
                # 执行回调
                if callback:
                    try:
                        callback(success, result)
                    except Exception as e:
                        self.logger.error(f"回调函数执行失败: {str(e)}")
                
                # 标记任务完成
                self.queue.task_done()
                
            except Exception as e:
                self.logger.error(f"队列处理线程发生异常: {str(e)}")

class BinanceTrader:
    def __init__(self, config, strategy_instance=None):
        # 添加日志以调试API密钥
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 保存策略实例引用
        self.strategy_instance = strategy_instance
        
        # 安全地获取API密钥和密钥
        try:
            api_key = config['network_params']['api_key']
            api_secret = config['network_params']['api_secret']
            self.logger.info(f"API Key length: {len(api_key)}")
            self.logger.info(f"API Secret length: {len(api_secret)}")
        except KeyError as e:
            self.logger.error(f"配置文件中缺少必要的API密钥信息: {e}")
            raise
        
        proxy_cfg = config.get('network_params', {}).get('proxy')
        self.http = HttpClient(api_key, api_secret, proxy_cfg=proxy_cfg, verify_ssl=False)
        self.symbols = self._load_symbols()
        self.logger = logging.getLogger(__name__)
        
        # 保存配置
        self.config = config
        # 处理杠杆配置，兼容旧版整数格式和新版字典格式
        leverage_config = config.get('leverage', 3)
        if isinstance(leverage_config, int):
            self.leverage_config = {
                'mode': 'isolated',
                'target_leverage': leverage_config,
                'cap_at_max': True
            }
        else:
            self.leverage_config = leverage_config
        
        # 初始化API限流器（默认15 req/sec，兼容Binance标准）
        self.query_limiter = ApiRateLimiter(max_rate=15, max_burst=30)  # 查询类API限流器
        self.trade_limiter = ApiRateLimiter(max_rate=10, max_burst=20)  # 交易类API限流器
        
        # 初始化订单操作队列
        self.order_queue = OrderOperationQueue(self.trade_limiter)
        self.order_queue.start()
        self.logger.info("BinanceTrader初始化完成，API限流器和订单操作队列已启动")
        
        # 订单缓存，避免重复查询
        self.order_cache = {}
        self.cache_expiry = 5  # 缓存有效期(秒)
        self.last_cache_update = 0

    def _load_symbols(self):
        """加载并过滤有效的交易对"""
        ex = self.http.get('/fapi/v1/exchangeInfo')
        
        # 检查API调用是否成功
        if not ex or 'symbols' not in ex:
            self.logger.error(f"获取交易对信息失败: {ex}")
            return {}
        
        valid_symbols = {}
        for s in ex['symbols']:
            # 只选择USDT交易对
            if not isinstance(s, dict) or not s.get('symbol') or not s['symbol'].endswith('USDT'):
                continue
            # 只选择永续合约
            if s.get('contractType') != 'PERPETUAL':
                continue
            # 只选择可以交易的币对
            if s.get('status') != 'TRADING':
                continue
            
            # 提取价格和数量精度信息
            filters = s.get('filters', [])
            price_filter = next((f for f in filters if f.get('filterType') == 'PRICE_FILTER'), {})
            lot_filter = next((f for f in filters if f.get('filterType') == 'LOT_SIZE'), {})
            
            # 获取最小交易金额限制
            min_notional_filter = next((f for f in filters if f.get('filterType') == 'MIN_NOTIONAL'), {})
            
            # 如果没有最小交易金额限制，默认为5 USDT
            min_notional = float(min_notional_filter.get('notional', 5.0)) if min_notional_filter else 5.0
            
            valid_symbols[s['symbol']] = {
                'symbol': s['symbol'],
                'status': s['status'],
                'contractType': s['contractType'],
                'baseAsset': s['baseAsset'],
                'quoteAsset': s['quoteAsset'],
                'tick_size': float(price_filter.get('tickSize', 0.01)),
                'step_size': float(lot_filter.get('stepSize', 0.001)),
                'min_qty': float(lot_filter.get('minQty', 0.001)),
                'max_qty': float(lot_filter.get('maxQty', 1000000)),
                'min_notional': min_notional
            }
        
        self.logger.info(f"加载了{len(valid_symbols)}个有效的永续合约交易对")
        return valid_symbols

    def get_ticker(self, symbol):
        # 检查交易对是否有效
        if symbol not in self.symbols:
            self.logger.warning(f"跳过无效交易对: {symbol}")
            return None
        return self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})

    def get_symbol_ticker(self, symbol):
        """获取单个交易对的最新价格信息"""
        # 检查交易对是否有效
        if symbol not in self.symbols:
            self.logger.warning(f"跳过无效交易对: {symbol}")
            return None
            
        try:
            ticker = self.http.get('/fapi/v1/ticker/price', {'symbol': symbol})
            if isinstance(ticker, dict) and 'price' in ticker:
                return ticker
            else:
                self.logger.warning(f"获取{symbol}价格失败: {ticker}")
                return None
        except Exception as e:
            self.logger.error(f"获取{symbol}价格异常: {str(e)}")
            return None

    def get_klines(self, symbol, interval='5m', limit=48):
        """获取K线数据，包含错误处理"""
        try:
            # 检查交易对是否有效
            if symbol not in self.symbols:
                self.logger.warning(f"跳过无效交易对: {symbol}")
                return []
                
            klines = self.http.get('/fapi/v1/klines', {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            })
            
            if isinstance(klines, list):
                return klines
            else:
                self.logger.warning(f"获取K线失败: {klines}")
                return []
                
        except Exception as e:
            self.logger.error(f"获取K线异常: {e}")
            return []
            
    def get_position(self, symbol, use_cache=True):
        """
        获取单个交易对的持仓（使用查询限流器）
        
        :param symbol: 交易对
        :param use_cache: 是否使用缓存
        :return: 仓位详情
        """
        # 检查交易对是否有效
        if symbol not in self.symbols:
            self.logger.warning(f"跳过无效交易对: {symbol}")
            return None
            
        # 创建查询函数
        def _get_position_operation():
            position = self.http.get('/fapi/v2/positionRisk', {'symbol': symbol})
            if isinstance(position, list) and len(position) > 0:
                return position[0]
            return None
        
        try:
            # 获取查询限流器令牌
            self.query_limiter.acquire()
            return _get_position_operation()
        except Exception as e:
            self.logger.error(f"获取持仓失败: {str(e)}")
            return None

    def get_all_positions(self):
        """获取所有持仓，返回字典格式"""
        try:
            raw = self.http.get('/fapi/v2/positionRisk')
            if isinstance(raw, list):
                return {p['symbol']: p for p in raw if float(p['positionAmt']) != 0}
            else:
                # 如果不是列表，可能是错误信息
                self.logger.warning(f"Unexpected positionRisk response: {raw}")
                return {}
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return {}

    def get_positions(self):
        """获取所有持仓，返回列表格式（为兼容旧接口）"""
        try:
            raw = self.http.get('/fapi/v2/positionRisk')
            if isinstance(raw, list):
                # 只返回有持仓的仓位
                return [p for p in raw if float(p['positionAmt']) != 0]
            else:
                # 如果不是列表，可能是错误信息
                self.logger.warning(f"Unexpected positionRisk response: {raw}")
                return []
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []

    def get_all_futures(self):
        return list(self.symbols.keys())

    def set_symbol_leverage(self, symbol, target_leverage=None):
        """设置杠杆，包括检查和设置保证金模式"""
        # 检查交易对是否有效
        if symbol not in self.symbols:
            self.logger.warning(f"跳过无效交易对: {symbol}")
            return False
            
        try:
            # 如果没有指定目标杠杆，使用配置中的值
            if target_leverage is None:
                target_leverage = self.leverage_config['target_leverage']
            
            target_leverage = int(target_leverage)
            
            # ① 检查当前保证金模式，只有不是逐仓时才设置
            current_margin = self.http.get_margin_type(symbol)
            if current_margin != 'ISOLATED':
                self.logger.info(f"为{symbol}设置逐仓模式...")
                result = self.http.set_margin_type(symbol)
                if result and result.get('error'):
                    # 检查是否是"无需更改"的错误
                    error_msg = str(result.get('error', ''))
                    if "No need to change margin type" in error_msg:
                        self.logger.info(f"{symbol} 已经是逐仓模式，无需更改")
                    else:
                        self.logger.error(f"设置保证金模式失败: {result['error']}")
                        return False
            else:
                self.logger.debug(f"{symbol} 已经是逐仓模式，跳过设置")
            
            # ② 检查当前杠杆倍数，只有不匹配时才设置
            current_leverage = self.http.get_leverage(symbol)
            if current_leverage != target_leverage:
                self.logger.info(f"为{symbol}设置{target_leverage}倍杠杆（当前{current_leverage}倍）...")
                result = self.http.set_leverage(symbol, target_leverage)
                if result and result.get('error'):
                    # 检查是否是"无需更改"的错误
                    error_msg = str(result.get('error', ''))
                    if "No need to change" in error_msg:
                        self.logger.info(f"{symbol} 杠杆已正确设置为{target_leverage}倍，无需更改")
                        return True
                    else:
                        self.logger.error(f"设置杠杆倍数失败: {result['error']}")
                        return False
                else:
                    self.logger.info(f"成功设置{symbol}杠杆为{target_leverage}倍")
            else:
                self.logger.debug(f"{symbol} 杠杆已经是{target_leverage}倍，跳过设置")
                
            return True
            
        except Exception as e:
            self.logger.error(f"设置杠杆时发生错误: {str(e)}")
            return False

    def set_leverage(self, symbol, target_leverage=None):
        """兼容旧版本的set_leverage方法"""
        return self.set_symbol_leverage(symbol, target_leverage)

    def place_order(self, symbol, side, order_type, quantity, price=None, 
                   stop_price=None, close_position=None, reduce_only=None, timeInForce=None, 
                   new_client_order_id=None, callback=None, max_retries=3):
        """下单（线程安全，使用订单队列）
        
        :param symbol: 交易对
        :param side: 交易方向（BUY/SELL）
        :param order_type: 订单类型（MARKET/LIMIT）
        :param quantity: 数量
        :param price: 价格（限价单需要）
        :param stop_price: 止损价格
        :param close_position: 是否全平
        :param reduce_only: 是否仅减仓
        :param timeInForce: 有效时间
        :param new_client_order_id: 客户端订单ID
        :param callback: 操作完成后的回调函数
        :param max_retries: 最大重试次数
        :return: 订单ID（如果异步）或订单详情（如果同步）
        """
        # 不再直接跳过无效交易对，而是记录警告后继续尝试下单
        if symbol not in self.symbols:
            self.logger.warning(f"交易对 {symbol} 不在有效列表中，但仍尝试下单")
            
        # 准备订单参数
        params = {
            'symbol': symbol,
            'side': side,
            'type': order_type,
            'quantity': quantity
        }
        
        # 添加可选参数
        if price is not None:
            params['price'] = price
        if stop_price is not None:
            params['stopPrice'] = stop_price
        if close_position is not None:
            params['closePosition'] = close_position
        if reduce_only is not None:
            params['reduceOnly'] = reduce_only
        if timeInForce is not None and timeInForce != '':
            params['timeInForce'] = timeInForce
        if new_client_order_id is not None:
            params['newClientOrderId'] = new_client_order_id
        
        # 创建操作函数
        def _place_order_operation():
            # 检查并调整数量精度
            if symbol in self.symbols:
                symbol_info = self.symbols[symbol]
                params['quantity'] = self._round_quantity(params['quantity'], symbol_info)
                
                # 如果有价格，也调整价格精度
                if params.get('price') is not None:
                    params['price'] = self._round_price(params['price'], symbol_info)
                
                # 如果有止损价格，也调整精度
                if params.get('stopPrice') is not None:
                    params['stopPrice'] = self._round_price(params['stopPrice'], symbol_info)
            
            # 指数退避重试机制
            retry_delay = 1  # 初始延迟1秒
            for attempt in range(max_retries):
                try:
                    # 执行下单
                    result = self.http.post('/fapi/v1/order', params)
                    
                    # 检查是否有错误
                    if result.get('error'):
                        error_msg = str(result.get('error', ''))
                        if 'Too many requests' in error_msg or 'API rate limit' in error_msg:
                            wait_time = min(retry_delay * (2 ** attempt), 30)  # 最大等待30秒
                            self.logger.warning(f"API限速触发，等待{wait_time}秒后重试 (尝试 {attempt+1}/{max_retries})")
                            time.sleep(wait_time)
                            continue
                        else:
                            self.logger.error(f"下单失败: {result['error']}")
                            raise Exception(result['error'])
                    
                    # 检查是否包含订单ID，确认订单真正成功
                    if 'orderId' in result:
                        self.logger.info(f"下单成功: 订单ID {result['orderId']}")
                        return result
                    else:
                        self.logger.error(f"下单响应异常: {result}")
                        raise Exception(f"下单响应异常: {result}")
                        
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    wait_time = min(retry_delay * (2 ** attempt), 30)  # 最大等待30秒
                    self.logger.warning(f"下单异常，等待{wait_time}秒后重试 (尝试 {attempt+1}/{max_retries}): {str(e)}")
                    time.sleep(wait_time)
        
        # 如果提供了回调，使用异步方式
        if callback:
            self.order_queue.add_operation(
                operation=_place_order_operation,
                callback=callback,
                retry_count=3,
                retry_delay=2
            )
            return None  # 异步模式下不返回具体结果
        else:
            # 同步模式，直接执行
            try:
                # 获取令牌
                self.trade_limiter.acquire()
                return _place_order_operation()
            except Exception as e:
                self.logger.error(f"下单异常: {str(e)}")
                return None

    def _round_price(self, price, symbol_info):
        """根据交易对信息调整价格精度"""
        try:
            # 使用更精确的精度调整方法
            tick_size = symbol_info.get('tick_size', 0.01)
            # 使用floor division来确保不超过tick_size的整数倍
            import decimal
            decimal.getcontext().prec = 28
            price_decimal = decimal.Decimal(str(price))
            tick_decimal = decimal.Decimal(str(tick_size))
            rounded_price = float((price_decimal // tick_decimal) * tick_decimal)
            
            self.logger.debug(f"价格精度调整: {price} -> {rounded_price} (tick_size: {tick_size})")
            return rounded_price
            
        except Exception as e:
            self.logger.error(f"调整价格精度时出错: {e}")
            return price

    def _round_quantity(self, quantity, symbol_info):
        """根据交易对信息调整数量精度"""
        try:
            # 使用更精确的精度调整方法
            step_size = symbol_info.get('step_size', 0.001)
            # 使用floor division来确保不超过step_size的整数倍
            import decimal
            decimal.getcontext().prec = 28
            qty_decimal = decimal.Decimal(str(quantity))
            step_decimal = decimal.Decimal(str(step_size))
            rounded_qty = float((qty_decimal // step_decimal) * step_decimal)
            
            # 确保不低于最小数量
            min_qty = symbol_info.get('min_qty', 0.001)
            if rounded_qty < min_qty:
                self.logger.warning(f"调整后数量 {rounded_qty} 低于最小数量 {min_qty}，将使用最小数量")
                rounded_qty = min_qty
            
            self.logger.debug(f"数量精度调整: {quantity} -> {rounded_qty} (step_size: {step_size})")
            return rounded_qty
            
        except Exception as e:
            self.logger.error(f"调整数量精度时出错: {e}")
            return quantity

    def get_balance(self, use_cache=True):
        """
        获取账户余额（使用查询限流器）
        
        :param use_cache: 是否使用缓存
        :return: 余额详情
        """
        # 创建查询函数
        def _get_balance_operation():
            balance = self.http.get('/fapi/v2/balance')
            if balance:
                return balance
            return None
        
        try:
            # 获取查询限流器令牌
            self.query_limiter.acquire()
            return _get_balance_operation()
        except Exception as e:
            self.logger.error(f"获取余额失败: {str(e)}")
            return None
            
    def get_total_balance(self):
        """获取总余额"""
        try:
            account = self.get_balance()
            if isinstance(account, list):
                for asset in account:
                    if asset['asset'] == 'USDT':
                        return float(asset['balance'])
            return 0
        except Exception as e:
            self.logger.error(f"获取余额异常: {str(e)}")
            return 0
            
    def get_order(self, symbol, order_id, use_cache=True, auto_cancel_timeout=30):
        """
        获取订单状态（使用查询限流器和缓存）
        
        :param symbol: 交易对
        :param order_id: 订单ID
        :param use_cache: 是否使用缓存
        :param auto_cancel_timeout: 自动取消超时(秒)，0表示不自动取消
        :return: 订单详情
        """
        # 不再直接跳过无效交易对，而是记录警告后继续尝试查询
        if symbol not in self.symbols:
            self.logger.warning(f"交易对 {symbol} 不在有效列表中，但仍尝试查询订单")
            
        # 检查缓存
        cache_key = f"{symbol}_{order_id}"
        current_time = time.time()
        
        if use_cache and cache_key in self.order_cache and current_time - self.last_cache_update < self.cache_expiry:
            return self.order_cache[cache_key]
        
        # 创建查询函数
        def _get_order_operation():
            params = {
                'symbol': symbol,
                'orderId': order_id
            }
            
            response = self.http.get('/fapi/v1/order', params)
            
            # 更新缓存
            if response:
                self.order_cache[cache_key] = response
                self.last_cache_update = current_time
                
                # 自动取消超时未成交的订单
                if auto_cancel_timeout > 0 and response.get('status') == 'NEW':
                    order_time = response.get('time', 0) / 1000  # 转换为秒
                    if current_time - order_time > auto_cancel_timeout:
                        self.logger.warning(f"订单 {order_id} 超时未成交，自动取消")
                        self.cancel_order(symbol, order_id)
                        return None
            
            return response
        
        try:
            # 获取查询限流器令牌
            self.query_limiter.acquire()
            return _get_order_operation()
        except Exception as e:
            self.logger.error(f"获取订单状态失败: {str(e)}")
            # 如果有缓存，返回缓存数据
            if use_cache and cache_key in self.order_cache:
                self.logger.warning(f"返回缓存的订单数据")
                return self.order_cache[cache_key]
            return None
            
    def cancel_order(self, symbol, order_id, callback=None):
        """
        取消订单（线程安全，使用订单队列）
        
        :param symbol: 交易对
        :param order_id: 订单ID
        :param callback: 操作完成后的回调函数
        :return: 订单ID（如果异步）或订单详情（如果同步）
        """
        # 不再直接跳过无效交易对，而是记录警告后继续尝试取消订单
        if symbol not in self.symbols:
            self.logger.warning(f"交易对 {symbol} 不在有效列表中，但仍尝试取消订单")
            
        # 创建操作函数
        def _cancel_order_operation():
            params = {
                'symbol': symbol,
                'orderId': order_id
            }
            
            response = self.http.delete('/fapi/v1/order', params)
            if response and 'orderId' in response:
                self.logger.info(f"取消订单成功: {symbol} 订单ID {order_id}")
                return response
            else:
                self.logger.error(f"取消订单失败: {response}")
                raise Exception(f"取消订单失败: {response}")
        
        # 如果提供了回调，使用异步方式
        if callback:
            self.order_queue.add_operation(
                operation=_cancel_order_operation,
                callback=callback,
                retry_count=3,
                retry_delay=2
            )
            return None  # 异步模式下不返回具体结果
        else:
            # 同步模式，直接执行
            try:
                # 获取令牌
                self.trade_limiter.acquire()
                return _cancel_order_operation()
            except Exception as e:
                self.logger.error(f"取消订单失败: {str(e)}")
                return None
            
    def open_position(self, symbol, side, size, price=None, timeInForce=None, force=False):
        """开仓
        Args:
            symbol: 交易对
            side: 方向,'BUY'或'SELL'
            size: 数量
            price: 价格,不指定则为市价单
            timeInForce: GTC、IOC、FOK等,限价单必须
            force: 是否强制开仓，即使交易对不在有效列表中
        Returns:
            bool: 开仓是否成功
        """
        # 检查交易对是否有效，除非force=True
        if symbol not in self.symbols and not force:
            self.logger.warning(f"跳过无效交易对: {symbol}")
            return False
        elif symbol not in self.symbols and force:
            self.logger.warning(f"交易对 {symbol} 不在有效列表中，但强制尝试开仓")
            
        try:
            # 设置杠杆，除非是强制开仓且交易对不在有效列表中
            if not (force and symbol not in self.symbols):
                if not self.set_symbol_leverage(symbol):
                    return False
                
            # 确定订单类型
            order_type = 'LIMIT' if price else 'MARKET'
            
            # 准备订单参数
            order_params = {
                'symbol': symbol,
                'side': side, 
                'order_type': order_type,
                'quantity': size,
                'reduce_only': False
            }
            
            # 只在限价单时添加price和timeInForce参数
            if order_type == 'LIMIT':
                order_params['price'] = price
                if timeInForce is None:
                    timeInForce = 'GTC'
                order_params['timeInForce'] = timeInForce
            # 对于市价单，不传递price和timeInForce参数
                
            # 下单    
            order = self.place_order(**order_params)
            
            if order and 'orderId' in order:
                self.logger.info(f"开仓成功: {symbol} {side} {size}, 订单ID: {order['orderId']}")
                return order  # 返回订单对象而不是True
            elif order:
                self.logger.error(f"开仓失败，订单响应异常: {order}")
                return False
            else:
                self.logger.error(f"开仓失败: {symbol} {side} {size}")
                return False
                
        except Exception as e:
            self.logger.error(f"开仓异常: {str(e)}")
            return False

    def place_stop_loss_order(self, symbol, quantity, stop_price, side=None):
        """下止损单
        Args:
            symbol: 交易对
            quantity: 数量
            stop_price: 止损价格
            side: 方向，如果为None则根据持仓自动判断
        Returns:
            bool: 止损单是否成功
        """
        # 不再直接跳过无效交易对，而是记录警告后继续尝试下单
        if symbol not in self.symbols:
            self.logger.warning(f"交易对 {symbol} 不在有效列表中，但仍尝试下止损单")
            
        try:
            # 如果没有指定side，根据持仓自动判断
            if side is None:
                position = self.get_position(symbol)
                if not position or float(position['positionAmt']) == 0:
                    self.logger.error(f"无法下止损单：{symbol} 没有持仓")
                    return False
                
                position_amt = float(position['positionAmt'])
                if position_amt > 0:
                    side = 'SELL'  # 多头止损
                else:
                    side = 'BUY'   # 空头止损
                    quantity = abs(quantity)  # 确保数量为正值
            
            # 调整数量和价格精度
            if symbol in self.symbols:
                symbol_info = self.symbols[symbol]
                quantity = self._round_quantity(quantity, symbol_info)
                stop_price = self._round_price(stop_price, symbol_info)
            
            params = {
                'symbol': symbol,
                'side': side,
                'type': 'STOP_MARKET',
                'quantity': quantity,
                'stopPrice': stop_price,
                'reduceOnly': True
            }
            
            result = self.http.post('/fapi/v1/order', params)
            
            if result and 'orderId' in result:
                self.logger.info(f"止损单成功: {symbol} {side} {quantity} @ {stop_price}")
                return True
            else:
                self.logger.error(f"止损单失败: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"下止损单异常: {str(e)}")
            return False
    
    def place_take_profit_order(self, symbol, quantity, take_price, side=None):
        """下止盈单
        Args:
            symbol: 交易对
            quantity: 数量
            take_price: 止盈价格
            side: 方向，如果为None则根据持仓自动判断
        """
        # 不再直接跳过无效交易对，而是记录警告后继续尝试下单
        if symbol not in self.symbols:
            self.logger.warning(f"交易对 {symbol} 不在有效列表中，但仍尝试下止盈单")
            
        try:
            # 如果没有指定side，根据持仓自动判断
            if side is None:
                position = self.get_position(symbol)
                if not position or float(position['positionAmt']) == 0:
                    self.logger.error(f"无法下止盈单：{symbol} 没有持仓")
                    return None
                
                position_amt = float(position['positionAmt'])
                if position_amt > 0:
                    side = 'SELL'  # 多头止盈
                else:
                    side = 'BUY'   # 空头止盈
                    quantity = abs(quantity)  # 确保数量为正值
            
            # 调整数量和价格精度
            if symbol in self.symbols:
                symbol_info = self.symbols[symbol]
                quantity = self._round_quantity(quantity, symbol_info)
                take_price = self._round_price(take_price, symbol_info)
            
            params = {
                'symbol': symbol,
                'side': side,
                'type': 'TAKE_PROFIT_MARKET',
                'quantity': quantity,
                'stopPrice': take_price,
                'reduceOnly': True
            }
            
            result = self.http.post('/fapi/v1/order', params)
            
            if result and 'orderId' in result:
                self.logger.info(f"止盈单成功: {symbol} {side} {quantity} @ {take_price}")
                return result
            else:
                self.logger.error(f"止盈单失败: {result}")
                return None
                
        except Exception as e:
            self.logger.error(f"下止盈单异常: {str(e)}")
            return None

    def cancel_order_by_client_id(self, symbol, client_order_id):
        """根据客户端订单ID取消订单"""
        # 不再直接跳过无效交易对，而是记录警告后继续尝试取消订单
        if symbol not in self.symbols:
            self.logger.warning(f"交易对 {symbol} 不在有效列表中，但仍尝试根据客户端订单ID取消订单")
            
        try:
            params = {
                'symbol': symbol,
                'origClientOrderId': client_order_id
            }
            
            response = self.http.delete('/fapi/v1/order', params)
            if response and 'orderId' in response:
                self.logger.info(f"取消订单成功: {symbol} 客户端订单ID {client_order_id}")
                return response
            else:
                self.logger.error(f"取消订单失败: {response}")
                return None
        except Exception as e:
            self.logger.error(f"取消订单失败: {str(e)}")
            return None
