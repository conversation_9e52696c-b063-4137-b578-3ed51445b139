#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略版本管理系统
提供统一的版本管理、备份、回滚接口
"""

import os
import json
import datetime
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from backup import BackupManager
from rollback import RollbackManager

class VersionManager:
    """版本管理器 - 统一管理备份和回滚"""
    
    def __init__(self, source_dir: str = "e:/allmace", backup_dir: str = "e:/allmace/backup"):
        """
        初始化版本管理器
        
        Args:
            source_dir: 源代码目录
            backup_dir: 备份目录
        """
        self.source_dir = Path(source_dir)
        self.backup_dir = Path(backup_dir)
        
        # 初始化子管理器
        self.backup_manager = BackupManager(source_dir, backup_dir)
        self.rollback_manager = RollbackManager(source_dir, backup_dir)
        
        # 配置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志配置"""
        logs_dir = self.backup_dir / "logs"
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = logs_dir / "version_manager.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_version(self, version: Optional[str] = None, description: str = "", backup_type: str = "manual") -> Optional[str]:
        """
        创建新版本
        
        Args:
            version: 版本号（可选，自动生成）
            description: 版本描述
            backup_type: 备份类型
            
        Returns:
            版本名称，失败返回None
        """
        try:
            self.logger.info(f"创建新版本: {version or '自动生成'}")
            
            version_name = self.backup_manager.create_backup(
                version=version,
                backup_type=backup_type,
                description=description
            )
            
            if version_name:
                self.logger.info(f"版本创建成功: {version_name}")
                return version_name
            else:
                self.logger.error("版本创建失败")
                return None
                
        except Exception as e:
            self.logger.error(f"创建版本失败: {e}")
            return None
    
    def list_versions(self, limit: int = 20) -> List[Dict]:
        """
        列出版本列表
        
        Args:
            limit: 返回数量限制
            
        Returns:
            版本信息列表
        """
        try:
            versions = self.rollback_manager.list_available_versions()
            return versions[:limit] if versions else []
            
        except Exception as e:
            self.logger.error(f"列出版本失败: {e}")
            return []
    
    def get_version_info(self, version_name: str) -> Optional[Dict]:
        """
        获取版本详细信息
        
        Args:
            version_name: 版本名称
            
        Returns:
            版本信息，不存在返回None
        """
        try:
            versions = self.list_versions()
            for version in versions:
                if version["version_name"] == version_name:
                    return version
            return None
            
        except Exception as e:
            self.logger.error(f"获取版本信息失败: {e}")
            return None
    
    def rollback_to_version(self, version_name: str, create_snapshot: bool = True) -> bool:
        """
        回滚到指定版本
        
        Args:
            version_name: 目标版本名称
            create_snapshot: 是否创建回滚前快照
            
        Returns:
            是否回滚成功
        """
        try:
            self.logger.info(f"回滚到版本: {version_name}")
            
            # 验证版本存在
            version_info = self.get_version_info(version_name)
            if not version_info:
                self.logger.error(f"版本不存在: {version_name}")
                return False
            
            # 执行回滚
            success = self.rollback_manager.rollback_to_version(version_name, create_snapshot)
            
            if success:
                self.logger.info(f"回滚成功: {version_name}")
            else:
                self.logger.error(f"回滚失败: {version_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"回滚失败: {e}")
            return False
    
    def verify_version(self, version_name: str) -> bool:
        """
        验证版本完整性
        
        Args:
            version_name: 版本名称
            
        Returns:
            是否验证通过
        """
        try:
            versions_dir = self.backup_dir / "versions"
            backup_path = versions_dir / version_name
            
            return self.rollback_manager.verify_backup_integrity(backup_path)
            
        except Exception as e:
            self.logger.error(f"验证版本失败: {e}")
            return False
    
    def delete_version(self, version_name: str, force: bool = False) -> bool:
        """
        删除版本
        
        Args:
            version_name: 版本名称
            force: 是否强制删除
            
        Returns:
            是否删除成功
        """
        try:
            self.logger.info(f"删除版本: {version_name}")
            
            versions_dir = self.backup_dir / "versions"
            backup_path = versions_dir / version_name
            
            if not backup_path.exists():
                self.logger.error(f"版本不存在: {version_name}")
                return False
            
            # 安全检查：不允许删除最近的版本（除非强制）
            if not force:
                versions = self.list_versions(limit=3)
                if versions and version_name == versions[0]["version_name"]:
                    self.logger.error("不允许删除最新版本，请使用 --force 参数")
                    return False
            
            # 删除版本目录
            import shutil
            shutil.rmtree(backup_path)
            
            self.logger.info(f"版本删除成功: {version_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除版本失败: {e}")
            return False
    
    def cleanup_old_versions(self, keep_count: int = 10) -> int:
        """
        清理旧版本
        
        Args:
            keep_count: 保留版本数量
            
        Returns:
            删除的版本数量
        """
        try:
            self.logger.info(f"清理旧版本，保留最新 {keep_count} 个")
            
            versions = self.list_versions()
            if len(versions) <= keep_count:
                self.logger.info("版本数量未超过限制，无需清理")
                return 0
            
            # 删除超出限制的版本
            versions_to_delete = versions[keep_count:]
            deleted_count = 0
            
            for version in versions_to_delete:
                # 跳过重要版本（如标记版本）
                if version.get("backup_type") == "release":
                    continue
                
                if self.delete_version(version["version_name"], force=True):
                    deleted_count += 1
            
            self.logger.info(f"清理完成，删除了 {deleted_count} 个版本")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"清理旧版本失败: {e}")
            return 0
    
    def get_system_status(self) -> Dict:
        """
        获取系统状态
        
        Returns:
            系统状态信息
        """
        try:
            status = {
                "timestamp": datetime.datetime.now().isoformat(),
                "source_dir": str(self.source_dir),
                "backup_dir": str(self.backup_dir),
                "versions": {
                    "total_count": 0,
                    "total_size_mb": 0,
                    "latest_version": None,
                    "oldest_version": None
                },
                "disk_usage": {
                    "backup_size_mb": 0,
                    "available_space_mb": 0
                },
                "system_health": {
                    "backup_integrity": True,
                    "critical_files_exist": True,
                    "config_files_valid": True
                }
            }
            
            # 获取版本信息
            versions = self.list_versions()
            if versions:
                status["versions"]["total_count"] = len(versions)
                status["versions"]["latest_version"] = versions[0]["version_name"]
                status["versions"]["oldest_version"] = versions[-1]["version_name"]
                status["versions"]["total_size_mb"] = sum(v["size_mb"] for v in versions)
            
            # 获取磁盘使用情况
            if self.backup_dir.exists():
                import shutil
                total, used, free = shutil.disk_usage(self.backup_dir)
                status["disk_usage"]["available_space_mb"] = free / (1024 * 1024)
                
                # 计算备份目录大小
                backup_size = sum(f.stat().st_size for f in self.backup_dir.rglob('*') if f.is_file())
                status["disk_usage"]["backup_size_mb"] = backup_size / (1024 * 1024)
            
            # 检查系统健康状态
            status["system_health"]["critical_files_exist"] = self._check_critical_files()
            status["system_health"]["config_files_valid"] = self._check_config_files()
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {}
    
    def _check_critical_files(self) -> bool:
        """检查关键文件是否存在"""
        critical_files = [
            "main.py",
            "binance_trader.py",
            "strategy/maker_channel.py",
            "cache_manager.py"
        ]
        
        for file_path in critical_files:
            if not (self.source_dir / file_path).exists():
                return False
        return True
    
    def _check_config_files(self) -> bool:
        """检查配置文件是否有效"""
        config_files = [
            self.source_dir / "config" / "config.json",
            self.source_dir / "config" / "trading_config.json"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        json.load(f)
                except json.JSONDecodeError:
                    return False
        return True
    
    def create_release_version(self, version: str, description: str = "") -> Optional[str]:
        """
        创建发布版本（重要版本，不会被自动清理）
        
        Args:
            version: 版本号
            description: 版本描述
            
        Returns:
            版本名称，失败返回None
        """
        return self.create_version(
            version=version,
            description=f"Release: {description}",
            backup_type="release"
        )
    
    def create_snapshot(self, description: str = "") -> Optional[str]:
        """
        创建快照（用于临时备份）
        
        Args:
            description: 快照描述
            
        Returns:
            快照名称，失败返回None
        """
        return self.create_version(
            version=None,
            description=f"Snapshot: {description}",
            backup_type="snapshot"
        )


def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="策略版本管理系统")
    parser.add_argument("--source", default="e:/allmace", help="源代码目录")
    parser.add_argument("--backup", default="e:/allmace/backup", help="备份目录")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 创建版本
    create_parser = subparsers.add_parser("create", help="创建新版本")
    create_parser.add_argument("--version", help="版本号")
    create_parser.add_argument("--description", default="", help="版本描述")
    create_parser.add_argument("--type", choices=["manual", "auto", "release", "snapshot"], default="manual", help="备份类型")
    
    # 列出版本
    list_parser = subparsers.add_parser("list", help="列出版本")
    list_parser.add_argument("--limit", type=int, default=20, help="显示数量限制")
    
    # 回滚版本
    rollback_parser = subparsers.add_parser("rollback", help="回滚到指定版本")
    rollback_parser.add_argument("version", help="目标版本名称")
    rollback_parser.add_argument("--no-snapshot", action="store_true", help="不创建回滚前快照")
    
    # 验证版本
    verify_parser = subparsers.add_parser("verify", help="验证版本完整性")
    verify_parser.add_argument("version", help="版本名称")
    
    # 删除版本
    delete_parser = subparsers.add_parser("delete", help="删除版本")
    delete_parser.add_argument("version", help="版本名称")
    delete_parser.add_argument("--force", action="store_true", help="强制删除")
    
    # 清理旧版本
    cleanup_parser = subparsers.add_parser("cleanup", help="清理旧版本")
    cleanup_parser.add_argument("--keep", type=int, default=10, help="保留版本数量")
    
    # 系统状态
    status_parser = subparsers.add_parser("status", help="显示系统状态")
    
    # 创建发布版本
    release_parser = subparsers.add_parser("release", help="创建发布版本")
    release_parser.add_argument("version", help="版本号")
    release_parser.add_argument("--description", default="", help="版本描述")
    
    # 创建快照
    snapshot_parser = subparsers.add_parser("snapshot", help="创建快照")
    snapshot_parser.add_argument("--description", default="", help="快照描述")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建版本管理器
    vm = VersionManager(args.source, args.backup)
    
    if args.command == "create":
        version_name = vm.create_version(args.version, args.description, args.type)
        if version_name:
            print(f"版本创建成功: {version_name}")
        else:
            print("版本创建失败")
            exit(1)
    
    elif args.command == "list":
        versions = vm.list_versions(args.limit)
        if versions:
            print(f"{'版本名称':<30} {'版本':<10} {'类型':<12} {'大小(MB)':<10} {'时间':<20}")
            print("-" * 90)
            for version in versions:
                print(f"{version['version_name']:<30} {version['version']:<10} {version['backup_type']:<12} "
                      f"{version['size_mb']:<10.1f} {version['timestamp'][:19]:<20}")
        else:
            print("没有找到版本")
    
    elif args.command == "rollback":
        create_snapshot = not args.no_snapshot
        if vm.rollback_to_version(args.version, create_snapshot):
            print(f"回滚成功: {args.version}")
        else:
            print(f"回滚失败: {args.version}")
            exit(1)
    
    elif args.command == "verify":
        if vm.verify_version(args.version):
            print(f"版本验证通过: {args.version}")
        else:
            print(f"版本验证失败: {args.version}")
            exit(1)
    
    elif args.command == "delete":
        if vm.delete_version(args.version, args.force):
            print(f"版本删除成功: {args.version}")
        else:
            print(f"版本删除失败: {args.version}")
            exit(1)
    
    elif args.command == "cleanup":
        deleted_count = vm.cleanup_old_versions(args.keep)
        print(f"清理完成，删除了 {deleted_count} 个版本")
    
    elif args.command == "status":
        status = vm.get_system_status()
        if status:
            print("=== 系统状态 ===")
            print(f"源代码目录: {status['source_dir']}")
            print(f"备份目录: {status['backup_dir']}")
            print(f"版本总数: {status['versions']['total_count']}")
            print(f"最新版本: {status['versions']['latest_version']}")
            print(f"备份总大小: {status['versions']['total_size_mb']:.1f} MB")
            print(f"可用空间: {status['disk_usage']['available_space_mb']:.1f} MB")
            print(f"系统健康: {'正常' if all(status['system_health'].values()) else '异常'}")
        else:
            print("获取系统状态失败")
            exit(1)
    
    elif args.command == "release":
        version_name = vm.create_release_version(args.version, args.description)
        if version_name:
            print(f"发布版本创建成功: {version_name}")
        else:
            print("发布版本创建失败")
            exit(1)
    
    elif args.command == "snapshot":
        version_name = vm.create_snapshot(args.description)
        if version_name:
            print(f"快照创建成功: {version_name}")
        else:
            print("快照创建失败")
            exit(1)


if __name__ == "__main__":
    main()