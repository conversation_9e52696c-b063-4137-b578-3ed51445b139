"""
智能网络环境检测器
根据运行环境自动决定代理配置策略
"""

import socket
import logging
import subprocess
import platform
import requests
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class NetworkEnvironment(Enum):
    """网络环境类型"""
    ALIYUN_MALAYSIA_SERVER = "aliyun_malaysia"  # 阿里云马来西亚服务器
    LOCAL_MAINLAND = "local_mainland"           # 本地大陆环境
    OTHER_SERVER = "other_server"               # 其他服务器环境
    UNKNOWN = "unknown"                         # 未知环境

@dataclass
class NetworkConfig:
    """网络配置结果"""
    environment: NetworkEnvironment
    should_use_proxy: bool
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = None
    detection_details: Dict = None
    confidence_score: float = 0.0

class NetworkEnvironmentDetector:
    """智能网络环境检测器"""
    
    # 阿里云马来西亚服务器IP段
    ALIYUN_MALAYSIA_IPS = [
        "*************",  # 明确指定的服务器IP
        "47.250.",        # 阿里云马来西亚IP段前缀
    ]
    
    # 本地代理配置
    DEFAULT_PROXY_CONFIG = {
        "host": "127.0.0.1",
        "port": 7897
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
    def detect_environment(self) -> NetworkConfig:
        """
        检测当前网络环境并返回配置建议
        
        Returns:
            NetworkConfig: 包含环境类型和代理配置建议
        """
        try:
            # 1. 获取本机IP地址
            local_ip = self._get_local_ip()
            
            # 2. 检测操作系统和网络特征
            os_info = self._get_system_info()
            
            # 3. 进行网络连通性测试
            connectivity_test = self._test_network_connectivity()
            
            # 4. 综合判断环境类型
            environment = self._determine_environment(local_ip, os_info, connectivity_test)
            
            # 5. 生成配置建议
            config = self._generate_network_config(environment, local_ip, connectivity_test)
            
            self.logger.info(f"网络环境检测完成: {environment.value}, 代理配置: {'启用' if config.should_use_proxy else '禁用'}")
            
            return config
            
        except Exception as e:
            self.logger.error(f"网络环境检测失败: {e}")
            # 默认返回安全配置（禁用代理）
            return NetworkConfig(
                environment=NetworkEnvironment.UNKNOWN,
                should_use_proxy=False,
                detection_details={"error": str(e)},
                confidence_score=0.0
            )
    
    def _get_local_ip(self) -> str:
        """获取本机出口IP地址"""
        try:
            # 方法1: 通过连接外部服务获取出口IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception as e:
            self.logger.warning(f"获取本机IP失败 (方法1): {e}")
            
        try:
            # 方法2: 通过hostname获取IP
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip
        except Exception as e:
            self.logger.warning(f"获取本机IP失败 (方法2): {e}")
            return "unknown"
    
    def _get_system_info(self) -> Dict:
        """获取系统信息"""
        try:
            return {
                "platform": platform.system(),
                "release": platform.release(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "hostname": socket.gethostname()
            }
        except Exception as e:
            self.logger.warning(f"获取系统信息失败: {e}")
            return {}
    
    def _test_network_connectivity(self) -> Dict:
        """测试网络连通性"""
        results = {
            "binance_direct": False,
            "binance_with_proxy": False,
            "general_internet": False,
            "response_times": {}
        }
        
        # 测试直连币安API
        try:
            import time
            start_time = time.time()
            response = requests.get(
                "https://fapi.binance.com/fapi/v1/ping",
                timeout=15,
                verify=False
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                results["binance_direct"] = True
                results["response_times"]["binance_direct"] = response_time
                self.logger.info(f"币安API直连测试成功，响应时间: {response_time:.2f}s")
            
        except Exception as e:
            self.logger.info(f"币安API直连测试失败: {e}")
        
        # 测试通过代理连接币安API（仅在直连失败时测试）
        if not results["binance_direct"]:
            try:
                # 先测试代理本身是否可用
                proxy_test_response = requests.get(
                    "http://www.google.com",
                    timeout=5,
                    proxies={
                        'http': f"http://{self.DEFAULT_PROXY_CONFIG['host']}:{self.DEFAULT_PROXY_CONFIG['port']}",
                        'https': f"http://{self.DEFAULT_PROXY_CONFIG['host']}:{self.DEFAULT_PROXY_CONFIG['port']}"
                    },
                    verify=False
                )
                
                if proxy_test_response.status_code == 200:
                    start_time = time.time()
                    proxies = {
                        'http': f"http://{self.DEFAULT_PROXY_CONFIG['host']}:{self.DEFAULT_PROXY_CONFIG['port']}",
                        'https': f"http://{self.DEFAULT_PROXY_CONFIG['host']}:{self.DEFAULT_PROXY_CONFIG['port']}"
                    }
                    response = requests.get(
                        "https://fapi.binance.com/fapi/v1/ping",
                        timeout=15,
                        proxies=proxies,
                        verify=False
                    )
                    response_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        results["binance_with_proxy"] = True
                        results["response_times"]["binance_with_proxy"] = response_time
                        self.logger.info(f"币安API代理连接测试成功，响应时间: {response_time:.2f}s")
                else:
                    self.logger.info(f"代理服务器连接测试失败，状态码: {proxy_test_response.status_code}")
                
            except Exception as e:
                self.logger.info(f"币安API代理连接测试失败: {e}")
        
        # 测试一般互联网连通性
        try:
            response = requests.get("https://www.google.com", timeout=5, verify=False)
            if response.status_code == 200:
                results["general_internet"] = True
        except:
            try:
                response = requests.get("https://www.baidu.com", timeout=5, verify=False)
                if response.status_code == 200:
                    results["general_internet"] = True
            except:
                pass
        
        return results
    
    def _determine_environment(self, local_ip: str, os_info: Dict, connectivity: Dict) -> NetworkEnvironment:
        """根据检测结果判断环境类型"""
        
        # 1. 优先检查是否为阿里云马来西亚服务器
        if self._is_aliyun_malaysia_server(local_ip):
            return NetworkEnvironment.ALIYUN_MALAYSIA_SERVER
        
        # 2. 检查是否为其他服务器环境
        if self._is_server_environment(local_ip, os_info, connectivity):
            return NetworkEnvironment.OTHER_SERVER
        
        # 3. 检查是否为本地大陆环境
        if self._is_local_mainland_environment(connectivity):
            return NetworkEnvironment.LOCAL_MAINLAND
        
        # 4. 默认返回未知环境
        return NetworkEnvironment.UNKNOWN
    
    def _is_aliyun_malaysia_server(self, local_ip: str) -> bool:
        """检查是否为阿里云马来西亚服务器"""
        if local_ip == "*************":
            return True
        
        for ip_prefix in self.ALIYUN_MALAYSIA_IPS:
            if local_ip.startswith(ip_prefix):
                return True
        
        return False
    
    def _is_server_environment(self, local_ip: str, os_info: Dict, connectivity: Dict) -> bool:
        """检查是否为服务器环境"""
        # 服务器环境特征：
        # 1. 公网IP地址
        # 2. 能够直连币安API
        # 3. Linux系统或Windows Server
        
        # 检查是否为公网IP
        if self._is_public_ip(local_ip):
            # 检查是否能直连币安API
            if connectivity.get("binance_direct", False):
                return True
        
        return False
    
    def _is_local_mainland_environment(self, connectivity: Dict) -> bool:
        """检查是否为本地大陆环境"""
        # 本地大陆环境特征：
        # 1. 无法直连币安API
        # 2. 通过代理可以连接币安API
        # 3. 有一般互联网连通性
        
        return (
            not connectivity.get("binance_direct", False) and
            connectivity.get("general_internet", False)
        )
    
    def _is_public_ip(self, ip: str) -> bool:
        """检查是否为公网IP地址"""
        if ip == "unknown":
            return False
        
        try:
            # 私有IP地址段
            private_ranges = [
                ("10.0.0.0", "**************"),
                ("**********", "**************"),
                ("***********", "***************"),
                ("*********", "***************")
            ]
            
            import ipaddress
            ip_obj = ipaddress.IPv4Address(ip)
            
            for start, end in private_ranges:
                if ipaddress.IPv4Address(start) <= ip_obj <= ipaddress.IPv4Address(end):
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _generate_network_config(self, environment: NetworkEnvironment, local_ip: str, connectivity: Dict) -> NetworkConfig:
        """根据环境类型生成网络配置"""
        
        detection_details = {
            "local_ip": local_ip,
            "environment": environment.value,
            "connectivity_test": connectivity,
            "timestamp": __import__('time').time()
        }
        
        if environment == NetworkEnvironment.ALIYUN_MALAYSIA_SERVER:
            # 阿里云马来西亚服务器：严格禁用代理
            return NetworkConfig(
                environment=environment,
                should_use_proxy=False,
                detection_details=detection_details,
                confidence_score=0.95
            )
        
        elif environment == NetworkEnvironment.LOCAL_MAINLAND:
            # 本地大陆环境：必须启用代理
            return NetworkConfig(
                environment=environment,
                should_use_proxy=True,
                proxy_host=self.DEFAULT_PROXY_CONFIG["host"],
                proxy_port=self.DEFAULT_PROXY_CONFIG["port"],
                detection_details=detection_details,
                confidence_score=0.90
            )
        
        elif environment == NetworkEnvironment.OTHER_SERVER:
            # 其他服务器环境：根据连通性测试决定
            should_use_proxy = not connectivity.get("binance_direct", False)
            config = NetworkConfig(
                environment=environment,
                should_use_proxy=should_use_proxy,
                detection_details=detection_details,
                confidence_score=0.80
            )
            
            if should_use_proxy:
                config.proxy_host = self.DEFAULT_PROXY_CONFIG["host"]
                config.proxy_port = self.DEFAULT_PROXY_CONFIG["port"]
            
            return config
        
        else:
            # 未知环境：保守策略，禁用代理
            return NetworkConfig(
                environment=environment,
                should_use_proxy=False,
                detection_details=detection_details,
                confidence_score=0.50
            )
    
    def get_recommended_config(self) -> Dict:
        """获取推荐的网络配置（用于外部调用）"""
        config = self.detect_environment()
        
        result = {
            "proxy": {
                "enabled": config.should_use_proxy,
                "host": config.proxy_host or "127.0.0.1",
                "port": config.proxy_port or 7897
            },
            "environment": config.environment.value,
            "confidence": config.confidence_score,
            "details": config.detection_details
        }
        
        return result

# 便捷函数
def detect_network_environment() -> NetworkConfig:
    """便捷函数：检测网络环境"""
    detector = NetworkEnvironmentDetector()
    return detector.detect_environment()

def get_proxy_config() -> Dict:
    """便捷函数：获取代理配置"""
    detector = NetworkEnvironmentDetector()
    return detector.get_recommended_config()

if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    detector = NetworkEnvironmentDetector()
    config = detector.detect_environment()
    
    print(f"检测结果:")
    print(f"  环境类型: {config.environment.value}")
    print(f"  是否使用代理: {config.should_use_proxy}")
    print(f"  置信度: {config.confidence_score:.2f}")
    
    if config.should_use_proxy:
        print(f"  代理地址: {config.proxy_host}:{config.proxy_port}")
    
    print(f"  检测详情: {config.detection_details}")