#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略版本回滚管理系统
实现自动化回滚、版本恢复和状态验证功能
"""

import os
import json
import shutil
import hashlib
import datetime
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class RollbackManager:
    """回滚管理器"""
    
    def __init__(self, source_dir: str, backup_dir: str):
        """
        初始化回滚管理器
        
        Args:
            source_dir: 源代码目录
            backup_dir: 备份目录
        """
        self.source_dir = Path(source_dir)
        self.backup_dir = Path(backup_dir)
        self.versions_dir = self.backup_dir / "versions"
        self.logs_dir = self.backup_dir / "logs"
        
        # 确保目录存在
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        self._setup_logging()
        
        # 回滚前需要停止的进程
        self.processes_to_stop = ["python main.py", "python strategy/maker_channel.py"]
    
    def _setup_logging(self):
        """设置日志配置"""
        log_file = self.logs_dir / "rollback.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def list_available_versions(self) -> List[Dict]:
        """
        列出可回滚的版本
        
        Returns:
            可用版本列表
        """
        versions = []
        
        try:
            if not self.versions_dir.exists():
                self.logger.warning("备份目录不存在")
                return versions
            
            for backup_dir in self.versions_dir.iterdir():
                if backup_dir.is_dir():
                    metadata_file = backup_dir / "metadata.json"
                    if metadata_file.exists():
                        try:
                            with open(metadata_file, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                            
                            version_info = {
                                "version_name": backup_dir.name,
                                "version": metadata["backup_info"]["version"],
                                "timestamp": metadata["backup_info"]["timestamp"],
                                "backup_type": metadata["backup_info"]["backup_type"],
                                "description": metadata["backup_info"]["description"],
                                "size_mb": metadata["backup_size"]["total_size_mb"],
                                "file_count": metadata["backup_size"]["file_count"],
                                "path": str(backup_dir),
                                "system_state": metadata.get("system_state", {})
                            }
                            versions.append(version_info)
                        except Exception as e:
                            self.logger.warning(f"读取版本元数据失败 {backup_dir}: {e}")
            
            # 按时间戳排序（最新的在前）
            versions.sort(key=lambda x: x["timestamp"], reverse=True)
            
        except Exception as e:
            self.logger.error(f"列出可用版本失败: {e}")
        
        return versions
    
    def verify_backup_integrity(self, backup_path: Path) -> bool:
        """
        验证备份完整性
        
        Args:
            backup_path: 备份路径
            
        Returns:
            是否验证通过
        """
        try:
            self.logger.info(f"验证备份完整性: {backup_path}")
            
            # 检查元数据文件
            metadata_file = backup_path / "metadata.json"
            if not metadata_file.exists():
                self.logger.error("元数据文件不存在")
                return False
            
            # 读取元数据
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 验证关键文件存在
            files_backed_up = metadata.get("files_backed_up", {})
            missing_files = []
            
            for category, file_list in files_backed_up.items():
                for file_path in file_list:
                    full_path = backup_path / file_path
                    if not full_path.exists():
                        missing_files.append(file_path)
            
            if missing_files:
                self.logger.error(f"备份文件缺失: {missing_files}")
                return False
            
            # 验证校验和（如果存在）
            checksums = metadata.get("checksums", {})
            if checksums:
                failed_checksums = []
                for file_path, expected_checksum in checksums.items():
                    full_path = backup_path / file_path
                    if full_path.exists():
                        actual_checksum = f"sha256:{self._calculate_file_checksum(full_path)}"
                        if actual_checksum != expected_checksum:
                            failed_checksums.append(file_path)
                
                if failed_checksums:
                    self.logger.error(f"校验和验证失败: {failed_checksums}")
                    return False
            
            self.logger.info("备份完整性验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"备份完整性验证失败: {e}")
            return False
    
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """计算文件SHA256校验和"""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件校验和失败 {file_path}: {e}")
            return ""
    
    def stop_running_processes(self) -> bool:
        """
        停止正在运行的策略进程
        
        Returns:
            是否成功停止
        """
        try:
            self.logger.info("停止正在运行的策略进程...")
            
            # 在Windows上查找并终止Python进程
            try:
                # 使用tasklist查找相关进程
                result = subprocess.run(
                    ["tasklist", "/FI", "IMAGENAME eq python.exe", "/FO", "CSV"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                
                if "python.exe" in result.stdout:
                    self.logger.info("发现运行中的Python进程")
                    
                    # 尝试优雅地停止（这里可以添加更精确的进程识别逻辑）
                    # 由于无法精确识别策略进程，建议手动停止
                    self.logger.warning("请手动停止策略进程后继续回滚")
                    return True
                else:
                    self.logger.info("没有发现运行中的Python进程")
                    return True
                    
            except subprocess.CalledProcessError:
                self.logger.warning("无法查询进程状态，假设没有运行中的进程")
                return True
                
        except Exception as e:
            self.logger.error(f"停止进程失败: {e}")
            return False
    
    def create_pre_rollback_snapshot(self) -> Optional[str]:
        """
        创建回滚前快照
        
        Returns:
            快照版本名称，失败返回None
        """
        try:
            self.logger.info("创建回滚前快照...")
            
            # 导入备份管理器
            from backup import BackupManager
            
            backup_manager = BackupManager(str(self.source_dir), str(self.backup_dir))
            snapshot_name = backup_manager.create_backup(
                version=None,
                backup_type="snapshot",
                description="回滚前自动快照"
            )
            
            if snapshot_name:
                self.logger.info(f"回滚前快照创建成功: {snapshot_name}")
                return snapshot_name
            else:
                self.logger.error("回滚前快照创建失败")
                return None
                
        except Exception as e:
            self.logger.error(f"创建回滚前快照失败: {e}")
            return None
    
    def restore_file(self, src_path: Path, dst_path: Path) -> bool:
        """
        恢复单个文件
        
        Args:
            src_path: 源文件路径（备份中的文件）
            dst_path: 目标文件路径（要恢复到的位置）
            
        Returns:
            是否恢复成功
        """
        try:
            if not src_path.exists():
                self.logger.warning(f"备份文件不存在: {src_path}")
                return False
            
            # 确保目标目录存在
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 如果目标文件存在，先备份
            if dst_path.exists():
                backup_path = dst_path.with_suffix(dst_path.suffix + ".rollback_backup")
                shutil.copy2(dst_path, backup_path)
            
            # 复制文件
            shutil.copy2(src_path, dst_path)
            
            # 验证复制结果
            if dst_path.exists() and dst_path.stat().st_size == src_path.stat().st_size:
                self.logger.debug(f"文件恢复成功: {dst_path}")
                return True
            else:
                self.logger.error(f"文件恢复验证失败: {dst_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"文件恢复失败 {src_path} -> {dst_path}: {e}")
            return False
    
    def restore_directory(self, src_dir: Path, dst_dir: Path) -> bool:
        """
        恢复目录
        
        Args:
            src_dir: 源目录（备份中的目录）
            dst_dir: 目标目录（要恢复到的位置）
            
        Returns:
            是否恢复成功
        """
        try:
            if not src_dir.exists():
                self.logger.warning(f"备份目录不存在: {src_dir}")
                return False
            
            success_count = 0
            total_count = 0
            
            for item in src_dir.rglob('*'):
                if item.is_file():
                    total_count += 1
                    relative_path = item.relative_to(src_dir)
                    dst_file = dst_dir / relative_path
                    
                    if self.restore_file(item, dst_file):
                        success_count += 1
            
            self.logger.info(f"目录恢复完成: {dst_dir} ({success_count}/{total_count} 文件)")
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"目录恢复失败 {src_dir} -> {dst_dir}: {e}")
            return False
    
    def rollback_to_version(self, version_name: str, create_snapshot: bool = True) -> bool:
        """
        回滚到指定版本
        
        Args:
            version_name: 目标版本名称
            create_snapshot: 是否创建回滚前快照
            
        Returns:
            是否回滚成功
        """
        try:
            self.logger.info(f"开始回滚到版本: {version_name}")
            
            # 验证目标版本
            backup_path = self.versions_dir / version_name
            if not backup_path.exists():
                self.logger.error(f"目标版本不存在: {version_name}")
                return False
            
            # 验证备份完整性
            if not self.verify_backup_integrity(backup_path):
                self.logger.error("目标版本备份完整性验证失败")
                return False
            
            # 读取备份元数据
            metadata_file = backup_path / "metadata.json"
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 创建回滚前快照
            snapshot_name = None
            if create_snapshot:
                snapshot_name = self.create_pre_rollback_snapshot()
                if not snapshot_name:
                    self.logger.warning("回滚前快照创建失败，继续回滚...")
            
            # 停止运行中的进程
            if not self.stop_running_processes():
                self.logger.warning("停止进程失败，继续回滚...")
            
            # 执行文件回滚
            files_backed_up = metadata.get("files_backed_up", {})
            rollback_stats = {
                "restored_files": [],
                "failed_files": [],
                "total_files": 0
            }
            
            # 按类别恢复文件
            for category, file_list in files_backed_up.items():
                self.logger.info(f"恢复 {category}...")
                
                for file_path in file_list:
                    rollback_stats["total_files"] += 1
                    src_file = backup_path / file_path
                    dst_file = self.source_dir / file_path
                    
                    if self.restore_file(src_file, dst_file):
                        rollback_stats["restored_files"].append(file_path)
                    else:
                        rollback_stats["failed_files"].append(file_path)
            
            # 特殊处理：恢复cache目录
            cache_backup = backup_path / "cache"
            if cache_backup.exists():
                self.logger.info("恢复缓存目录...")
                cache_target = self.source_dir / "cache"
                self.restore_directory(cache_backup, cache_target)
            
            # 记录回滚结果
            success_rate = len(rollback_stats["restored_files"]) / rollback_stats["total_files"] * 100
            self.logger.info(f"文件回滚完成: {len(rollback_stats['restored_files'])}/{rollback_stats['total_files']} ({success_rate:.1f}%)")
            
            if rollback_stats["failed_files"]:
                self.logger.warning(f"回滚失败的文件: {rollback_stats['failed_files']}")
            
            # 验证回滚结果
            if self.verify_rollback():
                self.logger.info(f"回滚成功: {version_name}")
                
                # 记录回滚操作
                self._record_rollback_operation(version_name, snapshot_name, rollback_stats)
                
                return True
            else:
                self.logger.error("回滚后验证失败")
                return False
                
        except Exception as e:
            self.logger.error(f"回滚失败: {e}")
            return False
    
    def verify_rollback(self) -> bool:
        """
        验证回滚后的系统状态
        
        Returns:
            是否验证通过
        """
        try:
            self.logger.info("验证回滚后的系统状态...")
            
            # 检查关键文件是否存在
            critical_files = [
                "main.py",
                "binance_trader.py",
                "strategy/maker_channel.py",
                "cache_manager.py"
            ]
            
            missing_files = []
            for file_path in critical_files:
                full_path = self.source_dir / file_path
                if not full_path.exists():
                    missing_files.append(file_path)
            
            if missing_files:
                self.logger.error(f"关键文件缺失: {missing_files}")
                return False
            
            # 验证配置文件语法
            config_files = [
                self.source_dir / "config" / "config.json",
                self.source_dir / "config" / "trading_config.json"
            ]
            
            for config_file in config_files:
                if config_file.exists():
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            json.load(f)
                        self.logger.debug(f"配置文件语法正确: {config_file}")
                    except json.JSONDecodeError as e:
                        self.logger.error(f"配置文件语法错误 {config_file}: {e}")
                        return False
            
            # 验证Python模块导入
            try:
                import sys
                sys.path.insert(0, str(self.source_dir))
                
                # 尝试导入关键模块
                import binance_trader
                import cache_manager
                
                self.logger.info("模块导入验证通过")
                
            except ImportError as e:
                self.logger.error(f"模块导入失败: {e}")
                return False
            
            self.logger.info("回滚后验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"回滚验证失败: {e}")
            return False
    
    def _record_rollback_operation(self, version_name: str, snapshot_name: Optional[str], stats: Dict):
        """记录回滚操作"""
        try:
            rollback_record = {
                "timestamp": datetime.datetime.now().isoformat(),
                "target_version": version_name,
                "pre_rollback_snapshot": snapshot_name,
                "restored_files": len(stats["restored_files"]),
                "failed_files": len(stats["failed_files"]),
                "total_files": stats["total_files"],
                "success_rate": len(stats["restored_files"]) / stats["total_files"] * 100,
                "failed_file_list": stats["failed_files"]
            }
            
            # 保存到回滚日志
            rollback_log_file = self.logs_dir / "rollback_history.json"
            
            if rollback_log_file.exists():
                with open(rollback_log_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            else:
                history = []
            
            history.append(rollback_record)
            
            # 只保留最近20次回滚记录
            if len(history) > 20:
                history = history[-20:]
            
            with open(rollback_log_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            self.logger.error(f"记录回滚操作失败: {e}")
    
    def get_rollback_history(self) -> List[Dict]:
        """
        获取回滚历史记录
        
        Returns:
            回滚历史列表
        """
        try:
            rollback_log_file = self.logs_dir / "rollback_history.json"
            
            if rollback_log_file.exists():
                with open(rollback_log_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"获取回滚历史失败: {e}")
            return []
    
    def emergency_restore(self, snapshot_name: str) -> bool:
        """
        紧急恢复到指定快照
        
        Args:
            snapshot_name: 快照名称
            
        Returns:
            是否恢复成功
        """
        self.logger.warning(f"执行紧急恢复: {snapshot_name}")
        return self.rollback_to_version(snapshot_name, create_snapshot=False)


def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="策略版本回滚管理系统")
    parser.add_argument("--source", default="e:/allmace", help="源代码目录")
    parser.add_argument("--backup", default="e:/allmace/backup", help="备份目录")
    parser.add_argument("--action", choices=["rollback", "list", "verify", "history"], default="list", help="操作类型")
    parser.add_argument("--version", help="目标版本名称")
    parser.add_argument("--no-snapshot", action="store_true", help="不创建回滚前快照")
    
    args = parser.parse_args()
    
    # 创建回滚管理器
    rollback_manager = RollbackManager(args.source, args.backup)
    
    if args.action == "rollback":
        # 执行回滚
        if not args.version:
            print("请指定目标版本")
            exit(1)
        
        create_snapshot = not args.no_snapshot
        success = rollback_manager.rollback_to_version(args.version, create_snapshot)
        
        if success:
            print(f"回滚成功: {args.version}")
        else:
            print(f"回滚失败: {args.version}")
            exit(1)
    
    elif args.action == "list":
        # 列出可用版本
        versions = rollback_manager.list_available_versions()
        if versions:
            print(f"{'版本名称':<25} {'版本':<8} {'类型':<12} {'大小(MB)':<10} {'文件数':<8} {'时间'}")
            print("-" * 80)
            for version in versions:
                print(f"{version['version_name']:<25} {version['version']:<8} {version['backup_type']:<12} "
                      f"{version['size_mb']:<10.1f} {version['file_count']:<8} {version['timestamp'][:19]}")
        else:
            print("没有找到可用版本")
    
    elif args.action == "verify":
        # 验证回滚后状态
        if rollback_manager.verify_rollback():
            print("系统状态验证通过")
        else:
            print("系统状态验证失败")
            exit(1)
    
    elif args.action == "history":
        # 显示回滚历史
        history = rollback_manager.get_rollback_history()
        if history:
            print(f"{'时间':<20} {'目标版本':<25} {'成功率':<8} {'文件数':<8}")
            print("-" * 70)
            for record in history:
                print(f"{record['timestamp'][:19]:<20} {record['target_version']:<25} "
                      f"{record['success_rate']:<8.1f}% {record['total_files']:<8}")
        else:
            print("没有回滚历史记录")


if __name__ == "__main__":
    main()