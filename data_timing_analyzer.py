#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据获取时机差异分析器
分析实时系统与增量脚本在数据获取时机上的差异
"""

import os
import sys
import json
import pickle
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataTimingAnalyzer:
    """数据获取时机差异分析器"""
    
    def __init__(self):
        self.base_dir = os.getcwd()
        self.data_dir = os.path.join(self.base_dir, 'data')
        self.cache_dir = self.base_dir
        
    def analyze_data_sources(self) -> Dict[str, Any]:
        """分析数据源和获取时机"""
        analysis = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'real_time_system': {},
            'incremental_script': {},
            'timing_differences': [],
            'data_freshness': {},
            'cache_status': {}
        }
        
        # 分析实时系统数据源
        logger.info("分析实时系统数据源...")
        analysis['real_time_system'] = self._analyze_real_time_data()
        
        # 分析增量脚本数据源
        logger.info("分析增量脚本数据源...")
        analysis['incremental_script'] = self._analyze_incremental_data()
        
        # 分析时机差异
        logger.info("分析数据获取时机差异...")
        analysis['timing_differences'] = self._analyze_timing_differences()
        
        # 分析数据新鲜度
        logger.info("分析数据新鲜度...")
        analysis['data_freshness'] = self._analyze_data_freshness()
        
        # 分析缓存状态
        logger.info("分析缓存状态...")
        analysis['cache_status'] = self._analyze_cache_status()
        
        return analysis
    
    def _analyze_real_time_data(self) -> Dict[str, Any]:
        """分析实时系统数据获取"""
        real_time_data = {
            'data_sources': [],
            'update_frequency': 'real-time',
            'data_flow': [],
            'dependencies': []
        }
        
        # 实时系统数据流分析
        real_time_data['data_sources'] = [
            'Binance API - 实时K线数据',
            'Binance API - 实时深度数据',
            'Binance API - 实时成交量数据',
            'cand_cache - 候选池缓存',
            'EnhancedScoreCalculator - 实时评分计算'
        ]
        
        real_time_data['data_flow'] = [
            '1. 系统启动时加载候选池 (candidates.pkl)',
            '2. 初始化 cand_cache (问题点: 可能未正确初始化)',
            '3. 定时从 Binance API 获取最新数据',
            '4. 实时计算评分和通道突破',
            '5. 更新候选池状态',
            '6. 显示当前候选池数量 (基于 cand_cache)'
        ]
        
        real_time_data['dependencies'] = [
            'Binance API 连接状态',
            'cand_cache 初始化状态',
            'EnhancedScoreCalculator 可用性',
            '网络延迟和API限制'
        ]
        
        return real_time_data
    
    def _analyze_incremental_data(self) -> Dict[str, Any]:
        """分析增量脚本数据获取"""
        incremental_data = {
            'data_sources': [],
            'update_frequency': 'batch/scheduled',
            'data_flow': [],
            'dependencies': []
        }
        
        # 增量脚本数据流分析
        incremental_data['data_sources'] = [
            'candidates.pkl - 候选池列表',
            '本地K线数据文件 (data/klines/)',
            'EnhancedScoreCalculator - 批量评分计算',
            '缓存的深度和成交量数据'
        ]
        
        incremental_data['data_flow'] = [
            '1. 加载候选池列表 (candidates.pkl)',
            '2. 批量读取本地K线数据文件',
            '3. 批量计算评分和通道突破',
            '4. 生成分析报告',
            '5. 不更新实时系统状态'
        ]
        
        incremental_data['dependencies'] = [
            'candidates.pkl 文件存在性',
            '本地K线数据文件完整性',
            'EnhancedScoreCalculator 可用性',
            '文件系统访问权限'
        ]
        
        return incremental_data
    
    def _analyze_timing_differences(self) -> List[Dict[str, Any]]:
        """分析时机差异"""
        differences = []
        
        # 数据获取时机差异
        differences.append({
            'category': '数据获取时机',
            'real_time': '连续实时获取，数据最新',
            'incremental': '批量获取，基于历史缓存数据',
            'impact': '实时系统数据更新，增量脚本数据可能滞后',
            'severity': 'medium'
        })
        
        # 候选池状态差异
        differences.append({
            'category': '候选池状态',
            'real_time': '基于 cand_cache 动态更新',
            'incremental': '基于 candidates.pkl 静态加载',
            'impact': '实时系统可能显示过时状态，增量脚本基于固定列表',
            'severity': 'high'
        })
        
        # 评分计算时机差异
        differences.append({
            'category': '评分计算时机',
            'real_time': '实时计算，基于最新市场数据',
            'incremental': '批量计算，基于缓存数据',
            'impact': '评分结果可能不同，影响候选池筛选',
            'severity': 'medium'
        })
        
        # 通道突破检查时机差异
        differences.append({
            'category': '通道突破检查',
            'real_time': '实时检查，基于最新K线',
            'incremental': '批量检查，基于历史K线文件',
            'impact': '突破检测结果可能不同，影响交易决策',
            'severity': 'high'
        })
        
        return differences
    
    def _analyze_data_freshness(self) -> Dict[str, Any]:
        """分析数据新鲜度"""
        freshness = {
            'candidates_pkl': self._get_file_age('candidates.pkl'),
            'cand_cache_pkl': self._get_file_age('cand_cache.pkl'),
            'kline_data': self._analyze_kline_freshness(),
            'recommendations': []
        }
        
        # 生成建议
        if (freshness['candidates_pkl']['age_hours'] is not None and 
            freshness['candidates_pkl']['age_hours'] > 24):
            freshness['recommendations'].append('candidates.pkl 超过24小时未更新，建议重新生成')
        
        if not freshness['cand_cache_pkl']['exists']:
            freshness['recommendations'].append('cand_cache.pkl 不存在，需要创建初始化机制')
        
        if freshness['kline_data']['missing_files'] > 0:
            freshness['recommendations'].append(f'缺失 {freshness["kline_data"]["missing_files"]} 个K线数据文件')
        
        return freshness
    
    def _analyze_kline_freshness(self) -> Dict[str, Any]:
        """分析K线数据新鲜度"""
        kline_info = {
            'total_files': 0,
            'missing_files': 0,
            'outdated_files': 0,
            'latest_update': None
        }
        
        # 检查K线数据目录
        kline_dir = os.path.join(self.data_dir, 'klines')
        if os.path.exists(kline_dir):
            files = [f for f in os.listdir(kline_dir) if f.endswith('.csv')]
            kline_info['total_files'] = len(files)
            
            # 检查最新更新时间
            if files:
                latest_file = max([os.path.join(kline_dir, f) for f in files], 
                                key=os.path.getmtime)
                kline_info['latest_update'] = datetime.fromtimestamp(
                    os.path.getmtime(latest_file)
                ).strftime('%Y-%m-%d %H:%M:%S')
        
        # 检查候选池对应的K线文件
        try:
            with open('candidates.pkl', 'rb') as f:
                candidates = pickle.load(f)
            
            for symbol in candidates:
                kline_file = os.path.join(kline_dir, f'{symbol}_1h.csv')
                if not os.path.exists(kline_file):
                    kline_info['missing_files'] += 1
        except:
            pass
        
        return kline_info
    
    def _get_file_age(self, filename: str) -> Dict[str, Any]:
        """获取文件年龄信息"""
        file_info = {
            'exists': False,
            'last_modified': None,
            'age_hours': None,
            'size_bytes': None
        }
        
        if os.path.exists(filename):
            file_info['exists'] = True
            stat = os.stat(filename)
            file_info['last_modified'] = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            file_info['age_hours'] = (datetime.now() - datetime.fromtimestamp(stat.st_mtime)).total_seconds() / 3600
            file_info['size_bytes'] = stat.st_size
        
        return file_info
    
    def _analyze_cache_status(self) -> Dict[str, Any]:
        """分析缓存状态"""
        cache_status = {
            'candidates_pkl': self._analyze_candidates_cache(),
            'cand_cache_pkl': self._analyze_cand_cache(),
            'sync_issues': []
        }
        
        # 检查同步问题
        if cache_status['candidates_pkl']['exists'] and not cache_status['cand_cache_pkl']['exists']:
            cache_status['sync_issues'].append('candidates.pkl 存在但 cand_cache.pkl 不存在')
        
        if (cache_status['candidates_pkl']['exists'] and cache_status['cand_cache_pkl']['exists'] and
            cache_status['candidates_pkl']['count'] != cache_status['cand_cache_pkl']['count']):
            cache_status['sync_issues'].append('两个缓存文件的币种数量不一致')
        
        return cache_status
    
    def _analyze_candidates_cache(self) -> Dict[str, Any]:
        """分析 candidates.pkl 缓存"""
        cache_info = {
            'exists': False,
            'count': 0,
            'format': 'list',
            'sample_data': None
        }
        
        try:
            with open('candidates.pkl', 'rb') as f:
                candidates = pickle.load(f)
            cache_info['exists'] = True
            cache_info['count'] = len(candidates)
            cache_info['sample_data'] = candidates[:3] if len(candidates) > 3 else candidates
        except Exception as e:
            logger.warning(f"无法加载 candidates.pkl: {e}")
        
        return cache_info
    
    def _analyze_cand_cache(self) -> Dict[str, Any]:
        """分析 cand_cache.pkl 缓存"""
        cache_info = {
            'exists': False,
            'count': 0,
            'format': 'dict',
            'sample_keys': None
        }
        
        try:
            with open('cand_cache.pkl', 'rb') as f:
                cand_cache = pickle.load(f)
            cache_info['exists'] = True
            cache_info['count'] = len(cand_cache)
            cache_info['sample_keys'] = list(cand_cache.keys())[:3] if len(cand_cache) > 3 else list(cand_cache.keys())
        except Exception as e:
            logger.warning(f"无法加载 cand_cache.pkl: {e}")
        
        return cache_info
    
    def generate_report(self, analysis: Dict[str, Any]) -> str:
        """生成分析报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'data_timing_analysis_{timestamp}.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("实时系统与增量脚本数据获取时机差异分析报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"生成时间: {analysis['timestamp']}\n\n")
            
            # 实时系统数据源分析
            f.write("📊 实时系统数据源分析\n")
            f.write("-" * 60 + "\n")
            real_time = analysis['real_time_system']
            f.write("数据源:\n")
            for source in real_time['data_sources']:
                f.write(f"  • {source}\n")
            f.write(f"\n更新频率: {real_time['update_frequency']}\n\n")
            
            f.write("数据流程:\n")
            for step in real_time['data_flow']:
                f.write(f"  {step}\n")
            f.write("\n")
            
            f.write("依赖项:\n")
            for dep in real_time['dependencies']:
                f.write(f"  • {dep}\n")
            f.write("\n")
            
            # 增量脚本数据源分析
            f.write("📈 增量脚本数据源分析\n")
            f.write("-" * 60 + "\n")
            incremental = analysis['incremental_script']
            f.write("数据源:\n")
            for source in incremental['data_sources']:
                f.write(f"  • {source}\n")
            f.write(f"\n更新频率: {incremental['update_frequency']}\n\n")
            
            f.write("数据流程:\n")
            for step in incremental['data_flow']:
                f.write(f"  {step}\n")
            f.write("\n")
            
            f.write("依赖项:\n")
            for dep in incremental['dependencies']:
                f.write(f"  • {dep}\n")
            f.write("\n")
            
            # 时机差异分析
            f.write("⏰ 数据获取时机差异分析\n")
            f.write("-" * 60 + "\n")
            for diff in analysis['timing_differences']:
                f.write(f"类别: {diff['category']}\n")
                f.write(f"  实时系统: {diff['real_time']}\n")
                f.write(f"  增量脚本: {diff['incremental']}\n")
                f.write(f"  影响: {diff['impact']}\n")
                f.write(f"  严重程度: {diff['severity']}\n\n")
            
            # 数据新鲜度分析
            f.write("🔄 数据新鲜度分析\n")
            f.write("-" * 60 + "\n")
            freshness = analysis['data_freshness']
            
            f.write("candidates.pkl:\n")
            if freshness['candidates_pkl']['exists']:
                f.write(f"  • 存在: 是\n")
                f.write(f"  • 最后修改: {freshness['candidates_pkl']['last_modified']}\n")
                f.write(f"  • 年龄: {freshness['candidates_pkl']['age_hours']:.1f} 小时\n")
                f.write(f"  • 大小: {freshness['candidates_pkl']['size_bytes']} 字节\n")
            else:
                f.write("  • 存在: 否\n")
            f.write("\n")
            
            f.write("cand_cache.pkl:\n")
            if freshness['cand_cache_pkl']['exists']:
                f.write(f"  • 存在: 是\n")
                f.write(f"  • 最后修改: {freshness['cand_cache_pkl']['last_modified']}\n")
                f.write(f"  • 年龄: {freshness['cand_cache_pkl']['age_hours']:.1f} 小时\n")
                f.write(f"  • 大小: {freshness['cand_cache_pkl']['size_bytes']} 字节\n")
            else:
                f.write("  • 存在: 否 ⚠️\n")
            f.write("\n")
            
            f.write("K线数据:\n")
            kline = freshness['kline_data']
            f.write(f"  • 总文件数: {kline['total_files']}\n")
            f.write(f"  • 缺失文件数: {kline['missing_files']}\n")
            f.write(f"  • 最新更新: {kline['latest_update']}\n\n")
            
            if freshness['recommendations']:
                f.write("建议:\n")
                for rec in freshness['recommendations']:
                    f.write(f"  • {rec}\n")
                f.write("\n")
            
            # 缓存状态分析
            f.write("💾 缓存状态分析\n")
            f.write("-" * 60 + "\n")
            cache = analysis['cache_status']
            
            f.write("candidates.pkl 缓存:\n")
            cand_cache = cache['candidates_pkl']
            f.write(f"  • 存在: {'是' if cand_cache['exists'] else '否'}\n")
            f.write(f"  • 币种数量: {cand_cache['count']}\n")
            f.write(f"  • 数据格式: {cand_cache['format']}\n")
            if cand_cache['sample_data']:
                f.write(f"  • 示例数据: {cand_cache['sample_data']}\n")
            f.write("\n")
            
            f.write("cand_cache.pkl 缓存:\n")
            cc_cache = cache['cand_cache_pkl']
            f.write(f"  • 存在: {'是' if cc_cache['exists'] else '否'}\n")
            f.write(f"  • 币种数量: {cc_cache['count']}\n")
            f.write(f"  • 数据格式: {cc_cache['format']}\n")
            if cc_cache['sample_keys']:
                f.write(f"  • 示例键值: {cc_cache['sample_keys']}\n")
            f.write("\n")
            
            if cache['sync_issues']:
                f.write("同步问题:\n")
                for issue in cache['sync_issues']:
                    f.write(f"  ⚠️ {issue}\n")
                f.write("\n")
            
            # 关键发现和建议
            f.write("🎯 关键发现和建议\n")
            f.write("-" * 60 + "\n")
            f.write("关键发现:\n")
            f.write("1. 实时系统和增量脚本使用不同的数据获取时机\n")
            f.write("2. 实时系统依赖 cand_cache，但该文件可能不存在\n")
            f.write("3. 数据新鲜度差异可能导致分析结果不一致\n")
            f.write("4. 缺少数据同步机制确保一致性\n\n")
            
            f.write("建议:\n")
            f.write("1. 实现 cand_cache 自动初始化机制\n")
            f.write("2. 添加数据同步检查和更新机制\n")
            f.write("3. 统一数据获取时机或添加时间戳标记\n")
            f.write("4. 增加数据新鲜度监控和告警\n")
            f.write("5. 考虑使用统一的数据管理接口\n")
        
        return report_file

def main():
    """主函数"""
    print("开始数据获取时机差异分析...")
    
    analyzer = DataTimingAnalyzer()
    analysis = analyzer.analyze_data_sources()
    report_file = analyzer.generate_report(analysis)
    
    print(f"\n数据获取时机差异分析完成！")
    print(f"报告文件: {report_file}")
    
    # 输出关键发现
    print("\n关键发现:")
    for diff in analysis['timing_differences']:
        if diff['severity'] == 'high':
            print(f"• {diff['category']}: {diff['impact']}")
    
    # 输出缓存同步问题
    if analysis['cache_status']['sync_issues']:
        print("\n缓存同步问题:")
        for issue in analysis['cache_status']['sync_issues']:
            print(f"• {issue}")

if __name__ == "__main__":
    main()