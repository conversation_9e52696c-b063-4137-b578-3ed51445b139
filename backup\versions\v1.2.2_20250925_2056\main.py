import yaml
import logging
import warnings
import urllib3
import time
import asyncio
from binance_trader import BinanceTrader
from strategy.maker_channel import MakerChannelStrategy
from cache_manager import CacheManager

# 完全禁用所有警告
warnings.filterwarnings("ignore")
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置详细日志格式（控制台+文件）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler('strategy.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 设置ReferenceFolder相关日志器的级别为DEBUG以显示评分详情
logging.getLogger('ReferenceFolder.symbol_scorer').setLevel(logging.DEBUG)

def main():
    """主函数"""
    # 加载配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    # 初始化缓存管理器
    cache_manager = CacheManager()
    
    # 初始化交易器
    trader = BinanceTrader(cfg['binance'])
    
    # 执行冷启动：加载全币种基础信息
    logging.info("执行冷启动：加载全币种基础信息...")
    symbols_info = trader.get_all_symbols()
    logging.info(f"冷启动完成，加载 {len(symbols_info)} 个交易对信息")
    
    # 初始化策略
    strategy = MakerChannelStrategy(trader, cfg)
    
    # 执行启动预热扫描
    logging.info("执行启动预热扫描（涨幅榜前25 + 成交量榜前25）...")
    strategy.warmup()
    
    # 主循环使用完整的策略循环
    logging.info("启动策略主循环...")
    asyncio.run(strategy.loop())

if __name__ == '__main__':
    main()