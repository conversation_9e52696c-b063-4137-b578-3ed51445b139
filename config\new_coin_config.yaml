# 新币种处理器配置文件
# 用于动态调整新币种处理参数，无需修改代码

# 基础配置
basic:
  # 新币种识别阈值（天数）
  new_coin_threshold_days: 7
  
  # 默认配置参数
  default_config:
    max_available_klines: 50
    recommended_intervals: ['1m', '5m', '15m', '1h']
    min_klines_required: 5
    max_limit: 30
    retry_count: 3
    retry_delay: 1.5

# 新币种白名单配置
whitelist:
  AKEUSDT:
    onboard_date: '2025-09-26'
    max_available_klines: 82
    recommended_intervals: ['1m', '5m', '15m', '1h']
    min_klines_required: 5
    max_limit: 50
    description: 'Akash Network token - 新上市币种'
  
  # 可以添加更多新币种
  # NEWUSDT:
  #   onboard_date: '2025-01-01'
  #   max_available_klines: 100
  #   recommended_intervals: ['5m', '15m', '1h']
  #   min_klines_required: 10
  #   max_limit: 50
  #   description: '新币种示例'

# 错误处理配置
error_handling:
  # 最大连续错误数
  max_consecutive_errors: 10
  
  # 错误冷却时间（秒）
  error_cooldown_time: 300
  
  # 是否启用错误统计
  enable_error_stats: true
  
  # 错误日志级别
  error_log_level: 'WARNING'

# 性能优化配置
performance:
  # 是否启用缓存
  enable_cache: true
  
  # 缓存过期时间（秒）
  cache_ttl: 3600
  
  # 最大缓存大小
  max_cache_size: 1000
  
  # 是否启用并发处理
  enable_concurrent: false
  
  # 并发数量
  concurrent_limit: 3

# 数据验证配置
validation:
  # 是否启用严格验证
  strict_validation: false
  
  # 价格波动阈值（最高价/最低价比值）
  price_volatility_threshold: 10.0
  
  # 时间间隔标准差阈值（分钟）
  time_interval_std_threshold: 30
  
  # 是否检查数据连续性
  check_data_continuity: true

# 日志配置
logging:
  # 日志级别
  level: 'INFO'
  
  # 是否启用详细日志
  verbose: true
  
  # 是否记录性能指标
  log_performance: true
  
  # 日志格式
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 监控配置
monitoring:
  # 是否启用监控
  enable_monitoring: true
  
  # 统计报告间隔（秒）
  stats_report_interval: 3600
  
  # 是否启用健康检查
  enable_health_check: true
  
  # 健康检查间隔（秒）
  health_check_interval: 300

# 自动更新配置
auto_update:
  # 是否启用自动更新白名单
  enable_auto_whitelist: true
  
  # 自动检测新币种的最小交易量
  min_volume_threshold: 1000000
  
  # 自动检测新币种的最小价格变化
  min_price_change_threshold: 0.05
  
  # 自动更新检查间隔（秒）
  update_check_interval: 86400  # 24小时