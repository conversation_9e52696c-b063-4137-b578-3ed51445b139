2025-09-28 08:30:56 - network_environment_detector - INFO - 币安API直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ping (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001E35F2FEE90>, 'Connection to fapi.binance.com timed out. (connect timeout=15)'))
2025-09-28 08:30:57 - network_environment_detector - INFO - 币安API代理连接测试成功，响应时间: 0.54s
2025-09-28 08:31:02 - network_environment_detector - INFO - 网络环境检测完成: local_mainland, 代理配置: 启用
2025-09-28 08:31:02 - root - INFO - 动态代理配置: {'enabled': True, 'host': '127.0.0.1', 'port': 7897}
2025-09-28 08:31:02 - root - INFO - 检测到网络环境: local_mainland
2025-09-28 08:31:02 - root - INFO - 检测置信度: 0.90
2025-09-28 08:31:02 - binance_trader - INFO - API Key length: 64
2025-09-28 08:31:02 - binance_trader - INFO - API Secret length: 64
2025-09-28 08:31:03 - binance_trader - INFO - 加载了501个有效的永续合约交易对
2025-09-28 08:31:03 - OrderOperationQueue - INFO - 订单操作队列已启动
2025-09-28 08:31:03 - binance_trader - INFO - BinanceTrader初始化完成，API限流器和订单操作队列已启动
2025-09-28 08:31:03 - root - INFO - 执行冷启动：加载全币种基础信息...
2025-09-28 08:31:03 - root - INFO - 冷启动完成，加载 501 个交易对信息
2025-09-28 08:31:03 - MakerChannel - INFO - 已加载通道配置文件: f:\allmace\config\channel_config.json
2025-09-28 08:31:03 - VolumeValidator - INFO - 成交量验证器初始化完成 - 绝对成交量阈值: 1,000,000 USDT, 相对成交量倍数: 1.5x, 均值周期: 20日
2025-09-28 08:31:03 - 核心指标监控系统 - INFO - 核心指标监控系统启动
2025-09-28 08:31:03 - trailing_stop_manager - INFO - 移动止损配置验证通过
2025-09-28 08:31:03 - trailing_stop_manager - INFO - 移动止损管理器初始化完成
2025-09-28 08:31:03 - MakerChannel - INFO - 移动止损管理器初始化成功
2025-09-28 08:31:03 - strategy_optimizer - INFO - 策略优化器启动
2025-09-28 08:31:03 - MakerChannel - INFO - 成功加载配置文件: f:\allmace\config\new_coin_config.yaml
2025-09-28 08:31:03 - BatchMonitor - INFO - 批次监控系统已启动 - 配置: 50个币种/批次
2025-09-28 08:31:03 - ReferenceFolder.symbol_selector - INFO - 币种选择器初始化完成，选择前50个币种，最低评分要求大于:6
2025-09-28 08:31:03 - MakerChannel - INFO - 监控系统、持仓监控器、策略优化器和批次恢复系统已启动
2025-09-28 08:31:03 - root - INFO - 成功加载现有 cand_cache.pkl，包含 43 个币种
2025-09-28 08:31:03 - root - INFO - 执行启动预热扫描（涨幅榜前25 + 成交量榜前25）...
2025-09-28 08:31:03 - MakerChannel - INFO - 执行轻量启动预热（仅基础信息）...
2025-09-28 08:31:03 - MakerChannel - INFO - 启动时无活跃持仓
2025-09-28 08:31:04 - MakerChannel - INFO - 交易所有效交易对数量: 501, 过滤后有效交易对数量: 490
2025-09-28 08:31:04 - MakerChannel - INFO - 预热完成，候选池包含 43 个交易对
2025-09-28 08:31:04 - root - INFO - 已保存 cand_cache 到文件，包含 43 个币种
2025-09-28 08:31:04 - root - INFO - 候选池数据同步成功，包含 43 个币种
2025-09-28 08:31:04 - root - INFO - 启动策略主循环...
2025-09-28 08:31:04 - MakerChannel - INFO - 策略主循环启动
2025-09-28 08:31:04 - MakerChannel - INFO - 执行轻量启动预热（仅基础信息）...
2025-09-28 08:31:05 - MakerChannel - INFO - 启动时无活跃持仓
2025-09-28 08:31:06 - MakerChannel - INFO - 交易所有效交易对数量: 501, 过滤后有效交易对数量: 490
2025-09-28 08:31:06 - MakerChannel - INFO - 预热完成，候选池包含 43 个交易对
2025-09-28 08:31:06 - MakerChannel - INFO - 异步评分批次 1: 处理 20 个币种 [1-20]
2025-09-28 08:31:06 - MakerChannel - INFO - 开始全市场扫描，寻找新的通道突破币种...
2025-09-28 08:31:06 - MakerChannel - INFO - 增强评分批次 1: 处理 20 个币种 [1-20]
2025-09-28 08:31:06 - MakerChannel - INFO - 全市场扫描开始：总币种数量 500，当前候选池 43 个
2025-09-28 08:31:06 - MakerChannel - ERROR - 获取USDCUSDT的K线数据时发生错误: maximum recursion depth exceeded while calling a Python object
2025-09-28 08:31:06 - MakerChannel - INFO - 扫描批次 1/25: 20 个币种
2025-09-28 08:31:06 - MakerChannel - ERROR - 代理服务器不可用
2025-09-28 08:31:06 - MakerChannel - ERROR - 代理服务器不可用
2025-09-28 08:31:06 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第1次重试
2025-09-28 08:31:06 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第1次重试
2025-09-28 08:31:06 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第1次重试
2025-09-28 08:31:07 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第2次重试
2025-09-28 08:31:07 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第2次重试
2025-09-28 08:31:07 - MakerChannel - ERROR - 内存监控异常: 'MakerChannelStrategy' object has no attribute 'memory_check_interval'
2025-09-28 08:31:07 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第2次重试
2025-09-28 08:31:08 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第3次重试
2025-09-28 08:31:08 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第3次重试
2025-09-28 08:31:08 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第3次重试
2025-09-28 08:31:09 - MakerChannel - ERROR - 获取BCHUSDT的K线数据失败，已重试3次
2025-09-28 08:31:09 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第1次重试
2025-09-28 08:31:09 - MakerChannel - ERROR - 获取USDCUSDT的K线数据失败，已重试3次
2025-09-28 08:31:09 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第1次重试
2025-09-28 08:31:09 - MakerChannel - ERROR - 获取MYXUSDT的K线数据失败，已重试3次
2025-09-28 08:31:09 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第1次重试
2025-09-28 08:31:10 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第2次重试
2025-09-28 08:31:10 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第2次重试
2025-09-28 08:31:10 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第2次重试
2025-09-28 08:31:11 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第3次重试
2025-09-28 08:31:11 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第3次重试
2025-09-28 08:31:11 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第3次重试
2025-09-28 08:31:12 - MakerChannel - ERROR - 获取BCHUSDT的K线数据失败，已重试3次
2025-09-28 08:31:12 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第1次重试
2025-09-28 08:31:12 - MakerChannel - ERROR - 获取USDCUSDT的K线数据失败，已重试3次
2025-09-28 08:31:12 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第1次重试
2025-09-28 08:31:12 - MakerChannel - ERROR - 获取MYXUSDT的K线数据失败，已重试3次
2025-09-28 08:31:12 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第1次重试
2025-09-28 08:31:13 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第2次重试
2025-09-28 08:31:13 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第2次重试
2025-09-28 08:31:13 - strategy_optimizer - WARNING - CPU使用率过高: 85.8%
2025-09-28 08:31:13 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第2次重试
2025-09-28 08:31:14 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第3次重试
2025-09-28 08:31:14 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第3次重试
2025-09-28 08:31:14 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第3次重试
2025-09-28 08:31:15 - MakerChannel - ERROR - 获取BCHUSDT的K线数据失败，已重试3次
2025-09-28 08:31:15 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第1次重试
2025-09-28 08:31:15 - MakerChannel - ERROR - 获取USDCUSDT的K线数据失败，已重试3次
2025-09-28 08:31:15 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第1次重试
2025-09-28 08:31:15 - MakerChannel - ERROR - 获取MYXUSDT的K线数据失败，已重试3次
2025-09-28 08:31:15 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第1次重试
2025-09-28 08:31:16 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第2次重试
2025-09-28 08:31:16 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第2次重试
2025-09-28 08:31:16 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第2次重试
2025-09-28 08:31:17 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第3次重试
2025-09-28 08:31:17 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第3次重试
2025-09-28 08:31:17 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第3次重试
2025-09-28 08:31:18 - MakerChannel - ERROR - 获取BCHUSDT的K线数据失败，已重试3次
2025-09-28 08:31:18 - MakerChannel - ERROR - 获取USDCUSDT的K线数据失败，已重试3次
2025-09-28 08:31:18 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第1次重试
2025-09-28 08:31:18 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第1次重试
2025-09-28 08:31:18 - MakerChannel - ERROR - 获取MYXUSDT的K线数据失败，已重试3次
2025-09-28 08:31:18 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第1次重试
2025-09-28 08:31:20 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第2次重试
2025-09-28 08:31:20 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第2次重试
2025-09-28 08:31:20 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第2次重试
2025-09-28 08:31:21 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第3次重试
2025-09-28 08:31:21 - MakerChannel - WARNING - 获取USDCUSDT的K线数据为空，第3次重试
2025-09-28 08:31:22 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第3次重试
2025-09-28 08:31:22 - MakerChannel - ERROR - 获取MYXUSDT的K线数据失败，已重试3次
2025-09-28 08:31:22 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第1次重试
2025-09-28 08:31:22 - MakerChannel - ERROR - 获取USDCUSDT的K线数据失败，已重试3次
2025-09-28 08:31:23 - MakerChannel - ERROR - 获取BCHUSDT的K线数据失败，已重试3次
2025-09-28 08:31:23 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第1次重试
2025-09-28 08:31:24 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第2次重试
2025-09-28 08:31:25 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第2次重试
2025-09-28 08:31:25 - MakerChannel - WARNING - 获取MYXUSDT的K线数据为空，第3次重试
2025-09-28 08:31:26 - MakerChannel - WARNING - 获取BCHUSDT的K线数据为空，第3次重试
2025-09-28 08:31:26 - MakerChannel - ERROR - 获取MYXUSDT的K线数据失败，已重试3次
2025-09-28 08:31:27 - MakerChannel - ERROR - 获取BCHUSDT的K线数据失败，已重试3次
2025-09-28 08:31:31 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProxyError('Cannot connect to proxy.', FileNotFoundError(2, 'No such file or directory'))': /fapi/v1/klines?symbol=USDCUSDT&interval=1d&limit=1000
