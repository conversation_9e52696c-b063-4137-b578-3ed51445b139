#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化器
实现主循环优化、性能提升和资源管理
"""

import asyncio
import time
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import weakref

@dataclass
class OptimizationConfig:
    """优化配置"""
    # 并发控制
    max_concurrent_tasks: int = 10
    max_worker_threads: int = 5
    
    # 缓存配置
    enable_caching: bool = True
    cache_ttl: int = 300  # 缓存生存时间（秒）
    max_cache_size: int = 1000
    
    # 批处理配置
    batch_size: int = 50
    batch_timeout: float = 5.0
    
    # 性能监控
    enable_profiling: bool = True
    performance_log_interval: int = 60
    
    # 资源限制
    max_memory_usage: float = 0.9  # 最大内存使用率（调整为90%）
    max_cpu_usage: float = 0.8     # 最大CPU使用率
    
    # 错误处理
    max_retries: int = 3
    retry_delay: float = 1.0
    circuit_breaker_threshold: int = 5

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {
            'task_count': 0,
            'success_count': 0,
            'error_count': 0,
            'total_execution_time': 0.0,
            'avg_execution_time': 0.0,
            'peak_memory': 0.0,
            'peak_cpu': 0.0
        }
        self.start_time = time.time()
        self.last_log_time = time.time()
        self.logger = logging.getLogger(__name__)
    
    def record_task(self, execution_time: float, success: bool = True):
        """记录任务执行"""
        self.metrics['task_count'] += 1
        self.metrics['total_execution_time'] += execution_time
        
        if success:
            self.metrics['success_count'] += 1
        else:
            self.metrics['error_count'] += 1
        
        # 计算平均执行时间
        if self.metrics['task_count'] > 0:
            self.metrics['avg_execution_time'] = (
                self.metrics['total_execution_time'] / self.metrics['task_count']
            )
    
    def update_resource_usage(self, memory_usage: float, cpu_usage: float):
        """更新资源使用情况"""
        self.metrics['peak_memory'] = max(self.metrics['peak_memory'], memory_usage)
        self.metrics['peak_cpu'] = max(self.metrics['peak_cpu'], cpu_usage)
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        uptime = time.time() - self.start_time
        success_rate = (
            self.metrics['success_count'] / self.metrics['task_count']
            if self.metrics['task_count'] > 0 else 0
        )
        
        return {
            **self.metrics,
            'uptime': uptime,
            'success_rate': success_rate,
            'tasks_per_second': self.metrics['task_count'] / uptime if uptime > 0 else 0
        }
    
    def should_log(self, interval: int = 60) -> bool:
        """检查是否应该记录日志"""
        current_time = time.time()
        if current_time - self.last_log_time >= interval:
            self.last_log_time = current_time
            return True
        return False

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 300):
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.access_times = {}
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # 检查是否过期
            if time.time() - self.access_times[key] > self.ttl:
                del self.cache[key]
                del self.access_times[key]
                return None
            
            # 更新访问时间
            self.access_times[key] = time.time()
            return self.cache[key]
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self.lock:
            # 检查缓存大小限制
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_oldest()
            
            self.cache[key] = value
            self.access_times[key] = time.time()
    
    def _evict_oldest(self, evict_count: int = None):
        """淘汰最旧的缓存项"""
        if not self.access_times:
            return
        
        # 如果没有指定清理数量，默认清理25%的缓存
        if evict_count is None:
            evict_count = max(1, len(self.cache) // 4)
        
        # 获取按访问时间排序的键列表
        sorted_keys = sorted(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # 清理最旧的项目
        for key in sorted_keys[:evict_count]:
            del self.cache[key]
            del self.access_times[key]
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)

class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 50, timeout: float = 5.0):
        self.batch_size = batch_size
        self.timeout = timeout
        self.batch_queue = queue.Queue()
        self.result_futures = {}
        self.logger = logging.getLogger(__name__)
        
        # 启动批处理线程
        self.processing_thread = threading.Thread(target=self._process_batches, daemon=True)
        self.processing_thread.start()
    
    def submit(self, task_id: str, data: Any) -> 'BatchFuture':
        """提交批处理任务"""
        future = BatchFuture()
        self.result_futures[task_id] = future
        self.batch_queue.put((task_id, data, time.time()))
        return future
    
    def _process_batches(self):
        """处理批次"""
        batch = []
        last_process_time = time.time()
        
        while True:
            try:
                # 收集批次数据
                while len(batch) < self.batch_size:
                    try:
                        timeout = max(0.1, self.timeout - (time.time() - last_process_time))
                        item = self.batch_queue.get(timeout=timeout)
                        batch.append(item)
                    except queue.Empty:
                        break
                
                # 处理批次
                if batch and (len(batch) >= self.batch_size or 
                             time.time() - last_process_time >= self.timeout):
                    self._execute_batch(batch)
                    batch = []
                    last_process_time = time.time()
                
            except Exception as e:
                self.logger.error(f"批处理异常: {e}")
                time.sleep(1)
    
    def _execute_batch(self, batch: List[tuple]):
        """执行批次"""
        try:
            # 这里可以实现具体的批处理逻辑
            # 例如批量API调用、批量数据库操作等
            
            for task_id, data, submit_time in batch:
                if task_id in self.result_futures:
                    # 模拟处理结果
                    result = {'processed': True, 'data': data, 'processing_time': time.time() - submit_time}
                    self.result_futures[task_id].set_result(result)
                    del self.result_futures[task_id]
            
            self.logger.debug(f"批处理完成，处理了 {len(batch)} 个任务")
            
        except Exception as e:
            self.logger.error(f"批处理执行失败: {e}")
            
            # 设置错误结果
            for task_id, _, _ in batch:
                if task_id in self.result_futures:
                    self.result_futures[task_id].set_exception(e)
                    del self.result_futures[task_id]

class BatchFuture:
    """批处理Future"""
    
    def __init__(self):
        self._result = None
        self._exception = None
        self._done = False
        self._condition = threading.Condition()
    
    def set_result(self, result: Any):
        """设置结果"""
        with self._condition:
            self._result = result
            self._done = True
            self._condition.notify_all()
    
    def set_exception(self, exception: Exception):
        """设置异常"""
        with self._condition:
            self._exception = exception
            self._done = True
            self._condition.notify_all()
    
    def result(self, timeout: Optional[float] = None) -> Any:
        """获取结果"""
        with self._condition:
            if not self._done:
                self._condition.wait(timeout)
            
            if self._exception:
                raise self._exception
            
            return self._result
    
    def done(self) -> bool:
        """检查是否完成"""
        return self._done

class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """通过熔断器调用函数"""
        with self.lock:
            if self.state == 'OPEN':
                if (time.time() - self.last_failure_time) > self.recovery_timeout:
                    self.state = 'HALF_OPEN'
                else:
                    raise Exception("熔断器开启，拒绝调用")
            
            try:
                result = func(*args, **kwargs)
                
                # 成功调用，重置计数器
                if self.state == 'HALF_OPEN':
                    self.state = 'CLOSED'
                self.failure_count = 0
                
                return result
                
            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = time.time()
                
                if self.failure_count >= self.failure_threshold:
                    self.state = 'OPEN'
                
                raise e

class StrategyOptimizer:
    """策略优化器"""
    
    def __init__(self, config: OptimizationConfig = None):
        """
        初始化策略优化器
        
        Args:
            config: 优化配置
        """
        self.config = config or OptimizationConfig()
        self.logger = logging.getLogger(__name__)
        
        # 组件初始化
        self.performance_monitor = PerformanceMonitor()
        self.cache_manager = CacheManager(
            max_size=self.config.max_cache_size,
            ttl=self.config.cache_ttl
        ) if self.config.enable_caching else None
        
        self.batch_processor = BatchProcessor(
            batch_size=self.config.batch_size,
            timeout=self.config.batch_timeout
        )
        
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=self.config.circuit_breaker_threshold
        )
        
        # 线程池
        self.executor = ThreadPoolExecutor(
            max_workers=self.config.max_worker_threads,
            thread_name_prefix="StrategyOptimizer"
        )
        
        # 任务队列
        self.task_queue = asyncio.Queue(maxsize=self.config.max_concurrent_tasks)
        self.running_tasks = set()
        
        # 资源监控
        self.resource_monitor_thread = None
        self.running = False
    
    def start(self):
        """启动优化器"""
        self.running = True
        
        # 启动资源监控
        if self.config.enable_profiling:
            self.resource_monitor_thread = threading.Thread(
                target=self._monitor_resources, daemon=True
            )
            self.resource_monitor_thread.start()
        
        self.logger.info("策略优化器启动")
    
    def stop(self):
        """停止优化器"""
        self.running = False
        
        # 等待所有任务完成
        for task in list(self.running_tasks):
            if not task.done():
                task.cancel()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("策略优化器停止")
    
    def _monitor_resources(self):
        """监控资源使用"""
        while self.running:
            try:
                import psutil
                
                # 获取资源使用情况
                memory_usage = psutil.virtual_memory().percent / 100
                cpu_usage = psutil.cpu_percent() / 100
                
                # 更新监控指标
                self.performance_monitor.update_resource_usage(memory_usage, cpu_usage)
                
                # 检查资源限制
                if memory_usage > self.config.max_memory_usage:
                    self.logger.warning(f"内存使用率过高: {memory_usage:.1%}")
                    self._handle_high_resource_usage('memory')
                
                if cpu_usage > self.config.max_cpu_usage:
                    self.logger.warning(f"CPU使用率过高: {cpu_usage:.1%}")
                    self._handle_high_resource_usage('cpu')
                
                # 记录性能日志
                if self.performance_monitor.should_log(self.config.performance_log_interval):
                    metrics = self.performance_monitor.get_metrics()
                    self.logger.info(f"性能指标: {metrics}")
                
                time.sleep(5)
                
            except ImportError:
                # 没有psutil，跳过资源监控
                time.sleep(30)
            except Exception as e:
                self.logger.error(f"资源监控异常: {e}")
                time.sleep(10)
    
    def _handle_high_resource_usage(self, resource_type: str):
        """处理高资源使用"""
        if resource_type == 'memory':
            # 只在内存使用率极高时才清理缓存，并且限制清理频率
            current_time = time.time()
            if not hasattr(self, '_last_cache_clear_time'):
                self._last_cache_clear_time = 0
            
            # 至少间隔30秒才能再次清理缓存
            if current_time - self._last_cache_clear_time > 30:
                if self.cache_manager:
                    # 只清理一半的缓存，而不是全部清理
                    cache_size_before = self.cache_manager.size()
                    self.cache_manager._evict_oldest()  # 只清理最旧的缓存
                    cache_size_after = self.cache_manager.size()
                    self.logger.info(f"部分清理缓存以释放内存: {cache_size_before} -> {cache_size_after}")
                    self._last_cache_clear_time = current_time
        
        elif resource_type == 'cpu':
            # 减少并发任务
            if len(self.running_tasks) > self.config.max_concurrent_tasks // 2:
                self.logger.info("减少并发任务以降低CPU使用")
    
    async def execute_optimized(self, func: Callable, *args, use_cache: bool = True, 
                               cache_key: str = None, **kwargs) -> Any:
        """
        优化执行函数
        
        Args:
            func: 要执行的函数
            args: 函数参数
            use_cache: 是否使用缓存
            cache_key: 缓存键
            kwargs: 函数关键字参数
        
        Returns:
            函数执行结果
        """
        start_time = time.time()
        
        try:
            # 检查缓存
            if use_cache and self.cache_manager and cache_key:
                cached_result = self.cache_manager.get(cache_key)
                if cached_result is not None:
                    self.logger.debug(f"缓存命中: {cache_key}")
                    return cached_result
            
            # 通过熔断器执行
            result = await self._execute_with_retry(func, *args, **kwargs)
            
            # 缓存结果
            if use_cache and self.cache_manager and cache_key:
                self.cache_manager.set(cache_key, result)
            
            # 记录性能
            execution_time = time.time() - start_time
            self.performance_monitor.record_task(execution_time, success=True)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_task(execution_time, success=False)
            self.logger.error(f"优化执行失败: {e}")
            raise
    
    async def _execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """带重试的执行"""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                # 如果是协程函数
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    # 在线程池中执行同步函数
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(self.executor, func, *args, **kwargs)
                    
            except Exception as e:
                last_exception = e
                
                if attempt < self.config.max_retries:
                    delay = self.config.retry_delay * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"执行失败，{delay}秒后重试 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {e}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"执行失败，已达最大重试次数: {e}")
        
        raise last_exception
    
    def submit_batch_task(self, task_id: str, data: Any) -> BatchFuture:
        """提交批处理任务"""
        return self.batch_processor.submit(task_id, data)
    
    async def parallel_execute(self, tasks: List[tuple], max_concurrent: int = None) -> List[Any]:
        """
        并行执行任务
        
        Args:
            tasks: 任务列表，每个元素为 (func, args, kwargs)
            max_concurrent: 最大并发数
        
        Returns:
            执行结果列表
        """
        max_concurrent = max_concurrent or self.config.max_concurrent_tasks
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_task(task_info):
            task_name = "unknown"
            try:
                async with semaphore:
                    func, args, kwargs = task_info
                    task_name = func.__name__ if hasattr(func, '__name__') else str(func)
                    self.logger.debug(f"开始执行任务: {task_name}")
                    result = await self.execute_optimized(func, *args, **kwargs)
                    self.logger.debug(f"任务执行完成: {task_name}")
                    return result
            except asyncio.CancelledError:
                self.logger.warning(f"任务被取消: {task_name}")
                return None  # 返回None而不是重新抛出异常
            except Exception as e:
                self.logger.error(f"任务执行失败: {task_name}, 错误: {e}", exc_info=True)
                return None
        
        # 创建任务
        coroutines = [execute_task(task) for task in tasks]
        
        try:
            # 并行执行，设置超时
            results = await asyncio.wait_for(
                asyncio.gather(*coroutines, return_exceptions=True),
                timeout=300  # 5分钟超时
            )
            
            # 过滤掉异常结果
            valid_results = []
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f"任务返回异常: {result}")
                    valid_results.append(None)
                else:
                    valid_results.append(result)
            
            return valid_results
            
        except asyncio.TimeoutError:
            self.logger.error("并行任务执行超时")
            return [None] * len(tasks)
        except Exception as e:
            self.logger.error(f"并行任务执行失败: {e}", exc_info=True)
            return [None] * len(tasks)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        metrics = self.performance_monitor.get_metrics()
        
        # 添加缓存指标
        if self.cache_manager:
            metrics['cache_size'] = self.cache_manager.size()
            metrics['cache_hit_rate'] = getattr(self.cache_manager, 'hit_rate', 0)
        
        # 添加任务队列指标
        metrics['running_tasks'] = len(self.running_tasks)
        metrics['queue_size'] = self.task_queue.qsize() if hasattr(self.task_queue, 'qsize') else 0
        
        return metrics
    
    def optimize_strategy_loop(self, strategy_func: Callable) -> Callable:
        """
        优化策略主循环
        
        Args:
            strategy_func: 原始策略函数
        
        Returns:
            优化后的策略函数
        """
        async def optimized_loop(*args, **kwargs):
            """优化的策略循环"""
            try:
                # 预热缓存
                await self._warmup_cache()
                
                # 执行优化的策略逻辑
                result = await self.execute_optimized(
                    strategy_func, 
                    *args, 
                    use_cache=True,
                    cache_key=f"strategy_{hash(str(args) + str(kwargs))}",
                    **kwargs
                )
                
                return result
                
            except Exception as e:
                self.logger.error(f"优化策略循环异常: {e}")
                raise
        
        return optimized_loop
    
    async def _warmup_cache(self):
        """预热缓存"""
        if not self.cache_manager:
            return
        
        try:
            # 这里可以实现缓存预热逻辑
            # 例如预加载常用数据、配置等
            self.logger.debug("缓存预热完成")
            
        except Exception as e:
            self.logger.error(f"缓存预热失败: {e}")


# 全局优化器实例
_strategy_optimizer = None

def get_strategy_optimizer() -> StrategyOptimizer:
    """获取全局优化器实例"""
    global _strategy_optimizer
    if _strategy_optimizer is None:
        _strategy_optimizer = StrategyOptimizer()
    return _strategy_optimizer

def init_strategy_optimizer(config: OptimizationConfig = None) -> StrategyOptimizer:
    """初始化全局优化器实例"""
    global _strategy_optimizer
    _strategy_optimizer = StrategyOptimizer(config)
    return _strategy_optimizer