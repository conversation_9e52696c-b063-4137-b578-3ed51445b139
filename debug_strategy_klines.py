#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略层面K线数据获取调试脚本
专门测试MakerChannelStrategy的get_klines方法
"""

import sys
import json
import time
import logging
from datetime import datetime

def debug_strategy_klines():
    """调试策略层面的K线数据获取"""
    print("开始调试策略层面的K线数据获取...")
    
    try:
        # 1. 导入必要模块
        print("1. 导入模块...")
        from binance_trader import BinanceTrader
        from strategy.maker_channel import MakerChannelStrategy
        
        # 2. 加载配置
        print("2. 加载配置...")
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 添加策略需要的配置参数
        config['first_nominal'] = 100
        config['order_ttl'] = 300
        config['network_check_interval'] = 60
        config['max_symbol_failures'] = 5
        config['failure_cooldown'] = 3600
        config['network_error_cooldown'] = 300
        
        # 3. 初始化交易器和策略
        print("3. 初始化BinanceTrader和策略...")
        trader = BinanceTrader(config)
        
        # 临时禁用日志输出，避免刷屏
        logging.getLogger('MakerChannel').setLevel(logging.ERROR)
        
        try:
            strategy = MakerChannelStrategy(trader, config)
            print("   ✅ 策略初始化成功")
        except Exception as e:
            print(f"   ❌ 策略初始化失败: {e}")
            return
        
        # 4. 测试问题币种
        problem_symbols = ['ALPINEUSDT', 'BCHUSDT', 'EGLDUSDT']
        
        print("\n4. 测试策略层面的K线数据获取...")
        for symbol in problem_symbols:
            print(f"\n--- 测试策略获取 {symbol} ---")
            
            try:
                # 测试策略的get_klines方法
                start_time = time.time()
                
                # 临时启用DEBUG日志来看详细过程
                strategy_logger = logging.getLogger('MakerChannel')
                original_level = strategy_logger.level
                strategy_logger.setLevel(logging.DEBUG)
                
                # 添加控制台处理器来查看日志
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.DEBUG)
                formatter = logging.Formatter('%(levelname)s - %(message)s')
                console_handler.setFormatter(formatter)
                strategy_logger.addHandler(console_handler)
                
                klines_df = strategy.get_klines(symbol, '15m', 20)
                
                # 恢复日志级别
                strategy_logger.setLevel(original_level)
                strategy_logger.removeHandler(console_handler)
                
                end_time = time.time()
                
                print(f"   策略响应时间: {end_time - start_time:.2f}秒")
                print(f"   策略响应类型: {type(klines_df)}")
                
                if klines_df is not None:
                    if hasattr(klines_df, '__len__'):
                        print(f"   ✅ 策略返回数据，长度: {len(klines_df)}")
                        if hasattr(klines_df, 'columns'):
                            print(f"   DataFrame列: {list(klines_df.columns)}")
                    else:
                        print(f"   ✅ 策略返回数据: {klines_df}")
                else:
                    print(f"   ❌ 策略返回None")
                    
            except Exception as e:
                print(f"   ❌ 策略调用异常: {e}")
                import traceback
                traceback.print_exc()
            
            time.sleep(1)  # 避免请求过快
        
        # 5. 测试缓存机制
        print("\n5. 测试缓存机制...")
        test_symbol = 'BTCUSDT'
        
        # 第一次调用（应该从API获取）
        print(f"   第一次调用 {test_symbol}...")
        start_time = time.time()
        klines1 = strategy.get_klines(test_symbol, '15m', 10)
        end_time = time.time()
        print(f"   第一次耗时: {end_time - start_time:.2f}秒")
        
        # 第二次调用（应该从缓存获取）
        print(f"   第二次调用 {test_symbol}...")
        start_time = time.time()
        klines2 = strategy.get_klines(test_symbol, '15m', 10)
        end_time = time.time()
        print(f"   第二次耗时: {end_time - start_time:.2f}秒")
        
        if klines1 is not None and klines2 is not None:
            print(f"   缓存效果: 第一次{len(klines1)}条，第二次{len(klines2)}条")
        
        # 6. 检查策略的网络状态检查
        print("\n6. 测试策略的网络状态检查...")
        try:
            network_status = strategy._check_network_status()
            print(f"   网络状态: {network_status}")
        except Exception as e:
            print(f"   网络状态检查异常: {e}")
        
        print("\n🔍 策略层面调试完成！")
        
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_strategy_klines()