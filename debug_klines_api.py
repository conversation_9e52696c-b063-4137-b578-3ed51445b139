#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试K线API问题的测试脚本
直接测试Binance API返回的K线数据
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from binance_trader import BinanceTrader
from http_client import HttpClient

def test_klines_api():
    """测试K线API的直接调用"""
    print("=== K线API调试测试 ===")
    
    # 加载配置
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✓ 配置文件加载成功")
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return
    
    # 初始化交易器
    try:
        trader = BinanceTrader(config)
        print("✓ BinanceTrader初始化成功")
    except Exception as e:
        print(f"✗ BinanceTrader初始化失败: {e}")
        return
    
    # 测试币种列表
    test_symbols = ['BCHUSDT', 'BTCUSDT', 'ETHUSDT', 'AKEUSDT', 'SSVUSDT']
    
    for symbol in test_symbols:
        print(f"\n--- 测试 {symbol} ---")
        
        # 方法1: 使用trader.get_klines
        try:
            print("方法1: 使用 trader.get_klines()")
            klines1 = trader.get_klines(symbol, '15m', 50)
            print(f"  返回类型: {type(klines1)}")
            print(f"  数据长度: {len(klines1) if isinstance(klines1, list) else 'N/A'}")
            if isinstance(klines1, list) and len(klines1) > 0:
                print(f"  第一条数据: {klines1[0]}")
                print(f"  最后一条数据: {klines1[-1]}")
            else:
                print(f"  返回内容: {klines1}")
        except Exception as e:
            print(f"  ✗ 方法1失败: {e}")
        
        # 方法2: 直接使用http.get
        try:
            print("方法2: 直接使用 http.get()")
            klines2 = trader.http.get('/fapi/v1/klines', {
                'symbol': symbol,
                'interval': '15m',
                'limit': 50
            })
            print(f"  返回类型: {type(klines2)}")
            print(f"  数据长度: {len(klines2) if isinstance(klines2, list) else 'N/A'}")
            if isinstance(klines2, list) and len(klines2) > 0:
                print(f"  第一条数据: {klines2[0]}")
                print(f"  最后一条数据: {klines2[-1]}")
            else:
                print(f"  返回内容: {klines2}")
        except Exception as e:
            print(f"  ✗ 方法2失败: {e}")
        
        # 方法3: 测试不同的limit值
        try:
            print("方法3: 测试不同limit值")
            for limit in [20, 50, 100, 200]:
                klines3 = trader.http.get('/fapi/v1/klines', {
                    'symbol': symbol,
                    'interval': '15m',
                    'limit': limit
                })
                if isinstance(klines3, list):
                    print(f"  limit={limit}: 返回{len(klines3)}条数据")
                else:
                    print(f"  limit={limit}: 返回错误 - {klines3}")
        except Exception as e:
            print(f"  ✗ 方法3失败: {e}")
        
        print("-" * 50)
        time.sleep(1)  # 避免API频率限制

def test_api_response_format():
    """测试API响应格式"""
    print("\n=== API响应格式测试 ===")
    
    # 加载配置
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"配置加载失败: {e}")
        return
    
    # 创建HTTP客户端
    network_params = config.get('network_params', {})
    api_key = network_params.get('api_key', '')
    api_secret = network_params.get('api_secret', '')
    
    if not api_key or not api_secret:
        print("API密钥未配置")
        return
    
    http_client = HttpClient(
        api_key=api_key,
        api_secret=api_secret,
        base_url='https://fapi.binance.com',
        timeout=30,
        proxy_cfg=config.get('proxy', {}),
        verify_ssl=False
    )
    
    # 测试单个请求的详细响应
    symbol = 'BCHUSDT'
    print(f"测试 {symbol} 的详细API响应:")
    
    response = http_client.get('/fapi/v1/klines', {
        'symbol': symbol,
        'interval': '15m',
        'limit': 20
    })
    
    print(f"响应类型: {type(response)}")
    print(f"响应内容: {response}")
    
    if isinstance(response, list):
        print(f"数据条数: {len(response)}")
        if len(response) > 0:
            print(f"单条数据格式: {response[0]}")
            print(f"数据字段数: {len(response[0]) if isinstance(response[0], list) else 'N/A'}")
    elif isinstance(response, dict):
        print("返回字典格式，可能是错误信息:")
        for key, value in response.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    test_klines_api()
    test_api_response_format()