"""
动态时间框架辅助模块
为通道突破策略提供基于币龄的动态时间周期计算
"""

def dynamic_tf_for_channel(age_days):
    """
    为通道突破计算动态时间周期的K线数量
    
    Args:
        age_days (float): 币龄（天数）
        
    Returns:
        int: 通道周期的K线数量
    """
    if age_days <= 1:
        return 8   # 新币：8根K线（约2小时）
    elif age_days <= 3:
        return 12  # 较新币：12根K线（约3小时）
    elif age_days <= 7:
        return 16  # 一周内：16根K线（约4小时）
    elif age_days <= 30:
        return 24  # 一月内：24根K线（约6小时）
    elif age_days <= 365:
        return 30  # 一年内：30根K线（约7.5小时）
    else:
        return 36  # 老币：36根K线（约9小时）

def dynamic_tf_for_scoring(age_days):
    """
    为评分系统计算动态时间框架
    
    Args:
        age_days (float): 币龄（天数）
        
    Returns:
        tuple: (大趋势周期, 入场周期, 出场周期) - 时间字符串格式
    """
    if age_days <= 1:
        return '15m', '5m', '3m'
    elif age_days <= 3:
        return '1h', '15m', '5m'
    elif age_days <= 7:
        return '3h', '1h', '15m'
    elif age_days <= 30:
        return '4h', '3h', '1h'
    elif age_days <= 60:
        return '6h', '4h', '3h'
    else:
        return '1d', '6h', '4h'