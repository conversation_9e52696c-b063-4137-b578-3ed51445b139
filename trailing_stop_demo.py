#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动止损功能演示脚本
展示移动止损在实际交易场景中的应用
"""

import logging
import time
import random
from datetime import datetime
from typing import List, Dict

from trailing_stop_manager import TrailingStopManager, TrailingStopState
from config_validator import ConfigValidator


class MockPriceGenerator:
    """模拟价格生成器"""
    
    def __init__(self, initial_price: float, volatility: float = 0.02):
        self.current_price = initial_price
        self.volatility = volatility
        self.trend = 1  # 1为上涨，-1为下跌
        self.trend_strength = 0.001
    
    def next_price(self) -> float:
        """生成下一个价格"""
        # 随机波动
        random_change = random.gauss(0, self.volatility)
        
        # 趋势影响
        trend_change = self.trend * self.trend_strength
        
        # 计算新价格
        price_change = random_change + trend_change
        self.current_price *= (1 + price_change)
        
        # 随机改变趋势
        if random.random() < 0.05:  # 5%概率改变趋势
            self.trend *= -1
            self.trend_strength = random.uniform(0.0005, 0.002)
        
        return self.current_price


class TrailingStopDemo:
    """移动止损演示类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        # 初始化移动止损管理器
        self.manager = TrailingStopManager(logger=self.logger)
        
        # 模拟持仓
        self.positions: Dict[str, Dict] = {}
        
        # 价格生成器
        self.price_generators: Dict[str, MockPriceGenerator] = {}
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('trailing_stop_demo.log'),
                logging.StreamHandler()
            ]
        )
    
    def create_position(self, symbol: str, side: str, entry_price: float, quantity: float):
        """创建模拟持仓"""
        try:
            # 初始化价格生成器
            self.price_generators[symbol] = MockPriceGenerator(entry_price)
            
            # 初始化移动止损状态
            trailing_state = self.manager.initialize_position(
                symbol=symbol,
                side=side,
                entry_price=entry_price,
                current_price=entry_price
            )
            
            # 创建持仓记录
            self.positions[symbol] = {
                'symbol': symbol,
                'side': side,
                'entry_price': entry_price,
                'quantity': quantity,
                'current_price': entry_price,
                'trailing_state': trailing_state,
                'is_active': True,
                'created_at': datetime.now()
            }
            
            self.logger.info(f"创建持仓: {symbol} {side} {quantity} @ {entry_price:.6f}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建持仓失败: {e}")
            return False
    
    def update_position_price(self, symbol: str) -> float:
        """更新持仓价格"""
        if symbol not in self.price_generators:
            return 0.0
        
        new_price = self.price_generators[symbol].next_price()
        if symbol in self.positions:
            self.positions[symbol]['current_price'] = new_price
        
        return new_price
    
    def check_trailing_stop(self, symbol: str) -> bool:
        """检查移动止损"""
        if symbol not in self.positions or not self.positions[symbol]['is_active']:
            return False
        
        position = self.positions[symbol]
        trailing_state = position['trailing_state']
        
        if not trailing_state:
            return False
        
        try:
            # 更新移动止损
            updated, new_stop_price = self.manager.update_trailing_stop(
                state=trailing_state,
                current_price=position['current_price']
            )
            
            if updated:
                self.logger.info(f"{symbol} 移动止损更新: 新止损价 {new_stop_price:.6f}")
            
            # 检查是否触发止损
            triggered = self.manager.check_stop_triggered(
                state=trailing_state,
                current_price=position['current_price']
            )
            
            if triggered:
                self.close_position(symbol, "移动止损触发")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"{symbol} 移动止损检查失败: {e}")
            return False
    
    def close_position(self, symbol: str, reason: str = "手动平仓"):
        """平仓"""
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        if not position['is_active']:
            return
        
        # 计算盈亏
        entry_price = position['entry_price']
        current_price = position['current_price']
        side = position['side']
        quantity = position['quantity']
        
        if side == "LONG":
            pnl_pct = (current_price - entry_price) / entry_price
        else:
            pnl_pct = (entry_price - current_price) / entry_price
        
        pnl_amount = pnl_pct * entry_price * quantity
        
        # 标记为非活跃
        position['is_active'] = False
        position['close_price'] = current_price
        position['close_reason'] = reason
        position['pnl_pct'] = pnl_pct
        position['pnl_amount'] = pnl_amount
        position['closed_at'] = datetime.now()
        
        # 移除移动止损状态
        self.manager.remove_position(symbol)
        
        self.logger.info(f"平仓: {symbol} @ {current_price:.6f}, "
                        f"盈亏: {pnl_pct:.2%} ({pnl_amount:.2f}), "
                        f"原因: {reason}")
    
    def print_position_status(self, symbol: str):
        """打印持仓状态"""
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        if not position['is_active']:
            return
        
        trailing_state = position['trailing_state']
        current_price = position['current_price']
        entry_price = position['entry_price']
        side = position['side']
        
        # 计算当前盈亏
        if side == "LONG":
            pnl_pct = (current_price - entry_price) / entry_price
        else:
            pnl_pct = (entry_price - current_price) / entry_price
        
        # 移动止损信息
        trailing_info = "未激活"
        if trailing_state and trailing_state.is_activated:
            trailing_info = f"已激活 (止损价: {trailing_state.current_stop_price:.6f}, 移动次数: {trailing_state.move_count})"
        
        print(f"{symbol}: 当前价 {current_price:.6f}, "
              f"盈亏 {pnl_pct:.2%}, "
              f"移动止损 {trailing_info}")
    
    def run_simulation(self, duration_minutes: int = 30, update_interval: float = 1.0):
        """运行模拟交易"""
        print(f"开始移动止损演示，持续 {duration_minutes} 分钟")
        print("=" * 80)
        
        # 创建一些测试持仓
        test_positions = [
            ("BTCUSDT", "LONG", 50000.0, 0.1),
            ("ETHUSDT", "LONG", 3000.0, 1.0),
            ("ADAUSDT", "SHORT", 0.5, 1000.0)
        ]
        
        for symbol, side, price, quantity in test_positions:
            self.create_position(symbol, side, price, quantity)
        
        start_time = datetime.now()
        end_time = start_time.timestamp() + (duration_minutes * 60)
        
        step = 0
        try:
            while datetime.now().timestamp() < end_time:
                step += 1
                
                # 更新所有持仓价格
                for symbol in list(self.positions.keys()):
                    if self.positions[symbol]['is_active']:
                        self.update_position_price(symbol)
                        self.check_trailing_stop(symbol)
                
                # 每10步打印一次状态
                if step % 10 == 0:
                    print(f"\n--- 第 {step} 步 ---")
                    for symbol in self.positions:
                        self.print_position_status(symbol)
                
                # 检查是否还有活跃持仓
                active_positions = [p for p in self.positions.values() if p['is_active']]
                if not active_positions:
                    print("\n所有持仓已平仓，演示结束")
                    break
                
                time.sleep(update_interval)
                
        except KeyboardInterrupt:
            print("\n用户中断演示")
        
        # 强制平仓剩余持仓
        for symbol in list(self.positions.keys()):
            if self.positions[symbol]['is_active']:
                self.close_position(symbol, "演示结束")
        
        self.print_summary()
    
    def print_summary(self):
        """打印演示摘要"""
        print("\n" + "=" * 80)
        print("演示摘要")
        print("=" * 80)
        
        total_positions = len(self.positions)
        profitable_positions = len([p for p in self.positions.values() if p.get('pnl_pct', 0) > 0])
        
        print(f"总持仓数: {total_positions}")
        print(f"盈利持仓: {profitable_positions}")
        print(f"胜率: {profitable_positions/total_positions:.1%}" if total_positions > 0 else "胜率: N/A")
        
        print("\n持仓详情:")
        for symbol, position in self.positions.items():
            if 'pnl_pct' in position:
                print(f"{symbol}: {position['side']} "
                      f"入场 {position['entry_price']:.6f} -> "
                      f"出场 {position['close_price']:.6f}, "
                      f"盈亏 {position['pnl_pct']:.2%}, "
                      f"原因: {position['close_reason']}")
        
        # 获取移动止损统计
        daily_summary = self.manager.get_daily_summary()
        performance_metrics = self.manager.get_performance_metrics()
        
        print(f"\n移动止损统计:")
        print(f"激活次数: {daily_summary.get('total_activations', 0)}")
        print(f"更新次数: {daily_summary.get('total_updates', 0)}")
        print(f"触发次数: {daily_summary.get('total_triggers', 0)}")
        print(f"成功率: {performance_metrics.get('success_rate', 0):.1%}")


def main():
    """主函数"""
    print("移动止损功能演示")
    print("=" * 50)
    
    demo = TrailingStopDemo()
    
    try:
        # 运行演示
        demo.run_simulation(duration_minutes=5, update_interval=0.5)
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        logging.exception("演示异常")


if __name__ == "__main__":
    main()