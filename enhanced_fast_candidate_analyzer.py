#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量版本快速候选池评分检测脚本
与实时系统的评分规则保持一致，用于快速验证实时系统的管理和评分功能
"""

import pandas as pd
import numpy as np
import pickle
import json
import logging
import time
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_score_calculator import EnhancedScoreCalculator, ScoreWeights

class EnhancedFastCandidateAnalyzer:
    """增量版本快速候选池分析器 - 与实时系统评分规则一致"""
    
    def __init__(self, cache_dir='cache'):
        """初始化分析器"""
        self.cache_dir = cache_dir
        self.logger = logging.getLogger('enhanced_fast_analyzer')
        self.logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        # 使用与实时系统相同的评分计算器配置
        self.score_calculator = EnhancedScoreCalculator()
        
        # 实时系统的评分阈值（从配置文件读取）
        self.passing_score = 12.0
        
        self.logger.info("增量版本快速候选池分析器初始化完成")
    
    def load_candidate_cache(self):
        """加载候选池缓存"""
        cache_file = os.path.join(self.cache_dir, 'candidates.pkl')
        
        if not os.path.exists(cache_file):
            self.logger.error(f"候选池缓存文件不存在: {cache_file}")
            return {}
        
        try:
            with open(cache_file, 'rb') as f:
                candidates = pickle.load(f)
            
            self.logger.info(f"成功加载候选池缓存，包含 {len(candidates)} 个币种")
            return candidates
        
        except Exception as e:
            self.logger.error(f"加载候选池缓存失败: {e}")
            return {}
    
    def load_cached_klines(self, symbol, timeframe='15m', limit=200):
        """加载缓存的K线数据"""
        cache_file = os.path.join(self.cache_dir, f'klines_{symbol}_{timeframe}_{limit}.pkl')
        
        if not os.path.exists(cache_file):
            return None
        
        try:
            with open(cache_file, 'rb') as f:
                klines = pickle.load(f)
            return klines
        except Exception as e:
            self.logger.warning(f"加载K线缓存失败 {symbol}: {e}")
            return None
    
    def load_cached_depth(self, symbol):
        """加载缓存的深度数据"""
        cache_file = os.path.join(self.cache_dir, f'depth_{symbol}.json')
        
        if not os.path.exists(cache_file):
            return None
        
        try:
            with open(cache_file, 'r') as f:
                depth_data = json.load(f)
            
            # 提取深度值
            if isinstance(depth_data, dict):
                return depth_data.get('depth', 0)
            elif isinstance(depth_data, (int, float)):
                return float(depth_data)
            else:
                return 0
        except Exception as e:
            self.logger.warning(f"加载深度缓存失败 {symbol}: {e}")
            return None
    
    def get_symbol_age_days(self, symbol):
        """计算币种年龄（天数）"""
        try:
            # 从symbols.json获取上市时间
            symbols_file = os.path.join(self.cache_dir, 'symbols.json')
            if os.path.exists(symbols_file):
                with open(symbols_file, 'r') as f:
                    symbols_data = json.load(f)
                
                for item in symbols_data:
                    if item.get('symbol') == symbol:
                        onboard_date = item.get('onboardDate')
                        if onboard_date:
                            # 转换时间戳为天数
                            onboard_time = datetime.fromtimestamp(onboard_date / 1000)
                            age_days = (datetime.now() - onboard_time).days
                            return max(1, age_days)
            
            # 默认返回500天（老币）
            return 500
            
        except Exception as e:
            self.logger.warning(f"计算币龄失败 {symbol}: {e}")
            return 500
    
    def analyze_single_symbol(self, symbol):
        """分析单个币种 - 使用与实时系统相同的评分逻辑"""
        try:
            # 加载K线数据
            klines_15m = self.load_cached_klines(symbol, '15m', 200)
            if klines_15m is None or len(klines_15m) < 50:
                self.logger.warning(f"{symbol}: K线数据不足")
                return None
            
            # 获取深度数据
            depth_data = self.load_cached_depth(symbol)
            
            # 获取币龄
            age_days = self.get_symbol_age_days(symbol)
            
            # 使用与实时系统相同的评分计算器
            score_result = self.score_calculator.calculate_comprehensive_score(
                symbol=symbol,
                df_data=klines_15m,
                depth_data=depth_data,
                additional_data={'age_days': age_days}
            )
            
            # 提取评分结果
            total_score = score_result.total_score
            component_scores = score_result.component_scores
            component_details = score_result.component_details
            
            # 获取当前价格 - 兼容不同列名格式
            close_col = 'close' if 'close' in klines_15m.columns else 'c'
            current_price = float(klines_15m[close_col].iloc[-1])
            
            # 构建结果
            result = {
                'symbol': symbol,
                'total_score': total_score,
                'component_scores': component_scores,
                'component_details': component_details,
                'current_price': current_price,
                'age_days': age_days,
                'depth': depth_data,
                'data_quality': score_result.data_quality,
                'calculation_time': score_result.calculation_time,
                'is_passing': total_score >= self.passing_score
            }
            
            self.logger.info(f"{symbol}: 总分={total_score:.2f}, 达标={'是' if result['is_passing'] else '否'}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"分析币种失败 {symbol}: {e}")
            return {
                'symbol': symbol,
                'total_score': 0.0,
                'error': str(e),
                'is_passing': False
            }
    
    def analyze_cached_candidates(self):
        """分析缓存的候选币种"""
        candidates = self.load_candidate_cache()
        if not candidates:
            return {
                'error': '候选池为空',
                'results': []
            }
        
        self.logger.info(f"开始分析 {len(candidates)} 个候选币种...")
        
        results = []
        success_count = 0
        passing_count = 0
        
        for symbol in candidates:
            result = self.analyze_single_symbol(symbol)
            if result:
                results.append(result)
                if 'error' not in result:
                    success_count += 1
                    if result.get('is_passing', False):
                        passing_count += 1
        
        # 按评分排序
        results.sort(key=lambda x: x.get('total_score', 0), reverse=True)
        
        analysis_result = {
            'total_candidates': len(candidates),
            'success_analyses': success_count,
            'passing_candidates': passing_count,
            'passing_rate': (passing_count / success_count * 100) if success_count > 0 else 0,
            'results': results,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.logger.info(f"分析完成: 总数={len(candidates)}, 成功={success_count}, 达标={passing_count}, 达标率={analysis_result['passing_rate']:.1f}%")
        
        return analysis_result
    
    def generate_detailed_report(self, analysis_result):
        """生成详细分析报告"""
        if 'error' in analysis_result:
            return f"分析失败: {analysis_result['error']}"
        
        report = []
        report.append("=" * 80)
        report.append("增量版本候选池评分检测报告")
        report.append("=" * 80)
        report.append(f"分析时间: {analysis_result['analysis_time']}")
        report.append(f"评分标准: 与实时系统一致（EnhancedScoreCalculator）")
        report.append(f"达标阈值: {self.passing_score}分")
        report.append("")
        
        # 总体统计
        report.append("📊 总体统计:")
        report.append(f"  候选池总数: {analysis_result['total_candidates']}")
        report.append(f"  成功分析: {analysis_result['success_analyses']}")
        report.append(f"  达标币种: {analysis_result['passing_candidates']}")
        report.append(f"  达标率: {analysis_result['passing_rate']:.1f}%")
        report.append("")
        
        # 达标币种详情
        passing_results = [r for r in analysis_result['results'] if r.get('is_passing', False)]
        if passing_results:
            report.append("🎯 达标币种详情 (≥7分):")
            report.append("-" * 80)
            for result in passing_results:
                symbol = result['symbol']
                score = result['total_score']
                components = result.get('component_scores', {})
                
                report.append(f"  {symbol:<12} | 总分: {score:.2f}")
                
                # 组件评分详情
                if components:
                    comp_str = "    组件: "
                    comp_parts = []
                    for comp_name, comp_score in components.items():
                        comp_parts.append(f"{comp_name}={comp_score:.1f}")
                    comp_str += ", ".join(comp_parts)
                    report.append(comp_str)
                
                # 基础信息
                price = result.get('current_price', 0)
                age = result.get('age_days', 0)
                depth = result.get('depth', 0)
                report.append(f"    价格: {price:.6f}, 币龄: {age}天, 深度: ${depth:,.0f}")
                report.append("")
        
        # 评分分布
        report.append("📈 评分分布:")
        score_ranges = [
            (12, float('inf'), "12分以上"),
            (10, 12, "10-12分"),
            (8, 10, "8-10分"),
            (6, 8, "6-8分"),
            (0, 6, "6分以下")
        ]
        
        for min_score, max_score, label in score_ranges:
            count = sum(1 for r in analysis_result['results'] 
                       if min_score <= r.get('total_score', 0) < max_score)
            report.append(f"  {label}: {count} 个币种")
        
        report.append("")
        
        # 与快速脚本对比说明
        report.append("🔍 与原快速脚本的主要差异:")
        report.append("  1. 使用与实时系统相同的EnhancedScoreCalculator")
        report.append("  2. 采用实时系统的评分权重配置")
        report.append("  3. 包含7个评分组件（深度、成交量、币龄、动量、通道、波动率、流动性）")
        report.append("  4. 应用数据质量调整因子")
        report.append("  5. 使用实时系统的评分阈值和计算逻辑")
        report.append("")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_report(self, report_content):
        """保存报告到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'enhanced_fast_analysis_report_{timestamp}.txt'
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"报告已保存到: {filename}")
            return filename
        
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")
            return None

def main():
    """主函数"""
    print("=" * 60)
    print("增量版本快速候选池评分检测")
    print("与实时系统评分规则保持一致")
    print("=" * 60)
    
    # 创建分析器
    analyzer = EnhancedFastCandidateAnalyzer()
    
    # 执行分析
    print("正在分析候选池...")
    analysis_result = analyzer.analyze_cached_candidates()
    
    # 生成报告
    print("正在生成报告...")
    report = analyzer.generate_detailed_report(analysis_result)
    
    # 保存报告
    filename = analyzer.save_report(report)
    
    # 输出关键统计
    if 'error' not in analysis_result:
        print(f"\n📊 分析结果:")
        print(f"  总候选数: {analysis_result['total_candidates']}")
        print(f"  成功分析: {analysis_result['success_analyses']}")
        print(f"  达标币种: {analysis_result['passing_candidates']}")
        print(f"  达标率: {analysis_result['passing_rate']:.1f}%")
        
        if filename:
            print(f"\n📄 详细报告已保存到: {filename}")
    else:
        print(f"\n❌ 分析失败: {analysis_result['error']}")

if __name__ == "__main__":
    main()