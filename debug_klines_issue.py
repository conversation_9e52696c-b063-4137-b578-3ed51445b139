#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线数据获取问题调试脚本
分析为什么会出现大量的K线数据获取失败
"""

import sys
import json
import time
import logging
from datetime import datetime

def debug_klines_issue():
    """调试K线数据获取问题"""
    print("开始调试K线数据获取问题...")
    
    try:
        # 1. 导入必要模块
        print("1. 导入模块...")
        from binance_trader import BinanceTrader
        from strategy.maker_channel import MakerChannelStrategy
        
        # 2. 加载配置
        print("2. 加载配置...")
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 添加策略需要的配置参数
        config['first_nominal'] = 100
        config['order_ttl'] = 300
        config['network_check_interval'] = 60
        config['max_symbol_failures'] = 5
        config['failure_cooldown'] = 3600
        config['network_error_cooldown'] = 300
        
        # 3. 初始化交易器
        print("3. 初始化BinanceTrader...")
        trader = BinanceTrader(config)
        print(f"   交易对数量: {len(trader.symbols)}")
        
        # 4. 测试问题币种
        problem_symbols = ['ALPINEUSDT', 'BCHUSDT', 'EGLDUSDT']
        
        print("\n4. 测试问题币种的K线数据获取...")
        for symbol in problem_symbols:
            print(f"\n--- 测试 {symbol} ---")
            
            # 检查币种是否在交易对列表中
            if symbol not in trader.symbols:
                print(f"   ❌ {symbol} 不在交易对列表中")
                continue
            else:
                print(f"   ✅ {symbol} 在交易对列表中")
            
            # 直接测试HTTP客户端
            print(f"   测试HTTP客户端直接调用...")
            try:
                start_time = time.time()
                klines_raw = trader.http.get('/fapi/v1/klines', {
                    'symbol': symbol,
                    'interval': '15m',
                    'limit': 5
                })
                end_time = time.time()
                
                print(f"   HTTP响应时间: {end_time - start_time:.2f}秒")
                print(f"   HTTP响应类型: {type(klines_raw)}")
                
                if isinstance(klines_raw, list):
                    print(f"   ✅ HTTP返回列表，长度: {len(klines_raw)}")
                    if len(klines_raw) > 0:
                        print(f"   首条数据: {klines_raw[0][:6]}")  # 只显示前6个字段
                    else:
                        print(f"   ⚠️  HTTP返回空列表")
                elif isinstance(klines_raw, dict):
                    print(f"   ⚠️  HTTP返回字典: {klines_raw}")
                else:
                    print(f"   ❌ HTTP返回异常类型: {klines_raw}")
                    
            except Exception as e:
                print(f"   ❌ HTTP调用异常: {e}")
            
            # 测试BinanceTrader的get_klines方法
            print(f"   测试BinanceTrader.get_klines...")
            try:
                start_time = time.time()
                klines_trader = trader.get_klines(symbol, '15m', 5)
                end_time = time.time()
                
                print(f"   Trader响应时间: {end_time - start_time:.2f}秒")
                print(f"   Trader响应类型: {type(klines_trader)}")
                
                if isinstance(klines_trader, list):
                    print(f"   ✅ Trader返回列表，长度: {len(klines_trader)}")
                else:
                    print(f"   ❌ Trader返回异常: {klines_trader}")
                    
            except Exception as e:
                print(f"   ❌ Trader调用异常: {e}")
            
            time.sleep(0.5)  # 避免请求过快
        
        # 5. 测试网络状态
        print("\n5. 测试网络状态...")
        try:
            # 测试简单的ping接口
            ping_result = trader.http.get('/fapi/v1/ping')
            print(f"   Ping结果: {ping_result}")
            
            # 测试服务器时间
            time_result = trader.http.get('/fapi/v1/time')
            print(f"   服务器时间: {time_result}")
            
        except Exception as e:
            print(f"   ❌ 网络测试异常: {e}")
        
        # 6. 检查代理设置
        print("\n6. 检查代理设置...")
        print(f"   代理配置: {trader.http.session.proxies}")
        
        # 7. 测试不同的时间间隔
        print("\n7. 测试不同时间间隔...")
        test_symbol = 'BTCUSDT'  # 使用稳定的币种
        intervals = ['1m', '5m', '15m', '1h']
        
        for interval in intervals:
            try:
                start_time = time.time()
                klines = trader.get_klines(test_symbol, interval, 5)
                end_time = time.time()
                
                if isinstance(klines, list) and len(klines) > 0:
                    print(f"   ✅ {interval}: {len(klines)}条数据, 耗时{end_time - start_time:.2f}秒")
                else:
                    print(f"   ❌ {interval}: 获取失败")
                    
            except Exception as e:
                print(f"   ❌ {interval}: 异常 {e}")
            
            time.sleep(0.2)
        
        print("\n🔍 调试完成！")
        
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_klines_issue()