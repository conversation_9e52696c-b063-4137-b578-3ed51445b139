#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开仓信号检查器
对候选池中的所有币种执行开仓信号检查，识别最适合开仓的币种
"""

import pandas as pd
import numpy as np
import pickle
import logging
import time
from datetime import datetime
from pathlib import Path
import sys
import os
import json
import yaml
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from binance_trader import BinanceTrader
from enhanced_score_calculator import EnhancedScoreCalculator
from strategy.dynamic_tf_helper import dynamic_tf_for_channel
from cache_manager import CacheManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('OpeningSignalChecker')

class OpeningSignalChecker:
    """开仓信号检查器"""
    
    def __init__(self, trader: BinanceTrader = None):
        # 加载配置并初始化trader
        self.config = self._load_config()
        self.trader = trader or (BinanceTrader(self.config) if self.config else None)
        self.log = logging.getLogger('OpeningSignalChecker')
        self.score_calculator = EnhancedScoreCalculator()
        self.cache_manager = CacheManager()
        
        # 开仓条件阈值
        self.min_score = 10  # 最低评分要求
        self.min_depth_ratio = 0.001  # 最小深度比例
        
        # 设置日志格式
        if not self.log.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.log.addHandler(handler)
            self.log.setLevel(logging.INFO)
    
    def _load_config(self) -> Optional[Dict]:
        """加载配置文件"""
        try:
            # 尝试加载YAML配置
            yaml_path = os.path.join('config', 'config.yaml')
            if os.path.exists(yaml_path):
                with open(yaml_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            
            # 尝试加载JSON配置
            json_path = os.path.join('config', 'config.json')
            if os.path.exists(json_path):
                with open(json_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            self.log.warning("未找到配置文件，将使用缓存数据进行分析")
            return None
            
        except Exception as e:
            self.log.error(f"加载配置文件失败: {e}")
            return None
    
    def load_candidate_cache(self) -> Dict:
        """加载候选池缓存数据"""
        try:
            cache_file = Path("cache/candidates.pkl")
            if not cache_file.exists():
                self.log.error(f"候选池缓存文件不存在: {cache_file}")
                return {}
            
            with open(cache_file, 'rb') as f:
                candidate_list = pickle.load(f)
            
            # 如果是列表，需要为每个币种创建基本数据结构
            if isinstance(candidate_list, list):
                cand_cache = {}
                for symbol in candidate_list:
                    cand_cache[symbol] = {
                        'score': 0,  # 默认评分，后续会重新计算
                        'last_update': '',
                        'fail_count': 0,
                        'price': 0,
                        'depth': 0,
                        'age': 0,
                        'M': 0
                    }
                self.log.info(f"成功加载候选池缓存（列表格式），共 {len(cand_cache)} 个币种")
                return cand_cache
            elif isinstance(candidate_list, dict):
                self.log.info(f"成功加载候选池缓存（字典格式），共 {len(candidate_list)} 个币种")
                return candidate_list
            else:
                self.log.error(f"候选池缓存格式不正确: {type(candidate_list)}")
                return {}
                
        except Exception as e:
            self.log.error(f"加载候选池缓存失败: {e}")
            return {}
    
    def get_current_positions(self) -> List[str]:
        """获取当前持仓币种列表"""
        try:
            if not self.trader:
                return []
            
            positions = self.trader.get_positions()
            position_symbols = []
            
            for pos in positions:
                if float(pos['positionAmt']) != 0:
                    position_symbols.append(pos['symbol'])
            
            self.log.info(f"当前持仓币种: {position_symbols}")
            return position_symbols
        except Exception as e:
            self.log.error(f"获取持仓信息失败: {e}")
            return []
    
    def get_klines_data(self, symbol: str, interval: str = '15m', limit: int = 200) -> pd.DataFrame:
        """获取K线数据，优先使用缓存"""
        try:
            # 首先尝试从缓存加载
            cached_data = self.cache_manager.load_klines(symbol, interval, limit)
            if cached_data is not None and not cached_data.empty:
                self.log.debug(f"从缓存加载K线数据成功: {symbol} {interval} {limit}条")
                return cached_data
            
            # 缓存未命中，从API获取
            self.log.debug(f"缓存未命中，从API获取K线数据: {symbol} {interval} {limit}条")
            
            if self.trader:
                # 使用trader获取数据
                klines = self.trader.http.get('/fapi/v1/klines', {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': limit
                })
                
                if not klines or len(klines) < 20:
                    return None
                
                df = pd.DataFrame(klines, columns=['t', 'o', 'h', 'l', 'c', 'v', 'ct', 'qv', 'n', 'tbbav', 'tbqav', 'ignore'])
                df = df[['t', 'o', 'h', 'l', 'c', 'v']].astype(float)
                
                # 重命名列以匹配通道位置计算的需求
                df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                
                # 设置时间索引，用于币龄计算
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                
                # 保存到缓存
                self.cache_manager.save_klines(symbol, interval, limit, df)
                
                return df
            else:
                self.log.warning(f"没有可用的trader实例，无法获取K线数据: {symbol}")
                return None
                
        except Exception as e:
            self.log.error(f"获取K线数据失败 {symbol}: {e}")
            return None
    
    def get_coin_age(self, symbol: str) -> int:
        """获取币种上市天数"""
        try:
            # 首先尝试从symbols缓存获取
            symbols_data = self.cache_manager.load_symbols(self.trader)
            if symbols_data:
                for symbol_info in symbols_data:
                    if symbol_info.get('symbol') == symbol:
                        return symbol_info.get('age_days', 30)  # 默认30天
            
            # 如果缓存中没有，尝试获取K线数据计算
            klines = self.get_klines_data(symbol, '1d', 1000)
            if klines is not None and not klines.empty:
                # 确保时间列是datetime类型且有时区信息
                first_time = klines.index[0]
                
                # 确保时间有时区信息
                if hasattr(first_time, 'tz') and first_time.tz is None:
                    # 如果是naive datetime，假设为UTC
                    first_time = first_time.tz_localize('UTC')
                
                # 计算币龄
                current_time = pd.Timestamp.now(tz='UTC')
                
                # 确保两个时间都有时区信息
                if hasattr(first_time, 'tz') and first_time.tz is not None:
                    age_days = (current_time - first_time).days
                else:
                    # 如果first_time没有时区信息，转换为UTC
                    if isinstance(first_time, pd.Timestamp):
                        first_time_utc = first_time.tz_localize('UTC')
                    else:
                        first_time_utc = pd.Timestamp(first_time).tz_localize('UTC')
                    age_days = (current_time - first_time_utc).days
                
                return max(1, age_days)  # 至少1天
            
            # 都获取不到，返回默认值
            self.log.warning(f"无法获取 {symbol} 币龄，使用默认值30天")
            return 30
            
        except Exception as e:
            self.log.warning(f"获取 {symbol} 币龄失败: {e}")
            return 30
    
    def check_breakthrough_conditions(self, symbol: str, klines: pd.DataFrame) -> Tuple[bool, str]:
        """检查通道突破条件"""
        try:
            if len(klines) < 20:
                return False, "数据量不足"
            
            # 计算动态时间框架
            coin_age = self.get_coin_age(symbol)
            tf_minutes = dynamic_tf_for_channel(coin_age)
            
            # 获取最新价格 - 兼容不同列名格式
            close_col = 'close' if 'close' in klines.columns else 'c'
            high_col = 'high' if 'high' in klines.columns else 'h'
            latest_close = klines[close_col].iloc[-1]
            latest_high = klines[high_col].iloc[-1]
            
            # 计算通道上轨（简化版本，使用20周期高点）
            upper_band = klines[high_col].rolling(20).max().iloc[-1]
            
            # 检查是否接近或突破上轨
            close_to_upper = (latest_close / upper_band) >= 0.98
            high_breakthrough = (latest_high / upper_band) >= 1.0
            
            # 检查突破幅度限制（不超过5%）
            breakthrough_ratio = latest_high / upper_band
            if breakthrough_ratio > 1.05:
                return False, f"突破幅度过大: {breakthrough_ratio:.3f}"
            
            # 避免连续突破（检查最近5根K线）
            recent_highs = klines['high'].tail(5)
            recent_breakthroughs = (recent_highs / upper_band >= 1.0).sum()
            if recent_breakthroughs > 2:
                return False, f"连续突破次数过多: {recent_breakthroughs}"
            
            if close_to_upper or high_breakthrough:
                reason = "收盘价接近上轨" if close_to_upper else "最高价突破上轨"
                return True, reason
            
            return False, "未满足突破条件"
            
        except Exception as e:
            self.log.error(f"检查 {symbol} 突破条件失败: {e}")
            return False, f"检查失败: {str(e)}"
    
    def get_market_depth(self, symbol: str) -> float:
        """获取市场深度信息（0.1%深度值）"""
        try:
            depth_file = Path(f"cache/depth_{symbol}.json")
            if depth_file.exists():
                with open(depth_file, 'r') as f:
                    depth_value = json.load(f)
                return float(depth_value) if depth_value is not None else 0.0
            return 0.0
        except Exception as e:
            self.log.warning(f"获取 {symbol} 深度信息失败: {e}")
            return 0.0
    
    def calculate_depth_ratio(self, depth_value: float) -> float:
        """计算深度比例（这里简化为深度值本身的归一化）"""
        try:
            # 由于缓存中存储的是0.1%深度值，我们直接使用这个值
            # 可以根据需要进行归一化处理
            if depth_value > 0:
                # 简单的归一化：深度值越大，比例越高，但限制在0-1之间
                return min(depth_value / 100000, 1.0)  # 假设100000为较高的深度值
            return 0.0
            
        except Exception as e:
            self.log.warning(f"计算深度比例失败: {e}")
            return 0.0
    
    def analyze_single_symbol(self, symbol: str, candidate_data: Dict) -> Dict:
        """分析单个币种的开仓信号"""
        try:
            self.log.info(f"开始分析 {symbol}")
            
            # 获取基本信息
            score = candidate_data.get('score', 0)
            coin_age = self.get_coin_age(symbol)
            
            # 如果评分为0，尝试重新计算评分
            if score == 0:
                # 计算动态时间周期
                tf_minutes = dynamic_tf_for_channel(coin_age)
                
                # 获取K线数据
                klines = self.get_klines_data(symbol, '15m', 200)
                if klines is not None and len(klines) >= 50:
                    # 获取深度数据
                    depth_value = candidate_data.get('depth', 0)
                    # 使用calculate_comprehensive_score方法
                    score_result = self.score_calculator.calculate_comprehensive_score(
                        symbol=symbol,
                        df_data=klines,
                        depth_data=depth_value
                    )
                    score = score_result.total_score
                    self.log.info(f"{symbol} 重新计算评分: {score}")
                else:
                    self.log.warning(f"{symbol} K线数据不足，无法计算评分")
            
            # 计算动态时间周期
            tf_minutes = dynamic_tf_for_channel(coin_age)
            
            # 获取K线数据
            klines = self.get_klines_data(symbol, '15m', 200)
            if klines is None or len(klines) < 20:
                return {
                    'symbol': symbol,
                    'score': score,
                    'coin_age': coin_age,
                    'meets_conditions': False,
                    'failure_reason': '数据不足',
                    'current_price': 0,
                    'depth_ratio': 0,
                    'breakthrough_check': False,
                    'breakthrough_reason': '数据不足'
                }
            
            # 获取当前价格 - 兼容不同列名格式
            close_col = 'close' if 'close' in klines.columns else 'c'
            current_price = klines[close_col].iloc[-1]
            
            # 检查通道突破条件
            breakthrough_passed, breakthrough_reason = self.check_breakthrough_conditions(symbol, klines)
            
            # 获取深度信息
            depth_value = self.get_market_depth(symbol)
            depth_ratio = self.calculate_depth_ratio(depth_value)
            
            # 综合判断是否满足开仓条件
            meets_conditions = (
                score >= self.min_score and
                breakthrough_passed and
                depth_ratio >= self.min_depth_ratio
            )
            
            # 确定失败原因
            failure_reason = ""
            if not meets_conditions:
                reasons = []
                if score < self.min_score:
                    reasons.append(f"评分不足({score}<{self.min_score})")
                if not breakthrough_passed:
                    reasons.append(f"未通过突破检查({breakthrough_reason})")
                if depth_ratio < self.min_depth_ratio:
                    reasons.append(f"深度不足({depth_ratio:.4f}<{self.min_depth_ratio})")
                failure_reason = "; ".join(reasons)
            
            result = {
                'symbol': symbol,
                'score': score,
                'coin_age': coin_age,
                'current_price': current_price,
                'depth_ratio': depth_ratio,
                'breakthrough_check': breakthrough_passed,
                'breakthrough_reason': breakthrough_reason,
                'meets_conditions': meets_conditions,
                'failure_reason': failure_reason,
                'tf_minutes': tf_minutes,
                'last_update': candidate_data.get('last_update', ''),
                'fail_count': candidate_data.get('fail_count', 0)
            }
            
            self.log.info(f"{symbol} 分析完成: 评分={score}, 突破={breakthrough_passed}, 深度={depth_ratio:.4f}, 满足条件={meets_conditions}")
            return result
            
        except Exception as e:
            self.log.error(f"分析 {symbol} 失败: {e}")
            return {
                'symbol': symbol,
                'score': 0,
                'meets_conditions': False,
                'failure_reason': f'分析失败: {str(e)}',
                'current_price': 0,
                'depth_ratio': 0,
                'breakthrough_check': False,
                'breakthrough_reason': '分析失败'
            }
    
    def check_all_candidates(self, max_workers: int = 10) -> Tuple[List[Dict], List[Dict]]:
        """检查所有候选币种的开仓信号"""
        try:
            # 加载候选池缓存
            cand_cache = self.load_candidate_cache()
            if not cand_cache:
                self.log.error("候选池缓存为空，无法执行检查")
                return [], []
            
            # 获取当前持仓
            current_positions = self.get_current_positions()
            
            # 过滤掉已持仓的币种
            available_candidates = {
                symbol: data for symbol, data in cand_cache.items()
                if symbol not in current_positions
            }
            
            self.log.info(f"候选池总数: {len(cand_cache)}, 已持仓: {len(current_positions)}, 可分析: {len(available_candidates)}")
            
            # 并行分析所有候选币种
            results = []
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_symbol = {
                    executor.submit(self.analyze_single_symbol, symbol, data): symbol
                    for symbol, data in available_candidates.items()
                }
                
                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        self.log.error(f"分析 {symbol} 时发生异常: {e}")
                        results.append({
                            'symbol': symbol,
                            'score': 0,
                            'meets_conditions': False,
                            'failure_reason': f'执行异常: {str(e)}',
                            'current_price': 0,
                            'depth_ratio': 0,
                            'breakthrough_check': False,
                            'breakthrough_reason': '执行异常'
                        })
            
            # 分离满足条件和不满足条件的币种
            qualified_symbols = [r for r in results if r['meets_conditions']]
            unqualified_symbols = [r for r in results if not r['meets_conditions']]
            
            # 按评分排序
            qualified_symbols.sort(key=lambda x: x['score'], reverse=True)
            unqualified_symbols.sort(key=lambda x: x['score'], reverse=True)
            
            self.log.info(f"检查完成: 满足条件 {len(qualified_symbols)} 个, 不满足条件 {len(unqualified_symbols)} 个")
            
            return qualified_symbols, unqualified_symbols
            
        except Exception as e:
            self.log.error(f"检查所有候选币种失败: {e}")
            return [], []
    
    def generate_report(self, qualified_symbols: List[Dict], unqualified_symbols: List[Dict]) -> str:
        """生成开仓信号检查报告"""
        try:
            report_lines = []
            report_lines.append("=" * 80)
            report_lines.append("开仓信号检查报告")
            report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append("=" * 80)
            
            # 总体统计
            total_candidates = len(qualified_symbols) + len(unqualified_symbols)
            report_lines.append(f"\n总体统计:")
            report_lines.append(f"  候选币种总数: {total_candidates}")
            
            if total_candidates > 0:
                report_lines.append(f"  满足开仓条件: {len(qualified_symbols)} ({len(qualified_symbols)/total_candidates*100:.1f}%)")
                report_lines.append(f"  不满足条件: {len(unqualified_symbols)} ({len(unqualified_symbols)/total_candidates*100:.1f}%)")
            else:
                report_lines.append(f"  满足开仓条件: {len(qualified_symbols)} (0.0%)")
                report_lines.append(f"  不满足条件: {len(unqualified_symbols)} (0.0%)")
            
            # 满足条件的币种详情
            if qualified_symbols:
                report_lines.append(f"\n满足开仓条件的币种 (共{len(qualified_symbols)}个):")
                report_lines.append("-" * 80)
                report_lines.append(f"{'排名':<4} {'币种':<15} {'评分':<6} {'当前价格':<12} {'深度比例':<10} {'突破原因':<20}")
                report_lines.append("-" * 80)
                
                for i, symbol_data in enumerate(qualified_symbols[:20], 1):  # 只显示前20个
                    report_lines.append(
                        f"{i:<4} {symbol_data['symbol']:<15} {symbol_data['score']:<6.1f} "
                        f"{symbol_data['current_price']:<12.6f} {symbol_data['depth_ratio']:<10.4f} "
                        f"{symbol_data['breakthrough_reason']:<20}"
                    )
            else:
                report_lines.append(f"\n当前没有满足开仓条件的币种")
            
            # 不满足条件的币种统计
            if unqualified_symbols:
                # 统计失败原因
                failure_stats = {}
                for symbol_data in unqualified_symbols:
                    reason = symbol_data['failure_reason']
                    failure_stats[reason] = failure_stats.get(reason, 0) + 1
                
                report_lines.append(f"\n不满足条件的原因统计:")
                report_lines.append("-" * 50)
                for reason, count in sorted(failure_stats.items(), key=lambda x: x[1], reverse=True):
                    report_lines.append(f"  {reason}: {count} 个币种")
                
                # 显示评分较高但不满足条件的币种
                high_score_unqualified = [s for s in unqualified_symbols if s['score'] >= 6]
                if high_score_unqualified:
                    report_lines.append(f"\n高评分但不满足条件的币种 (评分≥6, 共{len(high_score_unqualified)}个):")
                    report_lines.append("-" * 80)
                    report_lines.append(f"{'币种':<15} {'评分':<6} {'当前价格':<12} {'深度比例':<10} {'失败原因':<30}")
                    report_lines.append("-" * 80)
                    
                    for symbol_data in high_score_unqualified[:10]:  # 只显示前10个
                        report_lines.append(
                            f"{symbol_data['symbol']:<15} {symbol_data['score']:<6.1f} "
                            f"{symbol_data['current_price']:<12.6f} {symbol_data['depth_ratio']:<10.4f} "
                            f"{symbol_data['failure_reason']:<30}"
                        )
            
            report_lines.append("\n" + "=" * 80)
            
            return "\n".join(report_lines)
            
        except Exception as e:
            self.log.error(f"生成报告失败: {e}")
            return f"报告生成失败: {str(e)}"
    
    def save_report(self, report_content: str, filename: str = None) -> str:
        """保存报告到文件"""
        try:
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"opening_signal_report_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.log.info(f"报告已保存到: {filename}")
            return filename
            
        except Exception as e:
            self.log.error(f"保存报告失败: {e}")
            return ""
    
    def run_full_check(self) -> Tuple[List[Dict], List[Dict], str]:
        """执行完整的开仓信号检查"""
        try:
            self.log.info("开始执行完整的开仓信号检查...")
            
            # 检查所有候选币种
            qualified_symbols, unqualified_symbols = self.check_all_candidates()
            
            # 生成报告
            report_content = self.generate_report(qualified_symbols, unqualified_symbols)
            
            # 保存报告
            report_file = self.save_report(report_content)
            
            self.log.info("开仓信号检查完成")
            
            return qualified_symbols, unqualified_symbols, report_file
            
        except Exception as e:
            self.log.error(f"执行完整检查失败: {e}")
            return [], [], ""

def main():
    """主函数"""
    try:
        # 创建检查器实例
        checker = OpeningSignalChecker()
        
        # 执行完整检查
        qualified, unqualified, report_file = checker.run_full_check()
        
        print(f"\n检查结果:")
        print(f"满足开仓条件: {len(qualified)} 个币种")
        print(f"不满足条件: {len(unqualified)} 个币种")
        print(f"详细报告: {report_file}")
        
        # 显示前5个满足条件的币种
        if qualified:
            print(f"\n前5个最佳开仓候选:")
            for i, symbol_data in enumerate(qualified[:5], 1):
                print(f"{i}. {symbol_data['symbol']} - 评分: {symbol_data['score']:.1f}, "
                      f"价格: {symbol_data['current_price']:.6f}, "
                      f"深度: {symbol_data['depth_ratio']:.4f}")
        
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

if __name__ == "__main__":
    main()