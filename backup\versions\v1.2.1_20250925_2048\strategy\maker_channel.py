import pandas as pd
import numpy as np
import datetime
import logging
import time
# ↓↓↓ 复用你的原模块 ↓↓↓
from binance_trader import BinanceTrader
from binance_trader import ApiRateLimiter, OrderOperationQueue
# ↓↓↓ 缓存管理器 ↓↓↓
from cache_manager import CacheManager
# ↓↓↓ 监控系统 ↓↓↓
from 核心指标监控系统 import MetricsCollector
# ↓↓↓ 策略优化器 ↓↓↓
from strategy_optimizer import StrategyOptimizer, OptimizationConfig

class MakerChannelStrategy:
    """依赖 BinanceTrader 完成所有下单/查询"""
    def __init__(self, trader: BinanceTrader, config: dict):
        self.trader = trader
        self.cfg = config
        self.log = logging.getLogger('MakerChannel')
        self.symbol_selector_log = logging.getLogger('ReferenceFolder.symbol_selector')
        self.symbol_scorer_log = logging.getLogger('ReferenceFolder.symbol_scorer')
        self.symbol = None
        self.pos = None
        self.add_count = 0
        self.original_nominal = config['first_nominal']
        self.cache = CacheManager()
        self.cand_cache = {}  # 内存缓存候选池
        self.last_full_scan = 0  # 上次全量扫描时间
        self.last_candidate_scan = 0  # 上次候选池扫描时间
        self.margin_alert_symbols = set()  # 保证金警报币种集合
        self.active_orders = {}  # 活跃订单跟踪 {order_id: {symbol, type, create_time, ttl}}
        self.order_ttl = config.get('order_ttl', 300)  # 限价单TTL，默认5分钟
        self.network_check_interval = config.get('network_check_interval', 60)  # 网络检测间隔，默认60秒
        self.last_network_check = 0  # 上次网络检测时间
        self.network_status = {'connected': True, 'latency': 0, 'last_error': None}  # 网络状态
        
        # 异步首次打分相关
        self.first_scoring_completed = False
        self.scoring_batch_index = 0
        self.last_scoring_time = 0
        self.scoring_batch_size = 10  # 每批处理10个币种
        self.scoring_interval = 30  # 每30秒处理一批
        
        # 初始化监控系统
        monitoring_config = {
            'monitoring_interval': config.get('monitoring_interval', 60),
            'alert_thresholds': {
                'order_success_rate': config.get('min_order_success_rate', 0.9),
                'api_success_rate': config.get('min_api_success_rate', 0.95),
                'max_drawdown': config.get('max_drawdown_threshold', 0.1),
                'elimination_frequency': config.get('max_elimination_frequency', 5.0)
            }
        }
        self.metrics_collector = MetricsCollector(monitoring_config)
        self.metrics_collector.start_monitoring()
        
        # 初始化策略优化器
        optimization_config = OptimizationConfig(
            max_concurrent_tasks=config.get('max_concurrent_tasks', 10),
            max_worker_threads=config.get('max_worker_threads', 5),
            enable_caching=config.get('enable_optimization_caching', True),
            cache_ttl=config.get('optimization_cache_ttl', 300),
            batch_size=config.get('optimization_batch_size', 50),
            enable_profiling=config.get('enable_profiling', True)
        )
        self.strategy_optimizer = StrategyOptimizer(optimization_config)
        self.strategy_optimizer.start()
        
        self.symbol_selector_log.info(f"币种选择器初始化完成，选择前{config.get('symbol_limit', 50)}个币种，最低评分要求大于:{config.get('min_score', 7)}")
        self.log.info("监控系统和策略优化器已启动")
        
    # ---------- 新增：启动预热 ----------
    def warmup(self):
        """程序启动时轻量预热：仅获取基础信息，不拉取K线"""
        try:
            self.log.info("执行轻量启动预热（仅基础信息）...")

            # 首先检查并恢复实际持仓状态
            self._recover_positions_on_startup()

            # 获取所有交易对24小时行情（使用HttpClient的全局超时设置）
            info = self.trader.http.get('/fapi/v1/ticker/24hr')
            if not info or not isinstance(info, list):
                self.log.warning(f"启动预热失败：未获取到24hr行情数据 ({type(info)})")
                return

            # 获取交易所信息来过滤无效交易对
            exchange_info = self.trader.http.get('/fapi/v1/exchangeInfo')
            valid_symbols = set()
            if exchange_info and 'symbols' in exchange_info:
                for s in exchange_info['symbols']:
                    # 只选择状态为TRADING的USDT永续合约
                    if (isinstance(s, dict) and 
                        s.get('symbol', '').endswith('USDT') and
                        s.get('contractType') == 'PERPETUAL' and
                        s.get('status') == 'TRADING'):
                        valid_symbols.add(s['symbol'])

            # 过滤有效交易对
            usdt_pairs = []
            for s in info:
                symbol = s.get('symbol', '')
                if symbol in valid_symbols:
                    # 额外过滤条件：成交量大于1000 USDT，价格大于0.001
                    quote_volume = float(s.get('quoteVolume', 0.0))
                    last_price = float(s.get('lastPrice', 0.0))
                    price_change_percent = float(s.get('priceChangePercent', 0.0))
                    
                    # 过滤掉成交量过低、价格异常或涨幅异常的无效交易对
                    if (quote_volume > 1000 and 
                        last_price > 0.001 and 
                        abs(price_change_percent) < 10000):  # 过滤异常涨幅
                        usdt_pairs.append(s)
            
            self.log.info(f"交易所有效交易对数量: {len(valid_symbols)}, 过滤后有效交易对数量: {len(usdt_pairs)}")

            # 按涨幅排序，取前25
            top_gainers = sorted(
                usdt_pairs,
                key=lambda x: float(x.get('priceChangePercent', 0.0)),
                reverse=True
            )[:25]

            # 按成交额排序（quoteVolume = USDT成交额），取前25
            top_volume = sorted(
                usdt_pairs,
                key=lambda x: float(x.get('quoteVolume', 0.0)),
                reverse=True
            )[:25]

            # 合并去重，构建候选池
            candidates = {}
            for s in top_gainers + top_volume:
                symbol = s['symbol']
                candidates[symbol] = {
                    'symbol': symbol,
                    'price_change_percent': float(s.get('priceChangePercent', 0.0)),
                    'quote_volume': float(s.get('quoteVolume', 0.0)),
                    'last_price': float(s.get('lastPrice', 0.0)),
                    'score': 0.0,  # 初始评分为0，后续异步计算
                    'last_update': time.time()
                }

            self.cand_cache = candidates
            self.log.info(f"预热完成，候选池包含 {len(candidates)} 个交易对")
            
            # 记录监控指标
            self.metrics_collector.record_order({'type': 'warmup_candidates', 'count': len(candidates), 'status': 'success'})

        except Exception as e:
            self.log.error(f"启动预热失败: {e}")
            # 记录监控指标
            self.metrics_collector.record_order({'type': 'warmup_error', 'error': str(e), 'status': 'failed'})

    def _recover_positions_on_startup(self):
        """启动时恢复持仓状态"""
        try:
            positions = self.trader.http.get('/fapi/v2/positionRisk')
            if not positions:
                self.log.info("启动时无持仓")
                return

            active_positions = []
            for pos in positions:
                if isinstance(pos, dict):
                    size = float(pos.get('positionAmt', 0))
                    if abs(size) > 0.001:  # 有实际持仓
                        symbol = pos.get('symbol', '')
                        entry_price = float(pos.get('entryPrice', 0))
                        unrealized_pnl = float(pos.get('unRealizedProfit', 0))
                        
                        active_positions.append({
                            'symbol': symbol,
                            'size': size,
                            'entry_price': entry_price,
                            'unrealized_pnl': unrealized_pnl,
                            'side': 'LONG' if size > 0 else 'SHORT'
                        })

            if active_positions:
                self.log.info(f"启动时发现 {len(active_positions)} 个活跃持仓")
                for pos in active_positions:
                    self.log.info(f"持仓: {pos['symbol']} {pos['side']} {pos['size']} @ {pos['entry_price']}, PnL: {pos['unrealized_pnl']}")
                
                # 检查这些持仓的止损单状态
                self._check_all_positions_stop_orders(active_positions)
                
                # 记录监控指标
                for pos in active_positions:
                    self.metrics_collector.record_position_metric(
                        pos['symbol'], 
                        pos['unrealized_pnl'] > 0, 
                        pos['unrealized_pnl']
                    )
            else:
                self.log.info("启动时无活跃持仓")

        except Exception as e:
            self.log.error(f"恢复持仓状态失败: {e}")

    def _check_all_positions_stop_orders(self, active_positions):
        """检查所有持仓的止损单状态"""
        try:
            # 获取所有挂单
            open_orders = self.trader.http.get('/fapi/v1/openOrders')
            if not open_orders:
                self.log.warning("获取挂单列表失败或为空")
                return

            # 按symbol分组挂单
            orders_by_symbol = {}
            for order in open_orders:
                if isinstance(order, dict):
                    symbol = order.get('symbol', '')
                    if symbol not in orders_by_symbol:
                        orders_by_symbol[symbol] = []
                    orders_by_symbol[symbol].append(order)

            # 检查每个持仓的止损单
            for pos in active_positions:
                symbol = pos['symbol']
                side = pos['side']
                
                symbol_orders = orders_by_symbol.get(symbol, [])
                stop_orders = [o for o in symbol_orders if o.get('type') in ['STOP_MARKET', 'TAKE_PROFIT_MARKET']]
                
                if not stop_orders:
                    self.log.warning(f"持仓 {symbol} ({side}) 缺少止损单，需要补充")
                    # 这里可以添加补充止损单的逻辑
                else:
                    self.log.info(f"持仓 {symbol} ({side}) 已有 {len(stop_orders)} 个止损单")

        except Exception as e:
            self.log.error(f"检查持仓止损单失败: {e}")

    def _async_first_score_batch(self):
        """异步首次打分：分批处理候选池，避免阻塞主循环"""
        try:
            current_time = time.time()
            
            # 检查是否需要处理下一批
            if current_time - self.last_scoring_time < self.scoring_interval:
                return
            
            if self.first_scoring_completed:
                return
            
            # 获取候选池列表
            candidates = list(self.cand_cache.keys())
            if not candidates:
                self.first_scoring_completed = True
                return
            
            # 计算当前批次范围
            start_idx = self.scoring_batch_index * self.scoring_batch_size
            end_idx = min(start_idx + self.scoring_batch_size, len(candidates))
            
            if start_idx >= len(candidates):
                self.first_scoring_completed = True
                self.log.info("异步首次打分完成")
                return
            
            # 处理当前批次
            batch_symbols = candidates[start_idx:end_idx]
            self.log.info(f"异步打分批次 {self.scoring_batch_index + 1}: 处理 {len(batch_symbols)} 个币种")
            
            for symbol in batch_symbols:
                try:
                    # 获取币龄
                    age_minutes = self.get_symbol_age_minutes(symbol)
                    if age_minutes is None:
                        continue
                    
                    age_days = age_minutes / (60 * 24)
                    
                    # 获取K线数据进行评分
                    tf = self.dynamic_tf(age_days)
                    interval = f'{tf}m'
                    df = self.get_klines(symbol, interval, 200)
                    
                    if df is not None and len(df) >= 50:
                        score = self.score_symbol(df, age_days)
                        self.cand_cache[symbol]['score'] = score
                        self.cand_cache[symbol]['last_update'] = current_time
                        
                        # 记录监控指标
                        self.metrics_collector.record_order({'type': 'score_calculation', 'symbol': symbol, 'score': score, 'status': 'success'})
                    
                except Exception as e:
                    self.log.warning(f"异步打分失败 {symbol}: {e}")
            
            # 更新批次索引和时间
            self.scoring_batch_index += 1
            self.last_scoring_time = current_time
            
        except Exception as e:
            self.log.error(f"异步首次打分批处理失败: {e}")

    def calculate_atr(self, high, low, close, length=14):
        """计算ATR"""
        try:
            tr = np.maximum(high - low, np.maximum(np.abs(high - np.roll(close, 1)), np.abs(low - np.roll(close, 1))))
            tr[0] = high[0] - low[0]  # 第一个值
            atr = pd.Series(tr).rolling(window=length).mean()
            return atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
        except:
            return 0

    def get_klines(self, symbol, interval='15m', limit=200):
        """获取K线数据"""
        try:
            # 使用策略优化器的缓存功能
            cache_key = f"klines_{symbol}_{interval}_{limit}"
            cached_data = self.strategy_optimizer.cache_manager.get(cache_key)
            if cached_data is not None:
                return cached_data
            
            klines = self.trader.http.get('/fapi/v1/klines', {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            })
            
            if not klines or len(klines) < 50:
                return None
            
            df = pd.DataFrame(klines, columns=['t', 'o', 'h', 'l', 'c', 'v', 'ct', 'qv', 'n', 'tbbav', 'tbqav', 'ignore'])
            df = df[['t', 'o', 'h', 'l', 'c', 'v']].astype(float)
            
            # 缓存结果
            self.strategy_optimizer.cache_manager.set(cache_key, df, ttl=60)
            
            return df
            
        except Exception as e:
            self.log.warning(f"获取K线失败 {symbol}: {e}")
            return None

    def score_symbol(self, df_d, age_days=None):
        """计算币种评分"""
        try:
            # 使用增强评分计算器
            from enhanced_score_calculator import EnhancedScoreCalculator
            
            calculator = EnhancedScoreCalculator()
            score = calculator.calculate_comprehensive_score(df_d, age_days)
            
            # 记录监控指标
            self.metrics_collector.record_order({'type': 'symbol_score', 'symbol': symbol, 'score': score, 'status': 'success'})
            
            return score
            
        except Exception as e:
            self.log.warning(f"评分计算失败: {e}")
            return 0.0

    def loop(self):
        """主循环"""
        try:
            self.log.info("策略主循环启动")
            
            # 启动预热
            self.warmup()
            
            while True:
                try:
                    loop_start_time = time.time()
                    
                    # 使用策略优化器执行主循环逻辑
                    optimized_tasks = [
                        ('async_scoring', self._async_first_score_batch),
                        ('network_check', self._check_network_status),
                        ('order_lifecycle', self._manage_order_lifecycle),
                        ('retry_queue', self._process_retry_queue)
                    ]
                    
                    # 并行执行优化任务
                    results = self.strategy_optimizer.execute_parallel_tasks(optimized_tasks)
                    
                    # 记录性能指标
                    loop_duration = time.time() - loop_start_time
                    self.metrics_collector.record_order({'type': 'loop_performance', 'duration': loop_duration, 'status': 'success'})
                    
                    # 检查告警
                    alerts = self.metrics_collector.check_alerts()
                    if alerts:
                        for alert in alerts:
                            self.log.warning(f"监控告警: {alert}")
                    
                    time.sleep(1)
                    
                except Exception as e:
                    self.log.error(f"主循环执行错误: {e}")
                    # 记录错误指标
                    self.metrics_collector.record_order({'type': 'loop_error', 'error': str(e), 'status': 'failed'})
                    time.sleep(5)
                    
        except KeyboardInterrupt:
            self.log.info("收到停止信号，正在关闭...")
            self.metrics_collector.stop_monitoring()
            self.strategy_optimizer.stop()
        except Exception as e:
            self.log.error(f"主循环致命错误: {e}")
            self.metrics_collector.stop_monitoring()
            self.strategy_optimizer.stop()

    def _manage_order_lifecycle(self):
        """管理订单生命周期"""
        try:
            current_time = time.time()
            expired_orders = []
            
            for order_id, order_info in self.active_orders.items():
                if current_time - order_info['create_time'] > order_info['ttl']:
                    expired_orders.append(order_id)
            
            for order_id in expired_orders:
                order_info = self.active_orders.pop(order_id)
                self.log.info(f"订单 {order_id} 已过期，移除跟踪")
                
                # 记录监控指标
                self.metrics_collector.record_order({'type': 'order_expired', 'symbol': symbol, 'order_id': order_id, 'status': 'expired'})
                
        except Exception as e:
            self.log.error(f"订单生命周期管理失败: {e}")

    def _process_retry_queue(self):
        """处理重试队列"""
        # 这里可以添加重试队列处理逻辑
        pass

    def _check_network_status(self):
        """检查网络状态"""
        try:
            current_time = time.time()
            if current_time - self.last_network_check < self.network_check_interval:
                return
            
            # 简单的网络检查
            start_time = time.time()
            response = self.trader.http.get('/fapi/v1/ping')
            latency = (time.time() - start_time) * 1000
            
            if response is not None:
                self.network_status = {
                    'connected': True,
                    'latency': latency,
                    'last_error': None
                }
                # 记录监控指标
                self.metrics_collector.update_system_metrics(api_success_rate=1.0)
            else:
                self.network_status = {
                    'connected': False,
                    'latency': 0,
                    'last_error': 'Ping failed'
                }
                # 记录监控指标
                self.metrics_collector.update_system_metrics(api_success_rate=0.0)
            
            self.last_network_check = current_time
            
        except Exception as e:
            self.network_status = {
                'connected': False,
                'latency': 0,
                'last_error': str(e)
            }
            self.log.error(f"网络状态检查失败: {e}")

    def get_symbol_age_minutes(self, symbol):
        """获取币种上线时间（分钟）"""
        try:
            # 这里应该实现获取币种上线时间的逻辑
            # 暂时返回一个默认值
            return 60 * 24 * 30  # 30天
        except:
            return None

    def dynamic_tf(self, age_days):
        """动态时间框架"""
        if 60*24*7 <= age_days < 60*24*365:   # 7天-365天
            return 2
        elif age_days >= 60*24*365:           # >365天
            return 1
        elif 60*6 <= age_days < 60*24*7:      # 6小时-7天（新币也满分）
            return 2
        else:                                # <6小时
            return 1