#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查候选池缓存文件的内容
"""
import pickle
import os

def check_cand_cache():
    cache_file = 'cache/cand_cache.pkl'
    if not os.path.exists(cache_file):
        print(f"❌ 文件不存在: {cache_file}")
        return
    
    try:
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        
        print(f"✅ 候选池包含 {len(data)} 个币种")
        print("\n前10个币种详情:")
        print("-" * 80)
        
        for i, (symbol, info) in enumerate(list(data.items())[:10]):
            price_change = info.get('price_change_percent', 0)
            score = info.get('score', 0)
            last_price = info.get('last_price', 0)
            volume = info.get('quote_volume', 0)
            
            print(f"{i+1:2d}. {symbol:15s} | 涨幅: {price_change:6.2f}% | 评分: {score:6.2f} | 价格: ${last_price:8.4f} | 成交量: ${volume:,.0f}")
        
        print("-" * 80)
        print(f"总计: {len(data)} 个币种")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

def check_candidates():
    cache_file = 'cache/candidates.pkl'
    if not os.path.exists(cache_file):
        print(f"❌ 文件不存在: {cache_file}")
        return
    
    try:
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        
        print(f"✅ candidates.pkl 包含 {len(data)} 个币种")
        print("前10个币种:", data[:10] if len(data) >= 10 else data)
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

if __name__ == "__main__":
    print("=== 检查候选池缓存文件 ===")
    check_cand_cache()
    print("\n=== 检查候选列表文件 ===")
    check_candidates()