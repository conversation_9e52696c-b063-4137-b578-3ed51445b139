#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import yaml
from binance_trader import BinanceTrader

# 读取配置文件
with open('config/config.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

if __name__ == '__main__':
    try:
        trader = BinanceTrader(config)
        
        # 检查AWEUSDT持仓
        position = trader.get_position('AWEUSDT')
        print(f"AWEUSDT持仓信息:")
        if position:
            print(f"  持仓数量: {position.get('positionAmt', 0)}")
            print(f"  入场价格: {position.get('entryPrice', 0)}")
            print(f"  未实现盈亏: {position.get('unRealizedProfit', 0)}")
        else:
            print("  无持仓")
        
        # 检查AWEUSDT开放订单
        orders = trader.http.get('/fapi/v1/openOrders', {'symbol': 'AWEUSDT'})
        print(f"\nAWEUSDT开放订单:")
        if orders:
            for order in orders:
                print(f"  订单ID: {order.get('orderId')}")
                print(f"  类型: {order.get('type')}")
                print(f"  方向: {order.get('side')}")
                print(f"  数量: {order.get('origQty')}")
                print(f"  价格: {order.get('price', 'N/A')}")
                print(f"  止损价: {order.get('stopPrice', 'N/A')}")
                print(f"  状态: {order.get('status')}")
                print(f"  时间: {order.get('time')}")
                print("  ---")
        else:
            print("  无开放订单")
            
        # 检查所有持仓
        all_positions = trader.get_positions()
        active_positions = [p for p in all_positions if abs(float(p.get('positionAmt', 0))) > 0.001]
        print(f"\n当前所有活跃持仓 ({len(active_positions)}个):")
        for pos in active_positions:
            symbol = pos.get('symbol')
            qty = pos.get('positionAmt')
            print(f"  {symbol}: {qty}")
            
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()