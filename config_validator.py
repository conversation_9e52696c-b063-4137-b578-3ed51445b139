#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置参数校验模块
提供全面的配置参数验证功能，包括：
1. 移动止损参数的合理性检查
2. 参数范围和逻辑关系验证
3. 配置文件格式和完整性检查
4. 实时配置更新验证
"""

import yaml
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum


class ValidationLevel(Enum):
    """验证级别"""
    ERROR = "error"      # 错误级别，必须修复
    WARNING = "warning"  # 警告级别，建议修复
    INFO = "info"        # 信息级别，仅提示


@dataclass
class ValidationResult:
    """验证结果"""
    level: ValidationLevel
    field: str
    message: str
    current_value: Any = None
    suggested_value: Any = None


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.log = logger or logging.getLogger(__name__)
        self.validation_rules = self._init_validation_rules()
    
    def _init_validation_rules(self) -> Dict:
        """初始化验证规则"""
        return {
            # 移动止损基础参数规则
            'trailing_stop.enabled': {
                'type': bool,
                'required': False,
                'default': False
            },
            'trailing_stop.trigger_profit_pct': {
                'type': float,
                'required': True,
                'min': 0.01,  # 最小1%
                'max': 0.50,  # 最大50%
                'default': 0.08,
                'description': '移动止损触发浮盈百分比'
            },
            
            # 常规行情参数规则
            'trailing_stop.normal_market.profit_step_pct': {
                'type': float,
                'required': True,
                'min': 0.01,
                'max': 0.50,
                'default': 0.10,
                'description': '常规行情盈利步长百分比'
            },
            'trailing_stop.normal_market.stop_move_pct': {
                'type': float,
                'required': True,
                'min': 0.005,
                'max': 0.30,
                'default': 0.07,
                'description': '常规行情止损移动百分比'
            },
            
            # 极端行情参数规则
            'trailing_stop.extreme_market.enabled': {
                'type': bool,
                'required': False,
                'default': False
            },
            'trailing_stop.extreme_market.profit_step_pct': {
                'type': float,
                'required': False,
                'min': 0.01,
                'max': 0.30,
                'default': 0.05,
                'description': '极端行情盈利步长百分比'
            },
            'trailing_stop.extreme_market.stop_move_pct': {
                'type': float,
                'required': False,
                'min': 0.005,
                'max': 0.20,
                'default': 0.04,
                'description': '极端行情止损移动百分比'
            },
            'trailing_stop.extreme_market.volatility_threshold': {
                'type': float,
                'required': False,
                'min': 0.05,
                'max': 0.50,
                'default': 0.15,
                'description': '极端行情波动率阈值'
            },
            
            # 风控参数规则
            'trailing_stop.risk_control.only_move_up': {
                'type': bool,
                'required': False,
                'default': True
            },
            'trailing_stop.risk_control.min_stop_distance_pct': {
                'type': float,
                'required': False,
                'min': 0.005,
                'max': 0.10,
                'default': 0.02,
                'description': '最小止损距离百分比'
            },
            'trailing_stop.risk_control.max_trailing_count': {
                'type': int,
                'required': False,
                'min': 1,
                'max': 50,
                'default': 10,
                'description': '最大移动次数'
            }
        }
    
    def validate_config_file(self, config_path: str) -> Tuple[bool, List[ValidationResult]]:
        """验证配置文件"""
        results = []
        
        try:
            # 检查文件是否存在
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if not config_data:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'config_file',
                    '配置文件为空或格式错误'
                ))
                return False, results
            
            # 验证移动止损配置
            trailing_config = config_data.get('trailing_stop', {})
            if trailing_config:
                results.extend(self._validate_trailing_stop_config(trailing_config))
            else:
                results.append(ValidationResult(
                    ValidationLevel.INFO,
                    'trailing_stop',
                    '未找到移动止损配置，将使用默认值'
                ))
            
            # 检查是否有错误级别的问题
            has_errors = any(r.level == ValidationLevel.ERROR for r in results)
            
            return not has_errors, results
            
        except FileNotFoundError:
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                'config_file',
                f'配置文件不存在: {config_path}'
            ))
            return False, results
        except yaml.YAMLError as e:
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                'config_file',
                f'配置文件YAML格式错误: {e}'
            ))
            return False, results
        except Exception as e:
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                'config_file',
                f'配置文件验证异常: {e}'
            ))
            return False, results
    
    def _validate_trailing_stop_config(self, config: Dict) -> List[ValidationResult]:
        """验证移动止损配置"""
        results = []
        
        # 基础参数验证
        results.extend(self._validate_basic_params(config))
        
        # 常规行情参数验证
        normal_config = config.get('normal_market', {})
        results.extend(self._validate_normal_market_params(normal_config))
        
        # 极端行情参数验证
        extreme_config = config.get('extreme_market', {})
        if extreme_config.get('enabled', False):
            results.extend(self._validate_extreme_market_params(extreme_config))
        
        # 风控参数验证
        risk_config = config.get('risk_control', {})
        results.extend(self._validate_risk_control_params(risk_config))
        
        # 逻辑关系验证
        results.extend(self._validate_logical_relationships(config))
        
        return results
    
    def _validate_basic_params(self, config: Dict) -> List[ValidationResult]:
        """验证基础参数"""
        results = []
        
        # 验证触发条件
        trigger_pct = config.get('trigger_profit_pct')
        if trigger_pct is not None:
            if not isinstance(trigger_pct, (int, float)):
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'trigger_profit_pct',
                    '触发浮盈百分比必须是数字类型',
                    trigger_pct
                ))
            elif trigger_pct <= 0 or trigger_pct > 0.50:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'trigger_profit_pct',
                    '触发浮盈百分比必须在1%-50%之间',
                    trigger_pct,
                    0.08
                ))
            elif trigger_pct < 0.05:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    'trigger_profit_pct',
                    '触发浮盈百分比过低，可能导致频繁触发',
                    trigger_pct
                ))
        
        return results
    
    def _validate_normal_market_params(self, config: Dict) -> List[ValidationResult]:
        """验证常规行情参数"""
        results = []
        
        profit_step = config.get('profit_step_pct')
        stop_move = config.get('stop_move_pct')
        
        # 验证盈利步长
        if profit_step is not None:
            if not isinstance(profit_step, (int, float)):
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'normal_market.profit_step_pct',
                    '常规行情盈利步长必须是数字类型',
                    profit_step
                ))
            elif profit_step <= 0 or profit_step > 0.50:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'normal_market.profit_step_pct',
                    '常规行情盈利步长必须在1%-50%之间',
                    profit_step,
                    0.10
                ))
        
        # 验证止损移动幅度
        if stop_move is not None:
            if not isinstance(stop_move, (int, float)):
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'normal_market.stop_move_pct',
                    '常规行情止损移动幅度必须是数字类型',
                    stop_move
                ))
            elif stop_move <= 0 or stop_move > 0.30:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'normal_market.stop_move_pct',
                    '常规行情止损移动幅度必须在0.5%-30%之间',
                    stop_move,
                    0.07
                ))
        
        return results
    
    def _validate_extreme_market_params(self, config: Dict) -> List[ValidationResult]:
        """验证极端行情参数"""
        results = []
        
        profit_step = config.get('profit_step_pct')
        stop_move = config.get('stop_move_pct')
        volatility_threshold = config.get('volatility_threshold')
        
        # 验证极端行情盈利步长
        if profit_step is not None:
            if not isinstance(profit_step, (int, float)):
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'extreme_market.profit_step_pct',
                    '极端行情盈利步长必须是数字类型',
                    profit_step
                ))
            elif profit_step <= 0 or profit_step > 0.30:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'extreme_market.profit_step_pct',
                    '极端行情盈利步长必须在1%-30%之间',
                    profit_step,
                    0.05
                ))
        
        # 验证极端行情止损移动幅度
        if stop_move is not None:
            if not isinstance(stop_move, (int, float)):
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'extreme_market.stop_move_pct',
                    '极端行情止损移动幅度必须是数字类型',
                    stop_move
                ))
            elif stop_move <= 0 or stop_move > 0.20:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'extreme_market.stop_move_pct',
                    '极端行情止损移动幅度必须在0.5%-20%之间',
                    stop_move,
                    0.04
                ))
        
        # 验证波动率阈值
        if volatility_threshold is not None:
            if not isinstance(volatility_threshold, (int, float)):
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'extreme_market.volatility_threshold',
                    '波动率阈值必须是数字类型',
                    volatility_threshold
                ))
            elif volatility_threshold <= 0 or volatility_threshold > 0.50:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'extreme_market.volatility_threshold',
                    '波动率阈值必须在5%-50%之间',
                    volatility_threshold,
                    0.15
                ))
        
        return results
    
    def _validate_risk_control_params(self, config: Dict) -> List[ValidationResult]:
        """验证风控参数"""
        results = []
        
        min_distance = config.get('min_stop_distance_pct')
        max_count = config.get('max_trailing_count')
        
        # 验证最小止损距离
        if min_distance is not None:
            if not isinstance(min_distance, (int, float)):
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'risk_control.min_stop_distance_pct',
                    '最小止损距离必须是数字类型',
                    min_distance
                ))
            elif min_distance <= 0 or min_distance > 0.10:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'risk_control.min_stop_distance_pct',
                    '最小止损距离必须在0.5%-10%之间',
                    min_distance,
                    0.02
                ))
        
        # 验证最大移动次数
        if max_count is not None:
            if not isinstance(max_count, int):
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'risk_control.max_trailing_count',
                    '最大移动次数必须是整数类型',
                    max_count
                ))
            elif max_count <= 0 or max_count > 50:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    'risk_control.max_trailing_count',
                    '最大移动次数必须在1-50之间',
                    max_count,
                    10
                ))
        
        return results
    
    def _validate_logical_relationships(self, config: Dict) -> List[ValidationResult]:
        """验证逻辑关系"""
        results = []
        
        # 常规行情逻辑验证
        normal_config = config.get('normal_market', {})
        normal_profit_step = normal_config.get('profit_step_pct')
        normal_stop_move = normal_config.get('stop_move_pct')
        
        if normal_profit_step and normal_stop_move:
            if normal_stop_move >= normal_profit_step:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    'normal_market',
                    '常规行情止损移动幅度不应大于等于盈利步长，可能导致过度移动',
                    f'移动幅度: {normal_stop_move}, 盈利步长: {normal_profit_step}'
                ))
            
            # 检查移动效率
            move_efficiency = normal_stop_move / normal_profit_step
            if move_efficiency < 0.3:
                results.append(ValidationResult(
                    ValidationLevel.INFO,
                    'normal_market',
                    '常规行情移动效率较低，可能错失利润保护机会',
                    f'移动效率: {move_efficiency:.2%}'
                ))
            elif move_efficiency > 0.9:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    'normal_market',
                    '常规行情移动效率过高，可能导致过早平仓',
                    f'移动效率: {move_efficiency:.2%}'
                ))
        
        # 极端行情逻辑验证
        extreme_config = config.get('extreme_market', {})
        if extreme_config.get('enabled', False):
            extreme_profit_step = extreme_config.get('profit_step_pct')
            extreme_stop_move = extreme_config.get('stop_move_pct')
            
            if extreme_profit_step and extreme_stop_move:
                if extreme_stop_move >= extreme_profit_step:
                    results.append(ValidationResult(
                        ValidationLevel.WARNING,
                        'extreme_market',
                        '极端行情止损移动幅度不应大于等于盈利步长',
                        f'移动幅度: {extreme_stop_move}, 盈利步长: {extreme_profit_step}'
                    ))
        
        # 触发条件与移动参数的关系验证
        trigger_pct = config.get('trigger_profit_pct')
        if trigger_pct and normal_profit_step:
            if trigger_pct < normal_profit_step:
                results.append(ValidationResult(
                    ValidationLevel.INFO,
                    'trigger_logic',
                    '触发条件低于盈利步长，移动止损将在首次达到盈利步长时立即生效'
                ))
        
        return results
    
    def generate_validation_report(self, results: List[ValidationResult]) -> str:
        """生成验证报告"""
        if not results:
            return "✅ 配置验证通过，未发现问题"
        
        report_lines = ["📋 配置验证报告", "=" * 50]
        
        # 按级别分组
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        infos = [r for r in results if r.level == ValidationLevel.INFO]
        
        # 错误报告
        if errors:
            report_lines.append(f"\n❌ 错误 ({len(errors)}个):")
            for error in errors:
                report_lines.append(f"  • {error.field}: {error.message}")
                if error.current_value is not None:
                    report_lines.append(f"    当前值: {error.current_value}")
                if error.suggested_value is not None:
                    report_lines.append(f"    建议值: {error.suggested_value}")
        
        # 警告报告
        if warnings:
            report_lines.append(f"\n⚠️  警告 ({len(warnings)}个):")
            for warning in warnings:
                report_lines.append(f"  • {warning.field}: {warning.message}")
                if warning.current_value is not None:
                    report_lines.append(f"    当前值: {warning.current_value}")
        
        # 信息报告
        if infos:
            report_lines.append(f"\nℹ️  信息 ({len(infos)}个):")
            for info in infos:
                report_lines.append(f"  • {info.field}: {info.message}")
        
        # 总结
        report_lines.append(f"\n📊 验证总结:")
        report_lines.append(f"  • 错误: {len(errors)}个")
        report_lines.append(f"  • 警告: {len(warnings)}个")
        report_lines.append(f"  • 信息: {len(infos)}个")
        
        if errors:
            report_lines.append(f"\n❗ 请修复所有错误后再启用移动止损功能")
        elif warnings:
            report_lines.append(f"\n💡 建议修复警告项以获得更好的性能")
        else:
            report_lines.append(f"\n✅ 配置验证通过，可以安全使用")
        
        return "\n".join(report_lines)
    
    def validate_runtime_params(self, symbol: str, entry_price: float, 
                              current_price: float, side: str) -> List[ValidationResult]:
        """验证运行时参数"""
        results = []
        
        # 验证价格参数
        if entry_price <= 0:
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                f'{symbol}.entry_price',
                '入场价格必须大于0',
                entry_price
            ))
        
        if current_price <= 0:
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                f'{symbol}.current_price',
                '当前价格必须大于0',
                current_price
            ))
        
        # 验证方向参数
        if side not in ['LONG', 'SHORT']:
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                f'{symbol}.side',
                '持仓方向必须是LONG或SHORT',
                side
            ))
        
        # 验证价格合理性
        if entry_price > 0 and current_price > 0:
            price_change = abs(current_price - entry_price) / entry_price
            if price_change > 0.50:  # 50%以上的价格变化
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    f'{symbol}.price_change',
                    '价格变化幅度过大，请确认数据准确性',
                    f'{price_change:.2%}'
                ))
        
        return results