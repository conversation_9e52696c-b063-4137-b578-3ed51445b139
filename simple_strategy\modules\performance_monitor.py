import logging
import json
import time
from typing import Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    def __init__(self, log_file="logs/performance.json"):
        self.log_file = Path(log_file)
        self.log_file.parent.mkdir(exist_ok=True)
        self.metrics = {
            'start_time': time.time(),
            'orders_executed': 0,
            'orders_failed': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'winning_trades': 0,
            'losing_trades': 0,
            'api_calls': 0,
            'errors': 0
        }
    
    def record_order(self, success: bool, pnl: float = 0.0):
        """记录订单执行情况"""
        if success:
            self.metrics['orders_executed'] += 1
            self.metrics['total_pnl'] += pnl
            if pnl > 0:
                self.metrics['winning_trades'] += 1
            else:
                self.metrics['losing_trades'] += 1
        else:
            self.metrics['orders_failed'] += 1
        
        self._update_drawdown(pnl)
        self._save_metrics()
    
    def record_api_call(self):
        """记录API调用"""
        self.metrics['api_calls'] += 1
        self._save_metrics()
    
    def record_error(self):
        """记录错误"""
        self.metrics['errors'] += 1
        self._save_metrics()
    
    def _update_drawdown(self, pnl: float):
        """更新最大回撤"""
        # 简化实现：仅根据总盈亏更新回撤
        if self.metrics['total_pnl'] < 0 and abs(self.metrics['total_pnl']) > self.metrics['max_drawdown']:
            self.metrics['max_drawdown'] = abs(self.metrics['total_pnl'])
    
    def _save_metrics(self):
        """保存指标到文件"""
        try:
            with open(self.log_file, 'w') as f:
                json.dump(self.metrics, f, indent=2)
        except Exception as e:
            logger.error(f"保存性能指标失败: {e}")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        total_trades = self.metrics['winning_trades'] + self.metrics['losing_trades']
        win_rate = self.metrics['winning_trades'] / total_trades if total_trades > 0 else 0
        
        return {
            '运行时间': time.time() - self.metrics['start_time'],
            '订单执行数': self.metrics['orders_executed'],
            '订单失败数': self.metrics['orders_failed'],
            '总盈亏': self.metrics['total_pnl'],
            '最大回撤': self.metrics['max_drawdown'],
            '胜率': f"{win_rate:.2%}",
            'API调用数': self.metrics['api_calls'],
            '错误数': self.metrics['errors']
        }
    
    def reset(self):
        """重置指标"""
        self.metrics = {
            'start_time': time.time(),
            'orders_executed': 0,
            'orders_failed': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'winning_trades': 0,
            'losing_trades': 0,
            'api_calls': 0,
            'errors': 0
        }
        self._save_metrics()