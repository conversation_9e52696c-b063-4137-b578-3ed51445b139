import logging
from typing import Dict, Any, List
from datetime import datetime

logger = logging.getLogger(__name__)

class RiskManager:
    def __init__(self, config: dict):
        self.config = config
        self.max_account_risk = config['risk_management']['max_account_risk']
        self.max_correlation = config['risk_management']['max_correlation']
        self.kill_switch_enabled = config['risk_management']['enable_kill_switch']
        self.kill_switch_activated = False
    
    def check_account_risk(self, positions: Dict[str, Any], account_balance: float) -> bool:
        """检查账户风险"""
        try:
            if account_balance <= 0:
                logger.error("账户余额为零或负数")
                return False
            
            # 计算总风险敞口
            total_risk = 0
            for symbol, position in positions.items():
                position_value = position.get('amount', 0) * position.get('entry_price', 0)
                total_risk += position_value
            
            risk_ratio = total_risk / account_balance
            logger.info(f"账户风险比率: {risk_ratio:.4f}")
            
            if risk_ratio > self.max_account_risk:
                logger.warning(f"账户风险超过限制: {risk_ratio:.4f} > {self.max_account_risk}")
                return False
            
            return True
        except Exception as e:
            logger.error(f"检查账户风险失败: {e}")
            return False
    
    def check_correlation(self, new_symbol: str, positions: Dict[str, Any]) -> bool:
        """检查相关性风险"""
        # 简化实现：检查是否已有相同基础货币的持仓
        try:
            base_currency = new_symbol.split('/')[0]
            for symbol in positions.keys():
                if symbol.split('/')[0] == base_currency:
                    logger.warning(f"相关性风险: 已持有{base_currency}相关仓位")
                    return False
            return True
        except Exception as e:
            logger.error(f"检查相关性风险失败: {e}")
            return True
    
    def check_kill_switch(self) -> bool:
        """检查是否触发了紧急停止开关"""
        return self.kill_switch_enabled and self.kill_switch_activated
    
    def activate_kill_switch(self):
        """激活紧急停止开关"""
        self.kill_switch_activated = True
        logger.critical("紧急停止开关已激活")
    
    def reset_kill_switch(self):
        """重置紧急停止开关"""
        self.kill_switch_activated = False
        logger.info("紧急停止开关已重置")
    
    def validate_order(self, symbol: str, amount: float, price: float, account_balance: float) -> bool:
        """验证订单"""
        try:
            order_value = amount * price
            if order_value > account_balance * 0.1:  # 单笔订单不超过账户的10%
                logger.warning(f"订单金额过大: {order_value} > {account_balance * 0.1}")
                return False
            return True
        except Exception as e:
            logger.error(f"验证订单失败: {e}")
            return False