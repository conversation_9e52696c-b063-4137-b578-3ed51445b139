import requests
import hmac
import hashlib
import time
import logging
from requests.adapters import H<PERSON><PERSON><PERSON>pter
from network_environment_detector import NetworkEnvironmentDetector, NetworkEnvironment

class HttpClient:
    """Binance API HTTP客户端
    
    处理API请求、签名和重试逻辑
    """
    def __init__(self, api_key, api_secret, base_url='https://fapi.binance.com', timeout=30, proxy_cfg=None, verify_ssl=True):
        self.key = api_key
        self.secret = api_secret
        self.base = base_url.rstrip('/') 
        self.timeout = timeout
        self.session = requests.Session()
        self.verify_ssl = verify_ssl
        
        # 设置日志记录器
        self.logger = logging.getLogger(__name__)
        # self.logger.setLevel(logging.INFO)  # 改为INFO级别以记录代理配置信息
        
        # 禁用urllib3的警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 添加缓存以避免递归调用
        self._exchange_info_cache = None
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 缓存5分钟
        
        # 智能网络环境检测和代理配置
        self._setup_intelligent_proxy(proxy_cfg)
        
    def _setup_intelligent_proxy(self, proxy_cfg):
        """智能网络环境检测和代理配置
        
        根据网络环境自动决定代理策略：
        - 阿里云马来西亚服务器：严格禁用代理
        - 本地大陆环境：必须启用代理
        - 其他环境：根据连通性测试决定
        """
        import os
        
        # 强制清除系统代理环境变量
        proxy_env_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        for var in proxy_env_vars:
            if os.environ.get(var):
                # self.logger.warning(f"检测到系统代理环境变量 {var}={os.environ[var]}，强制清除")
                print(f"检测到系统代理环境变量 {var}={os.environ[var]}，强制清除")
                del os.environ[var]
        
        # 简化代理配置，避免复杂的网络检测导致递归
        try:
            # 简单的IP检测，避免使用NetworkEnvironmentDetector
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            
            print(f"本机IP: {local_ip}")
            
            # 检查是否为阿里云马来西亚服务器
            is_aliyun_malaysia = (local_ip == "*************" or local_ip.startswith("47.250."))
            
            if is_aliyun_malaysia:
                # 阿里云马来西亚服务器：严格禁用代理
                print("阿里云马来西亚服务器环境，严格禁用代理")
                self.session.proxies = {}
                self.session.trust_env = False
            else:
                # 其他环境：使用传统代理配置
                if proxy_cfg and proxy_cfg.get('enabled', True):
                    proxy_host = proxy_cfg.get('host', '127.0.0.1')
                    proxy_port = proxy_cfg.get('port', 7897)
                    proxy_url = f"http://{proxy_host}:{proxy_port}"
                    
                    # 确保代理配置正确设置
                    self.session.proxies = {
                        'http': proxy_url,
                        'https': proxy_url
                    }
                    self.session.trust_env = False  # 禁用环境变量代理
                    print(f"启用代理模式: {proxy_url}")
                else:
                    # 默认启用本地代理
                    proxy_url = "http://127.0.0.1:7897"
                    self.session.proxies = {
                        'http': proxy_url,
                        'https': proxy_url
                    }
                    self.session.trust_env = False  # 禁用环境变量代理
                    print(f"启用默认代理模式: {proxy_url}")
            
            # 记录最终配置
            print(f"最终代理配置: {self.session.proxies}")
            
        except Exception as e:
            print(f"代理配置失败: {e}")
            
            # 降级策略：根据传统方法判断
            self._fallback_proxy_setup(proxy_cfg)
            
        # 配置连接池和重试策略，提高并发性能
        from urllib3.util.retry import Retry
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS", "POST", "DELETE"],
            backoff_factor=1
        )
        
        # 配置HTTPAdapter，增加连接池大小
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=20,  # 连接池大小
            pool_maxsize=20,      # 每个连接池的最大连接数
            pool_block=False      # 不阻塞，避免并发问题
        )
        
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
            
        # 简化连接测试，避免递归
        # self._test_connection()

    def _fallback_proxy_setup(self, proxy_cfg):
        """降级代理配置策略"""
        try:
            if proxy_cfg and proxy_cfg.get('enabled', True):
                proxy_host = proxy_cfg.get('host', '127.0.0.1')
                proxy_port = proxy_cfg.get('port', 7897)
                proxy_url = f"http://{proxy_host}:{proxy_port}"
                
                # 确保代理配置正确设置，使用直接赋值而不是update
                self.session.proxies = {
                    'http': proxy_url,
                    'https': proxy_url
                }
                self.session.trust_env = False  # 禁用环境变量代理
                print(f"降级策略：启用代理模式 {proxy_url}")
            else:
                # 默认启用本地代理
                proxy_url = "http://127.0.0.1:7897"
                self.session.proxies = {
                    'http': proxy_url,
                    'https': proxy_url
                }
                self.session.trust_env = False  # 禁用环境变量代理
                print(f"降级策略：启用默认代理模式 {proxy_url}")
                
        except Exception as e:
            print(f"降级代理配置也失败: {e}")
            # 最后的降级：直连
            self.session.proxies = {}
            self.session.trust_env = False
            print("最终降级：使用直连模式")

    def _test_connection(self):
        """简化的连接测试，避免递归"""
        try:
            # 简单的连接测试，不使用复杂的网络检测
            test_url = "https://api.binance.com/api/v3/ping"
            response = requests.get(test_url, timeout=5, proxies=self.session.proxies)
            if response.status_code == 200:
                print("网络连接测试成功")
            else:
                print(f"网络连接测试失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"网络连接测试异常: {e}")
            # 不进行任何递归调用或复杂处理

    def _setup_proxy(self, proxy_cfg):
        """保留原有方法名以兼容性（已被_setup_intelligent_proxy替代）"""
        pass

    def _sign(self, params):
        """使用HMAC SHA256生成签名
        params: dict - 参数字典 
        """
        try:
            # 获取服务器时间
            server_time = self._get_server_time()
            
            # 添加时间戳
            params['timestamp'] = str(server_time)
            
            # 按字母序排序键名并构建查询字符串
            sorted_params = sorted(params.items(), key=lambda x: x[0])
            # 确保数值类型转为字符串
            query_string = '&'.join([f"{k}={str(v)}" for k, v in sorted_params])
            
            # 生成签名
            signature = hmac.new(
                self.secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # 移除日志调用以避免递归
            # self.logger.debug(f"参数: {params}")
            # self.logger.debug(f"签名: {signature}")
            
            return signature, query_string
        except Exception as e:
            # 移除日志调用以避免递归，直接抛出异常
            # self.logger.error(f"生成签名失败: {e}")
            raise

    def _get_server_time(self):
        """获取服务器时间（完全独立实现避免递归）"""
        # 添加递归深度检查
        import sys
        if hasattr(self, '_server_time_recursion_depth'):
            self._server_time_recursion_depth += 1
            if self._server_time_recursion_depth > 3:
                # 递归深度过深，直接返回本地时间
                return int(time.time() * 1000)
        else:
            self._server_time_recursion_depth = 1
        
        try:
            # 完全独立的HTTP请求，创建新的session避免任何依赖
            import requests
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            
            # 创建完全独立的session
            independent_session = requests.Session()
            
            # 硬编码代理配置
            proxies = {
                'http': 'http://127.0.0.1:7897',
                'https': 'http://127.0.0.1:7897'
            }
            
            # 硬编码URL
            base_url = "https://fapi.binance.com"
            
            # 尝试代理连接（简化错误处理）
            try:
                resp = independent_session.get(
                    f"{base_url}/fapi/v1/time", 
                    timeout=5,  # 减少超时时间
                    verify=False,
                    proxies=proxies
                )
                if resp.status_code == 200:
                    result = resp.json()['serverTime']
                    self._server_time_recursion_depth = 0  # 重置计数器
                    return result
            except:
                pass  # 静默失败，尝试直连
            
            # 代理失败，尝试直连（简化错误处理）
            try:
                resp = independent_session.get(
                    f"{base_url}/fapi/v1/time", 
                    timeout=5,  # 减少超时时间
                    verify=False
                )
                if resp.status_code == 200:
                    result = resp.json()['serverTime']
                    self._server_time_recursion_depth = 0  # 重置计数器
                    return result
            except:
                pass  # 静默失败
                    
        except:
            pass  # 完全静默失败，避免任何可能的递归
        finally:
            # 确保计数器被重置
            if hasattr(self, '_server_time_recursion_depth'):
                self._server_time_recursion_depth = 0
        
        # 如果获取失败则使用本地时间
        return int(time.time() * 1000)

    def _handle_response(self, response, path):
        try:
            # 打印响应内容
            response_text = response.text
            # 移除日志调用以避免递归
            # self.logger.debug(f"响应内容: {response_text}")
            
            # 检查响应状态码
            if response.status_code == 429:
                # 处理API频率限制
                retry_after = response.headers.get('Retry-After', '60')
                # 移除日志调用以避免递归
                # self.logger.warning(f"API频率限制触发，建议等待 {retry_after} 秒后重试")
                return {'error': f'API频率限制，请等待 {retry_after} 秒', 'retry_after': int(retry_after), 'rate_limited': True}
            
            if response.status_code != 200:
                # 移除日志调用以避免递归
                # self.logger.error(f"HTTP状态码错误: {response.status_code}, 响应内容: {response_text}")
                return {'error': f'HTTP错误: {response.status_code}, {response_text}'}
                
            # 尝试解析JSON
            try:
                data = response.json()
            except Exception as e:
                # 移除日志调用以避免递归
                # self.logger.error(f"JSON解析失败: {response_text}")
                return {'error': f'解析响应失败: {str(e)}'}
            
            # 成功响应且没有错误码
            if not isinstance(data, dict) or 'code' not in data:
                return data
                
            # 处理特殊情况
            if path == '/fapi/v1/marginType':
                if data.get('code') == 200 or data.get('msg', '').startswith('No need to change'):
                    return {'success': True}
            
            if path == '/fapi/v1/leverage':
                if data.get('code') == 200 or data.get('leverage'):
                    return {'success': True, 'leverage': data.get('leverage')}
            
            # 处理签名错误
            if data.get('code') == -1022:  # 签名错误的代码
                # 移除日志调用以避免递归
                # self.logger.error("签名验证失败，请检查时间同步")
                return {'error': '签名验证失败，请检查时间同步'}
            
            # 处理API频率限制错误码
            if data.get('code') == -1003:  # Too many requests
                # 移除日志调用以避免递归
                # self.logger.warning("API请求频率过高，触发限制")
                return {'error': 'API请求频率过高', 'rate_limited': True}
            
            # 处理"无需更改保证金类型"的情况
            if data.get('code') == -4046 and 'No need to change margin type' in data.get('msg', ''):
                # 移除日志调用以避免递归
                # self.logger.info("保证金类型已正确设置，无需更改")
                return {'success': True}
            
            # 处理"未知订单"的情况
            if data.get('code') == -2011 and 'Unknown order sent' in data.get('msg', ''):
                # 移除日志调用以避免递归
                # self.logger.warning("尝试操作不存在的订单")
                return {'error': '订单不存在或已成交'}
            
            # 处理一般情况
            if data.get('code') == 200:
                return data
            
            # 处理API错误
            error_msg = data.get('msg', 'Unknown error')
            # 移除日志调用以避免递归
            # self.logger.error(f"API错误: {error_msg}")
            return {'error': f"API错误: {error_msg}"}
            
        except Exception as e:
            # 移除日志调用以避免递归
            # self.logger.error(f"解析响应失败: {str(e)}")
            return {'error': f"解析响应失败: {str(e)}"}



    def get(self, path, params=None):
        """GET请求"""
        return self._request_with_retry('GET', path, params)

    def post(self, path, params=None):
        """POST请求"""
        return self._request_with_retry('POST', path, params)
            
    def delete(self, path, params=None):
        """DELETE请求"""
        return self._request_with_retry('DELETE', path, params)

    def _is_signed_endpoint(self, path):
        """判断接口是否需要签名
        
        根据Binance API文档：
        - 市场数据接口（如klines、depth、ticker等）不需要签名
        - 账户相关接口（如position、balance、order等）需要签名
        """
        # 公开的市场数据接口，不需要签名
        public_endpoints = [
            '/fapi/v1/ping',
            '/fapi/v1/time',
            '/fapi/v1/exchangeInfo',
            '/fapi/v1/depth',
            '/fapi/v1/trades',
            '/fapi/v1/historicalTrades',
            '/fapi/v1/aggTrades',
            '/fapi/v1/klines',  # K线数据
            '/fapi/v1/continuousKlines',
            '/fapi/v1/indexPriceKlines',
            '/fapi/v1/markPriceKlines',
            '/fapi/v1/premiumIndex',
            '/fapi/v1/fundingRate',
            '/fapi/v1/ticker/24hr',
            '/fapi/v1/ticker/price',
            '/fapi/v1/ticker/bookTicker',
            '/fapi/v1/openInterest',
            '/fapi/v1/openInterestHist',
            '/fapi/v1/topLongShortAccountRatio',
            '/fapi/v1/topLongShortPositionRatio',
            '/fapi/v1/globalLongShortAccountRatio',
            '/fapi/v1/takerlongshortRatio',
            '/fapi/v1/lvtKlines',
            '/fapi/v1/indexInfo',
            '/fapi/v1/assetIndex',
            '/fapi/v2/balance',
            '/fapi/v2/account'
        ]
        
        return path not in public_endpoints

    def _request_with_retry(self, method, path, params=None, max_retries=3):
        """带重试机制的请求方法"""
        headers = {'X-MBX-APIKEY': self.key}
        params = params or {}
        
        # 所有参数值转为字符串
        params = {k: str(v) for k, v in params.items()}
        
        # 判断是否需要签名
        if self._is_signed_endpoint(path):
            # 需要签名的接口
            signature, query = self._sign(params)
            final_query = f"{query}&signature={signature}"
        else:
            # 公开接口，不需要签名
            if params:
                query_parts = [f"{k}={str(v)}" for k, v in params.items()]
                final_query = '&'.join(query_parts)
            else:
                final_query = ""
        
        # 移除日志调用以避免递归
        # self.logger.debug(f"最终查询字符串: {final_query}")
        
        for attempt in range(max_retries + 1):
            try:
                if final_query:
                    url = f"{self.base}{path}?{final_query}"
                else:
                    url = f"{self.base}{path}"
                # 移除日志调用以避免递归
                # self.logger.debug(f"发送{method}请求 (尝试 {attempt + 1}/{max_retries + 1}): URL={url}")
                
                if method == 'GET':
                    response = self.session.get(url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
                elif method == 'POST':
                    response = self.session.post(url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
                elif method == 'DELETE':
                    response = self.session.delete(url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                result = self._handle_response(response, path)
                
                # 如果是频率限制错误，进行智能重试
                if isinstance(result, dict) and result.get('rate_limited') and attempt < max_retries:
                    retry_after = result.get('retry_after', 60)
                    # 指数退避策略，但不超过Retry-After建议的时间
                    wait_time = min(retry_after, (2 ** attempt) * 5)
                    # 移除日志调用以避免递归
                    # self.logger.warning(f"API频率限制，等待 {wait_time} 秒后重试 (尝试 {attempt + 1}/{max_retries + 1})")
                    time.sleep(wait_time)
                    continue
                
                # 如果是网络错误，进行重试
                if isinstance(result, dict) and 'error' in result and any(err in result['error'].lower() for err in ['connection', 'timeout', 'network']):
                    if attempt < max_retries:
                        wait_time = (2 ** attempt) * 2  # 指数退避
                        # 移除日志调用以避免递归
                        # self.logger.warning(f"网络错误，等待 {wait_time} 秒后重试: {result['error']}")
                        time.sleep(wait_time)
                        continue
                
                return result
                
            except Exception as e:
                error_msg = str(e)
                # 移除日志调用以避免递归
                # self.logger.error(f"{method}请求失败 {path} (尝试 {attempt + 1}/{max_retries + 1}): {error_msg}")
                
                # 如果是最后一次尝试，返回错误
                if attempt == max_retries:
                    return {'error': f"{method}请求失败: {error_msg}"}
                
                # 网络相关错误进行重试
                if any(err in error_msg.lower() for err in ['connection', 'timeout', 'proxy', 'ssl']):
                    wait_time = (2 ** attempt) * 2  # 指数退避
                    # 移除日志调用以避免递归
                    # self.logger.warning(f"网络异常，等待 {wait_time} 秒后重试: {error_msg}")
                    time.sleep(wait_time)
                else:
                    # 非网络错误直接返回
                    return {'error': f"{method}请求失败: {error_msg}"}

    def _get_exchange_info_cached(self):
        """获取缓存的交易所信息，避免递归调用"""
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._exchange_info_cache is not None and 
            current_time - self._cache_timestamp < self._cache_ttl):
            return self._exchange_info_cache
        
        # 直接调用API，避免通过self.get()造成递归
        try:
            headers = {'X-MBX-APIKEY': self.key}
            url = f"{self.base}/fapi/v1/exchangeInfo"
            response = self.session.get(url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
            
            if response.status_code == 200:
                data = response.json()
                # 更新缓存
                self._exchange_info_cache = data
                self._cache_timestamp = current_time
                return data
            else:
                print(f"获取交易所信息失败: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"获取交易所信息异常: {e}")
            return None

    def get_margin_type(self, symbol):
        """获取保证金模式"""
        # 使用缓存的交易所信息，避免递归调用
        info = self._get_exchange_info_cached()
        if not info or not isinstance(info, dict) or not info.get('symbols'):
            print("获取交易对信息失败")
            return 'CROSS'
            
        found = False
        symbols = info.get('symbols', [])
        if not isinstance(symbols, list):
            symbols = []
        for s in symbols:
            if isinstance(s, dict) and s.get('symbol') == symbol:
                found = True
                break
                
        if not found:
            print(f"交易对 {symbol} 不存在")
            return 'CROSS'
            
        # 获取仓位信息 - 直接调用API避免递归
        try:
            headers = {'X-MBX-APIKEY': self.key}
            params = {'symbol': symbol}
            signature, query = self._sign(params)
            url = f"{self.base}/fapi/v2/positionRisk?{query}&signature={signature}"
            response = self.session.get(url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    margin_type = result[0].get('marginType', '').upper()
                    if margin_type in ('ISOLATED', 'CROSS'):
                        return margin_type
        except Exception as e:
            print(f"获取保证金模式失败: {e}")
        
        return 'CROSS'  # 如果获取失败，默认返回全仓

    def get_leverage(self, symbol):
        """获取当前杠杆倍数"""
        # 使用缓存的交易所信息，避免递归调用
        info = self._get_exchange_info_cached()
        if not info or not isinstance(info, dict) or not info.get('symbols'):
            print("获取交易对信息失败")
            return 1
            
        found = False
        symbols = info.get('symbols', [])
        if not isinstance(symbols, list):
            symbols = []
        for s in symbols:
            if isinstance(s, dict) and s.get('symbol') == symbol:
                found = True
                break
                
        if not found:
            print(f"交易对 {symbol} 不存在")
            return 1
            
        # 获取仓位信息 - 直接调用API避免递归
        try:
            headers = {'X-MBX-APIKEY': self.key}
            params = {'symbol': symbol}
            signature, query = self._sign(params)
            url = f"{self.base}/fapi/v2/positionRisk?{query}&signature={signature}"
            response = self.session.get(url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    leverage = result[0].get('leverage', '1')
                    try:
                        return int(float(leverage))
                    except (ValueError, TypeError):
                        return 1
        except Exception as e:
            print(f"获取杠杆倍数失败: {e}")
        
        return 1  # 如果获取失败，默认返回1倍杠杆

    def set_margin_type(self, symbol, margin_type='ISOLATED'):
        """设置保证金模式"""
        # 使用缓存的交易所信息，避免递归调用
        info = self._get_exchange_info_cached()
        if not info or not isinstance(info, dict) or not info.get('symbols'):
            print("获取交易对信息失败")
            return {'error': '获取交易对信息失败'}
            
        found = False
        symbols = info.get('symbols', [])
        if not isinstance(symbols, list):
            symbols = []
        for s in symbols:
            if isinstance(s, dict) and s.get('symbol') == symbol:
                found = True
                break
                
        if not found:
            print(f"交易对 {symbol} 不存在")
            return {'error': f"交易对 {symbol} 不存在"}
            
        response = self.post('/fapi/v1/marginType', {
            'symbol': symbol,
            'marginType': margin_type.upper()  # 确保大写
        })
        if response and ('success' in response or (isinstance(response, dict) and response.get('code') == 200) or "No need to change margin type" in str(response)):
            print(f"成功设置{symbol}为{margin_type}模式")
            return {'success': True}
        print(f"设置{symbol}保证金模式失败: {response.get('error', '未知错误') if response else '未知错误'}")
        return response

    def set_leverage(self, symbol, leverage):
        """设置杠杆倍数"""
        # 使用缓存的交易所信息，避免递归调用
        info = self._get_exchange_info_cached()
        if not info or not isinstance(info, dict) or not info.get('symbols'):
            print("获取交易对信息失败")
            return {'error': '获取交易对信息失败'}
            
        found = False
        symbols = info.get('symbols', [])
        if not isinstance(symbols, list):
            symbols = []
        for s in symbols:
            if isinstance(s, dict) and s.get('symbol') == symbol:
                found = True
                break
                
        if not found:
            print(f"交易对 {symbol} 不存在")
            return {'error': f"交易对 {symbol} 不存在"}
        
        try:
            lev = int(leverage)
            response = self.post('/fapi/v1/leverage', {
                'symbol': symbol,
                'leverage': lev  # 确保是整数
            })
            if response and ('leverage' in response or 'success' in response or (isinstance(response, dict) and response.get('code') == 200)):
                print(f"成功设置{symbol}杠杆为{lev}倍")
                return {'success': True, 'leverage': lev}
            print(f"设置{symbol}杠杆失败: {response.get('error', '未知错误') if response else '未知错误'}")
            return response
        except Exception as e:
            error = f"设置杠杆发生异常: {str(e)}"
            print(error)
            return {'error': error}
