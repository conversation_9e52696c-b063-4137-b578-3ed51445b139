{"risk_management": {"enabled": true, "max_daily_loss": 0.05, "max_drawdown": 0.1, "max_correlation": 0.7, "position_concentration": 0.3}, "account_limits": {"max_account_risk": 0.02, "max_position_risk": 0.01, "max_leverage": 3, "min_account_balance": 1000, "emergency_stop_loss": 0.15}, "position_limits": {"max_positions": 3, "max_position_size_usd": 1000, "min_position_size_usd": 10, "max_single_symbol_exposure": 0.3, "max_sector_exposure": 0.5}, "order_limits": {"max_order_size_usd": 500, "max_orders_per_minute": 10, "max_orders_per_hour": 100, "max_daily_orders": 1000, "order_timeout": 300}, "volatility_control": {"enabled": true, "volatility_threshold": 0.05, "volatility_period": 24, "high_volatility_action": "reduce_size", "volatility_multiplier": 0.5}, "correlation_control": {"enabled": true, "correlation_threshold": 0.7, "correlation_period": 30, "high_correlation_action": "skip_entry", "correlation_symbols": ["BTC", "ETH"]}, "drawdown_control": {"enabled": true, "max_drawdown": 0.1, "drawdown_period": 30, "drawdown_action": "stop_trading", "recovery_threshold": 0.05}, "liquidity_control": {"enabled": true, "min_volume_24h": 1000000, "min_market_cap": *********, "max_spread": 0.002, "min_depth": 50000}, "emergency_controls": {"enabled": true, "api_error_threshold": 10, "network_error_threshold": 5, "order_failure_threshold": 5, "emergency_stop_duration": 3600}, "monitoring": {"enabled": true, "check_interval": 60, "alert_channels": ["log", "email"], "metrics": ["account_balance", "unrealized_pnl", "daily_pnl", "position_count", "order_success_rate", "api_error_rate"]}, "alerts": {"enabled": true, "alert_levels": {"info": {"enabled": true, "channels": ["log"]}, "warning": {"enabled": true, "channels": ["log", "console"]}, "critical": {"enabled": true, "channels": ["log", "console", "email"]}}, "alert_conditions": {"high_drawdown": {"threshold": 0.05, "level": "warning"}, "max_drawdown": {"threshold": 0.1, "level": "critical"}, "low_balance": {"threshold": 500, "level": "warning"}, "api_errors": {"threshold": 5, "level": "warning"}, "order_failures": {"threshold": 3, "level": "warning"}}}}