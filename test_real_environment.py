#!/usr/bin/env python3
"""
实际环境测试脚本 - 使用完整的MakerChannelStrategy测试K线获取
"""

import sys
import json
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from binance_trader import BinanceTrader
from strategy.maker_channel import MakerChannelStrategy

def test_real_environment():
    """在实际环境中测试K线获取"""
    
    # 加载配置
    with open('config/config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 添加缺失的配置项
    config['first_nominal'] = 100
    config['max_nominal'] = 1000
    
    # 初始化交易器和策略
    trader = BinanceTrader(config)
    strategy = MakerChannelStrategy(trader, config)
    
    # 测试符号
    test_symbols = ['SSVUSDT', 'BCHUSDT', 'BTCUSDT']
    
    print("实际环境K线获取测试:")
    print("=" * 60)
    
    for symbol in test_symbols:
        print(f"\n测试 {symbol}:")
        
        try:
            # 使用策略的get_klines方法（这是实际使用的方法）
            df = strategy.get_klines(symbol, interval='15m', limit=200)
            
            if df is not None and not df.empty:
                print(f"  ✓ 成功获取 {len(df)} 条K线数据")
                print(f"  - 数据范围: {df.index[0]} 到 {df.index[-1]}")
                print(f"  - 列名: {list(df.columns)}")
                
                if len(df) < 20:
                    print(f"  ⚠️  警告: 数据不足20条，仅有{len(df)}条")
                else:
                    print(f"  ✓ 数据充足，满足最低要求")
                    
            else:
                print(f"  ❌ 获取失败: 返回None或空DataFrame")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
            import traceback
            print(f"  详细错误: {traceback.format_exc()}")
    
    print("\n" + "=" * 60)
    
    # 测试缓存状态
    print("缓存状态检查:")
    cache_manager = strategy.strategy_optimizer.cache_manager
    
    for symbol in test_symbols:
        cache_key = f"klines_{symbol}_15m_200"
        cached_data = cache_manager.get(cache_key)
        
        if cached_data is not None:
            print(f"  {symbol}: 缓存存在，{len(cached_data)} 条数据")
        else:
            print(f"  {symbol}: 无缓存数据")
    
    print("\n测试完成")

if __name__ == "__main__":
    test_real_environment()