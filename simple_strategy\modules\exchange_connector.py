import ccxt
import asyncio
import logging
from typing import Dict, List, Any
import time

logger = logging.getLogger(__name__)

class ExchangeConnector:
    def __init__(self, config: dict):
        self.config = config
        self.exchange = None
        self.rate_limiter = RateLimiter(config['exchange']['rate_limit'])
        self._initialize_exchange()
    
    def _initialize_exchange(self):
        """初始化交易所连接"""
        try:
            exchange_config = {
                'apiKey': self.config['exchange']['api_key'],
                'secret': self.config['exchange']['api_secret'],
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future'
                }
            }
            
            if self.config['exchange']['testnet']:
                exchange_config['urls'] = {
                    'api': {
                        'public': 'https://testnet.binancefuture.com/public',
                        'private': 'https://testnet.binancefuture.com/private'
                    }
                }
            
            self.exchange = getattr(ccxt, self.config['exchange']['name'])(exchange_config)
            logger.info(f"成功连接到交易所: {self.config['exchange']['name']}")
        except Exception as e:
            logger.error(f"初始化交易所连接失败: {e}")
            raise
    
    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> List[List]:
        """获取K线数据"""
        try:
            await self.rate_limiter.acquire()
            data = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_ohlcv, symbol, timeframe, limit
            )
            return data
        except Exception as e:
            logger.error(f"获取K线数据失败 {symbol}: {e}")
            return []
    
    async def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """获取 ticker 数据"""
        try:
            await self.rate_limiter.acquire()
            ticker = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_ticker, symbol
            )
            return ticker
        except Exception as e:
            logger.error(f"获取 ticker 数据失败 {symbol}: {e}")
            return {}
    
    async def fetch_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            await self.rate_limiter.acquire()
            balance = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_balance
            )
            return balance
        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            return {}
    
    async def fetch_positions(self) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        try:
            await self.rate_limiter.acquire()
            positions = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_positions
            )
            return positions
        except Exception as e:
            logger.error(f"获取持仓信息失败: {e}")
            return []
    
    async def create_limit_order(self, symbol: str, side: str, amount: float, price: float) -> Dict[str, Any]:
        """创建限价单"""
        try:
            await self.rate_limiter.acquire()
            order = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.create_limit_order, symbol, side, amount, price
            )
            logger.info(f"创建限价单成功: {symbol} {side} {amount}@{price}")
            return order
        except Exception as e:
            logger.error(f"创建限价单失败 {symbol}: {e}")
            return {}
    
    async def create_market_order(self, symbol: str, side: str, amount: float) -> Dict[str, Any]:
        """创建市价单"""
        try:
            await self.rate_limiter.acquire()
            order = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.create_market_order, symbol, side, amount
            )
            logger.info(f"创建市价单成功: {symbol} {side} {amount}")
            return order
        except Exception as e:
            logger.error(f"创建市价单失败 {symbol}: {e}")
            return {}
    
    async def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """取消订单"""
        try:
            await self.rate_limiter.acquire()
            result = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.cancel_order, order_id, symbol
            )
            logger.info(f"取消订单成功: {order_id}")
            return result
        except Exception as e:
            logger.error(f"取消订单失败 {order_id}: {e}")
            return {}

class RateLimiter:
    def __init__(self, rate_limit: int):
        self.rate_limit = rate_limit
        self.tokens = rate_limit
        self.last_refill_time = time.time()
    
    async def acquire(self):
        """获取令牌"""
        while self.tokens < 1:
            self._refill()
            if self.tokens < 1:
                await asyncio.sleep(0.1)
        self.tokens -= 1
    
    def _refill(self):
        """补充令牌"""
        now = time.time()
        elapsed = now - self.last_refill_time
        new_tokens = elapsed * self.rate_limit
        if new_tokens > 0:
            self.tokens = min(self.rate_limit, self.tokens + new_tokens)
            self.last_refill_time = now