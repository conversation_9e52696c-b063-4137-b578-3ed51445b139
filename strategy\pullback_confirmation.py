"""
回踩确认模块 - 实现0.38%回踩限价单机制
优化入场时机，避免追高风险
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import threading


class PullbackConfirmation:
    """回踩确认模块"""
    
    def __init__(self, trader, config: dict = None):
        self.trader = trader
        self.log = logging.getLogger(__name__)
        
        # 默认配置
        self.config = {
            'pullback_percentage': 0.0038,  # 0.38%回踩
            'max_wait_minutes': 30,         # 最大等待30分钟
            'min_pullback_percentage': 0.001,  # 最小回踩0.1%
            'max_pullback_percentage': 0.01,   # 最大回踩1%
            'price_check_interval': 10,     # 价格检查间隔(秒)
        }
        
        if config:
            self.config.update(config)
            
        # 等待回踩的订单管理
        self.waiting_pullbacks = {}  # {symbol: pullback_info}
        self.pullback_orders = {}    # {symbol: order_info}
        
        # 启动监控线程
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_pullbacks, daemon=True)
        self.monitor_thread.start()
        
    def wait_for_pullback(self, symbol: str, breakthrough_price: float, 
                         quantity: float, side: str = "BUY") -> bool:
        """
        等待回踩确认并下限价单
        
        Args:
            symbol: 交易对
            breakthrough_price: 突破价格
            quantity: 交易数量
            side: 交易方向
            
        Returns:
            bool: 是否成功启动回踩等待
        """
        try:
            # 计算回踩目标价格
            pullback_target = breakthrough_price * (1 - self.config['pullback_percentage'])
            
            # 记录回踩等待信息
            pullback_info = {
                'symbol': symbol,
                'breakthrough_price': breakthrough_price,
                'pullback_target': pullback_target,
                'quantity': quantity,
                'side': side,
                'start_time': datetime.now(),
                'max_wait_time': datetime.now() + timedelta(minutes=self.config['max_wait_minutes']),
                'status': 'waiting',
                'highest_price': breakthrough_price,  # 跟踪最高价
                'current_pullback_pct': 0.0
            }
            
            self.waiting_pullbacks[symbol] = pullback_info
            
            self.log.info(f"{symbol} 开始等待回踩确认 - 突破价: {breakthrough_price:.6f}, "
                         f"回踩目标: {pullback_target:.6f} ({self.config['pullback_percentage']*100:.2f}%)")
            
            return True
            
        except Exception as e:
            self.log.error(f"{symbol} 启动回踩等待失败: {e}")
            return False
    
    def _monitor_pullbacks(self):
        """监控回踩情况"""
        while self.monitoring:
            try:
                current_time = datetime.now()
                symbols_to_remove = []
                
                for symbol, pullback_info in self.waiting_pullbacks.items():
                    # 检查是否超时
                    if current_time > pullback_info['max_wait_time']:
                        self.log.warning(f"{symbol} 回踩等待超时，执行市价单开仓")
                        self._execute_market_order(pullback_info)
                        symbols_to_remove.append(symbol)
                        continue
                    
                    # 获取当前价格
                    current_price = self._get_current_price(symbol)
                    if current_price is None:
                        continue
                    
                    # 更新最高价
                    if current_price > pullback_info['highest_price']:
                        pullback_info['highest_price'] = current_price
                    
                    # 计算当前回踩幅度
                    current_pullback_pct = (pullback_info['highest_price'] - current_price) / pullback_info['highest_price']
                    pullback_info['current_pullback_pct'] = current_pullback_pct
                    
                    # 检查回踩条件
                    if self._check_pullback_condition(pullback_info, current_price):
                        self.log.info(f"{symbol} 满足回踩条件，下限价单")
                        if self._place_limit_order(pullback_info, current_price):
                            symbols_to_remove.append(symbol)
                        continue
                    
                    # 检查是否回踩过度
                    if current_pullback_pct > self.config['max_pullback_percentage']:
                        self.log.warning(f"{symbol} 回踩过度 {current_pullback_pct*100:.2f}%，取消等待")
                        symbols_to_remove.append(symbol)
                        continue
                
                # 清理已完成的等待
                for symbol in symbols_to_remove:
                    if symbol in self.waiting_pullbacks:
                        del self.waiting_pullbacks[symbol]
                
                time.sleep(self.config['price_check_interval'])
                
            except Exception as e:
                self.log.error(f"回踩监控异常: {e}")
                time.sleep(5)
    
    def _check_pullback_condition(self, pullback_info: dict, current_price: float) -> bool:
        """检查是否满足回踩条件"""
        try:
            # 基本回踩幅度检查
            pullback_pct = pullback_info['current_pullback_pct']
            
            # 必须达到最小回踩幅度
            if pullback_pct < self.config['min_pullback_percentage']:
                return False
            
            # 检查是否达到目标回踩价格
            if current_price <= pullback_info['pullback_target']:
                return True
            
            # 检查是否有足够的回踩幅度（动态调整）
            if pullback_pct >= self.config['pullback_percentage']:
                return True
            
            return False
            
        except Exception as e:
            self.log.error(f"回踩条件检查失败: {e}")
            return False
    
    def _place_limit_order(self, pullback_info: dict, current_price: float) -> bool:
        """下限价单"""
        try:
            symbol = pullback_info['symbol']
            quantity = pullback_info['quantity']
            side = pullback_info['side']
            
            # 计算限价单价格（略高于当前价格，确保成交）
            limit_price = current_price * 1.001  # 高出0.1%
            
            # 下限价单
            result = self.trader.open_position(
                symbol=symbol,
                side=side,
                size=quantity,
                price=limit_price,
                timeInForce='GTC'
            )
            
            if result:
                # 记录订单信息
                order_info = {
                    'symbol': symbol,
                    'order_id': result.get('orderId') if isinstance(result, dict) else None,
                    'price': limit_price,
                    'quantity': quantity,
                    'side': side,
                    'create_time': datetime.now(),
                    'ttl_expire_time': datetime.now() + timedelta(minutes=self.config['max_wait_minutes'])
                }
                
                self.pullback_orders[symbol] = order_info
                
                self.log.info(f"{symbol} 回踩限价单已下达 - 价格: {limit_price:.6f}, "
                             f"数量: {quantity:.6f}, 回踩幅度: {pullback_info['current_pullback_pct']*100:.2f}%")
                
                return True
            else:
                self.log.error(f"{symbol} 回踩限价单下达失败")
                return False
                
        except Exception as e:
            self.log.error(f"{symbol} 下限价单失败: {e}")
            return False
    
    def _execute_market_order(self, pullback_info: dict):
        """执行市价单（回踩等待超时时）"""
        try:
            symbol = pullback_info['symbol']
            quantity = pullback_info['quantity']
            side = pullback_info['side']
            
            # 执行市价单
            result = self.trader.open_position(
                symbol=symbol,
                side=side,
                size=quantity,
                price=None,  # 市价单
                timeInForce=None
            )
            
            if result:
                self.log.info(f"{symbol} 回踩超时，市价单开仓成功")
            else:
                self.log.error(f"{symbol} 回踩超时，市价单开仓失败")
                
        except Exception as e:
            self.log.error(f"{symbol} 市价单开仓失败: {e}")
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            if hasattr(self.trader, 'get_symbol_ticker'):
                ticker = self.trader.get_symbol_ticker(symbol)
                if ticker and 'price' in ticker:
                    return float(ticker['price'])
            
            # 备用方法：通过K线获取
            if hasattr(self.trader, 'get_klines'):
                klines = self.trader.get_klines(symbol, '1m', 1)
                if klines and len(klines) > 0:
                    return float(klines[0]['close'])
            
            return None
            
        except Exception as e:
            self.log.error(f"获取{symbol}当前价格失败: {e}")
            return None
    
    def manage_limit_orders(self):
        """管理限价单TTL"""
        try:
            current_time = datetime.now()
            orders_to_remove = []
            
            for symbol, order_info in self.pullback_orders.items():
                # 检查TTL是否过期
                if current_time > order_info['ttl_expire_time']:
                    self.log.info(f"{symbol} 限价单TTL过期，尝试撤单")
                    
                    # 撤销订单
                    if order_info.get('order_id'):
                        try:
                            self.trader.cancel_order(symbol, order_info['order_id'])
                            self.log.info(f"{symbol} 限价单已撤销")
                        except Exception as e:
                            self.log.warning(f"{symbol} 撤单失败: {e}")
                    
                    orders_to_remove.append(symbol)
            
            # 清理过期订单
            for symbol in orders_to_remove:
                if symbol in self.pullback_orders:
                    del self.pullback_orders[symbol]
                    
        except Exception as e:
            self.log.error(f"限价单管理失败: {e}")
    
    def get_pullback_status(self, symbol: str) -> Optional[dict]:
        """获取回踩状态"""
        if symbol in self.waiting_pullbacks:
            return self.waiting_pullbacks[symbol].copy()
        elif symbol in self.pullback_orders:
            return self.pullback_orders[symbol].copy()
        return None
    
    def cancel_pullback_wait(self, symbol: str) -> bool:
        """取消回踩等待"""
        try:
            if symbol in self.waiting_pullbacks:
                del self.waiting_pullbacks[symbol]
                self.log.info(f"{symbol} 回踩等待已取消")
                return True
            return False
        except Exception as e:
            self.log.error(f"取消{symbol}回踩等待失败: {e}")
            return False
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)