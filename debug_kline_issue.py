#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试K线数据获取问题的脚本
模拟策略中的完整K线获取流程
"""

import sys
import os
import json
import pandas as pd
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from binance_trader import BinanceTrader

def debug_kline_issue():
    """调试K线数据获取问题"""
    
    # 加载配置
    try:
        with open('config/api_config.json', 'r', encoding='utf-8') as f:
            api_config = json.load(f)
        
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        print("✓ 配置文件加载成功")
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return
    
    # 初始化交易器
    try:
        trader = BinanceTrader(config)
        print("✓ BinanceTrader初始化成功")
    except Exception as e:
        print(f"✗ BinanceTrader初始化失败: {e}")
        return
    
    # 测试币种列表
    test_symbols = ['BCHUSDT', 'FLOWUSDT', 'ALPINEUSDT']
    
    for symbol in test_symbols:
        print(f"\n=== 测试 {symbol} ===")
        
        # 检查币种是否在有效列表中
        if hasattr(trader, 'symbols') and symbol not in trader.symbols:
            print(f"⚠️  {symbol} 不在有效交易对列表中")
            continue
        
        # 测试不同的limit值
        for limit in [20, 200]:
            print(f"\n--- 测试 limit={limit} ---")
            
            try:
                # 调用BinanceTrader的get_klines方法
                klines = trader.get_klines(symbol, interval='15m', limit=limit)
                
                if not klines:
                    print(f"✗ 获取失败: 返回空列表")
                    continue
                
                print(f"✓ 原始数据获取成功，条数: {len(klines)}")
                
                # 模拟策略中的DataFrame转换
                try:
                    df = pd.DataFrame(klines, columns=['t', 'o', 'h', 'l', 'c', 'v', 'ct', 'qv', 'n', 'tbbav', 'tbqav', 'ignore'])
                    df = df[['t', 'o', 'h', 'l', 'c', 'v']].astype(float)
                    
                    # 重命名列
                    df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                    
                    # 设置时间索引
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    print(f"✓ DataFrame转换成功，最终条数: {len(df)}")
                    print(f"  时间范围: {df.index[0]} 到 {df.index[-1]}")
                    
                except Exception as df_e:
                    print(f"✗ DataFrame转换失败: {df_e}")
                    print(f"  原始数据样本: {klines[:2] if len(klines) >= 2 else klines}")
                    
            except Exception as e:
                print(f"✗ 获取K线数据异常: {e}")

if __name__ == "__main__":
    debug_kline_issue()