#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版评分计算器
优化交易对评分算法，提升选择准确性
"""

import pandas as pd
import numpy as np
import logging
import time
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ScoreComponent(Enum):
    """评分组件枚举"""
    DEPTH = "depth"          # 深度评分
    VOLUME = "volume"        # 成交量评分
    AGE = "age"             # 币龄评分
    MOMENTUM = "momentum"    # 动量评分
    CHANNEL = "channel"      # 通道评分
    VOLATILITY = "volatility" # 波动率评分
    LIQUIDITY = "liquidity"  # 流动性评分

@dataclass
class ScoreWeights:
    """评分权重配置"""
    depth: float = 2.0
    volume: float = 1.0
    age: float = 2.0
    momentum: float = 3.0
    channel: float = 2.0
    volatility: float = 1.5
    liquidity: float = 1.0

@dataclass
class ScoreResult:
    """评分结果"""
    symbol: str
    total_score: float
    component_scores: Dict[str, float]
    component_details: Dict[str, Any]
    calculation_time: float
    data_quality: float

class EnhancedScoreCalculator:
    """增强版评分计算器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化评分计算器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger('enhanced_score_calculator')
        self.logger.setLevel(logging.INFO)  # 确保INFO级别日志能输出
        
        # 评分权重 - 根据配置文件更新权重分配
        weights_config = self.config.get('weights', {})
        self.weights = ScoreWeights(
            depth=weights_config.get('depth', 1.5),        # 降低深度权重
            volume=weights_config.get('volume', 1.0),      # 保持成交量权重
            age=weights_config.get('age', 1.5),            # 降低币龄权重
            momentum=weights_config.get('momentum', 4.0),   # 提高动量权重至4.0
            channel=weights_config.get('channel', 3.5),     # 提高通道权重至3.5
            volatility=weights_config.get('volatility', 1.0), # 降低波动率权重
            liquidity=weights_config.get('liquidity', 1.0)  # 保持流动性权重
        )
        
        # 评分阈值
        self.thresholds = self.config.get('thresholds', {
            'depth': {
                'excellent': 2e6,    # 200万美元
                'good': 2e5,         # 20万美元
                'minimum': 5e4       # 5万美元
            },
            'volume': {
                'growth_excellent': 0.5,  # 50%增长
                'growth_good': 0.2,       # 20%增长
                'growth_minimum': 0.0     # 0%增长
            },
            'age': {
                'new_coin_days': 7,       # 新币期限
                'mature_days': 30,        # 成熟期限
                'old_days': 365          # 老币期限
            },
            'momentum': {
                'excellent_ratio': 1.5,   # 优秀动量比率
                'good_ratio': 0.5,        # 良好动量比率
                'minimum_change': 0.0     # 最小涨幅
            },
            'volatility': {
                'low': 1.0,              # 波动率过低阈值1%
                'optimal': 5.0,          # 最优波动率5%
                'high': 15.0             # 波动率过高阈值15%
            }
        })
        
        # 数据质量权重
        self.data_quality_weights = {
            'completeness': 0.3,    # 数据完整性
            'freshness': 0.3,       # 数据新鲜度
            'consistency': 0.2,     # 数据一致性
            'accuracy': 0.2         # 数据准确性
        }
    
    def calculate_comprehensive_score(self, symbol: str, df_data: pd.DataFrame, 
                                    depth_data: float = None, 
                                    additional_data: Dict[str, Any] = None) -> ScoreResult:
        """
        计算综合评分
        
        Args:
            symbol: 交易对符号
            df_data: K线数据
            depth_data: 深度数据
            additional_data: 额外数据
            
        Returns:
            评分结果
        """
        start_time = time.time()
        
        try:
            # 数据质量检查
            data_quality = self._assess_data_quality(df_data, depth_data)
            
            # 计算各组件评分
            component_scores = {}
            component_details = {}
            
            # 1. 深度评分
            depth_score, depth_detail = self._calculate_depth_score(depth_data)
            component_scores[ScoreComponent.DEPTH.value] = depth_score
            component_details[ScoreComponent.DEPTH.value] = depth_detail
            
            # 2. 成交量评分
            volume_score, volume_detail = self._calculate_volume_score(df_data)
            component_scores[ScoreComponent.VOLUME.value] = volume_score
            component_details[ScoreComponent.VOLUME.value] = volume_detail
            
            # 3. 币龄评分
            age_score, age_detail = self._calculate_age_score(df_data)
            component_scores[ScoreComponent.AGE.value] = age_score
            component_details[ScoreComponent.AGE.value] = age_detail
            
            # 4. 动量评分
            momentum_score, momentum_detail = self._calculate_momentum_score(df_data)
            component_scores[ScoreComponent.MOMENTUM.value] = momentum_score
            component_details[ScoreComponent.MOMENTUM.value] = momentum_detail
            
            # 5. 通道评分
            channel_score, channel_detail = self._calculate_channel_score(df_data)
            component_scores[ScoreComponent.CHANNEL.value] = channel_score
            component_details[ScoreComponent.CHANNEL.value] = channel_detail
            
            # 6. 波动率评分
            volatility_score, volatility_detail = self._calculate_volatility_score(df_data)
            component_scores[ScoreComponent.VOLATILITY.value] = volatility_score
            component_details[ScoreComponent.VOLATILITY.value] = volatility_detail
            
            # 7. 流动性评分
            liquidity_score, liquidity_detail = self._calculate_liquidity_score(df_data, depth_data)
            component_scores[ScoreComponent.LIQUIDITY.value] = liquidity_score
            component_details[ScoreComponent.LIQUIDITY.value] = liquidity_detail
            
            # 计算总分
            total_score = (
                depth_score * self.weights.depth +
                volume_score * self.weights.volume +
                age_score * self.weights.age +
                momentum_score * self.weights.momentum +
                channel_score * self.weights.channel +
                volatility_score * self.weights.volatility +
                liquidity_score * self.weights.liquidity
            )
            
            # 数据质量调整
            total_score *= data_quality
            
            # 基础保护分数
            if total_score == 0 and depth_data and depth_data >= self.thresholds['depth']['minimum']:
                total_score = 1.0
                component_details['protection'] = "基础保护分数"
            
            # 调试日志：输出各组件评分
            self.logger.info(f"{symbol} 评分详情: 深度={depth_score:.2f}, 成交量={volume_score:.2f}, "
                           f"币龄={age_score:.2f}, 动量={momentum_score:.2f}, 通道={channel_score:.2f}, "
                           f"波动率={volatility_score:.2f}, 流动性={liquidity_score:.2f}, "
                           f"数据质量={data_quality:.2f}, 总分={total_score:.2f}")
            
            calculation_time = time.time() - start_time
            
            result = ScoreResult(
                symbol=symbol,
                total_score=total_score,
                component_scores=component_scores,
                component_details=component_details,
                calculation_time=calculation_time,
                data_quality=data_quality
            )
            
            self.logger.debug(f"{symbol} 评分完成: 总分={total_score:.2f}, 耗时={calculation_time:.3f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"计算 {symbol} 评分失败: {e}")
            return ScoreResult(
                symbol=symbol,
                total_score=0.0,
                component_scores={},
                component_details={'error': str(e)},
                calculation_time=time.time() - start_time,
                data_quality=0.0
            )
    
    def _assess_data_quality(self, df_data: pd.DataFrame, depth_data: float = None) -> float:
        """评估数据质量"""
        try:
            quality_scores = {}
            
            # 1. 数据完整性
            if df_data is not None and not df_data.empty:
                # 智能识别列名格式
                open_col = 'open' if 'open' in df_data.columns else 'o'
                high_col = 'high' if 'high' in df_data.columns else 'h'
                low_col = 'low' if 'low' in df_data.columns else 'l'
                close_col = 'close' if 'close' in df_data.columns else 'c'
                volume_col = 'volume' if 'volume' in df_data.columns else 'v'
                
                required_columns = [open_col, high_col, low_col, close_col, volume_col]
                missing_columns = [col for col in required_columns if col not in df_data.columns]
                completeness = 1.0 - (len(missing_columns) / len(required_columns))
                
                # 检查空值
                available_columns = [col for col in required_columns if col in df_data.columns]
                if available_columns:
                    null_ratio = df_data[available_columns].isnull().sum().sum() / (len(df_data) * len(available_columns))
                    completeness *= (1.0 - null_ratio)
            else:
                completeness = 0.0
            
            quality_scores['completeness'] = completeness
            
            # 2. 数据新鲜度
            if df_data is not None and not df_data.empty:
                try:
                    last_time = df_data.index[-1]
                    current_time = pd.Timestamp.utcnow()
                    if hasattr(last_time, 'tz') and last_time.tz is None:
                        last_time = last_time.tz_localize('UTC')
                    if current_time.tz is None:
                        current_time = current_time.tz_localize('UTC')
                    
                    time_diff = (current_time - last_time).total_seconds() / 3600  # 小时
                    freshness = max(0.0, 1.0 - time_diff / 24)  # 24小时内为满分
                except:
                    freshness = 0.5  # 默认值
            else:
                freshness = 0.0
            
            quality_scores['freshness'] = freshness
            
            # 3. 数据一致性
            if df_data is not None and len(df_data) > 1:
                # 检查价格一致性（高>=低，收盘价在高低之间）
                high_col = 'high' if 'high' in df_data.columns else 'h'
                low_col = 'low' if 'low' in df_data.columns else 'l'
                close_col = 'close' if 'close' in df_data.columns else 'c'
                
                price_consistency = (
                    (df_data[high_col] >= df_data[low_col]).all() and
                    (df_data[close_col] >= df_data[low_col]).all() and
                    (df_data[close_col] <= df_data[high_col]).all()
                )
                consistency = 1.0 if price_consistency else 0.5
            else:
                consistency = 0.0
            
            quality_scores['consistency'] = consistency
            
            # 4. 数据准确性（基于深度数据可用性）
            accuracy = 1.0 if depth_data is not None and depth_data > 0 else 0.5
            quality_scores['accuracy'] = accuracy
            
            # 计算综合质量分数
            total_quality = sum(
                score * self.data_quality_weights[component]
                for component, score in quality_scores.items()
            )
            
            return max(0.1, min(1.0, total_quality))  # 限制在0.1-1.0之间
            
        except Exception as e:
            self.logger.warning(f"数据质量评估失败: {e}")
            return 0.5  # 默认质量分数
    
    def _calculate_depth_score(self, depth_data: float) -> Tuple[float, Dict[str, Any]]:
        """计算深度评分"""
        if depth_data is None or depth_data <= 0:
            return 0.0, {'depth': 0, 'reason': '深度数据不可用'}
        
        thresholds = self.thresholds['depth']
        
        if depth_data >= thresholds['excellent']:
            score = 2.0
            level = 'excellent'
        elif depth_data >= thresholds['good']:
            score = 1.0
            level = 'good'
        elif depth_data >= thresholds['minimum']:
            score = 0.5
            level = 'minimum'
        else:
            score = 0.0
            level = 'insufficient'
        
        detail = {
            'depth': depth_data,
            'level': level,
            'thresholds': thresholds
        }
        
        return score, detail
    
    def _calculate_volume_score(self, df_data: pd.DataFrame) -> Tuple[float, Dict[str, Any]]:
        """计算成交量评分"""
        if df_data is None or len(df_data) < 50:
            return 0.0, {'reason': '数据不足', 'length': len(df_data) if df_data is not None else 0}
        
        try:
            # 基本数据完整性检查
            if df_data is None or len(df_data) < 10:
                return 0.0, {'reason': '数据不足', 'length': len(df_data) if df_data is not None else 0}
            
            # 检查必要列是否存在
            required_cols = ['open', 'high', 'low', 'close', 'volume'] if 'volume' in df_data.columns else ['o', 'h', 'l', 'c', 'v']
            if not all(col in df_data.columns for col in required_cols):
                return 0.0, {'reason': f'缺少必要列: {required_cols}', 'available_cols': list(df_data.columns)}
            
            # 检查数据逻辑性
            high_col = 'high' if 'high' in df_data.columns else 'h'
            low_col = 'low' if 'low' in df_data.columns else 'l'
            close_col = 'close' if 'close' in df_data.columns else 'c'
            
            data_valid = (
                (df_data[high_col] >= df_data[low_col]).all() and
                (df_data[close_col] >= df_data[low_col]).all() and
                (df_data[close_col] <= df_data[high_col]).all()
            )
            
            if not data_valid:
                return 0.0, {'reason': '数据验证失败', 'high_col': high_col, 'low_col': low_col, 'close_col': close_col}
            
            # 计算成交量变化，使用正确的列名
            volume_col = 'volume' if 'volume' in df_data.columns else 'v'
            recent_volume = df_data[volume_col].iloc[-25:].mean()
            previous_volume = df_data[volume_col].iloc[-50:-25].mean()
            
            if previous_volume <= 0:
                return 0.0, {'reason': '历史成交量为零'}
            
            volume_change = (recent_volume / previous_volume) - 1
            
            thresholds = self.thresholds['volume']
            
            if volume_change >= thresholds['growth_excellent']:
                score = 2.0
                level = 'excellent'
            elif volume_change >= thresholds['growth_good']:
                score = 1.0
                level = 'good'
            elif volume_change >= thresholds['growth_minimum']:
                score = 0.5
                level = 'minimum'
            else:
                score = 0.0
                level = 'declining'
            
            detail = {
                'volume_change': volume_change,
                'recent_volume': recent_volume,
                'previous_volume': previous_volume,
                'level': level
            }
            
            return score, detail
            
        except Exception as e:
            return 0.0, {'error': str(e)}
    
    def _calculate_age_score(self, df_data: pd.DataFrame) -> Tuple[float, Dict[str, Any]]:
        """计算币龄评分"""
        if df_data is None or df_data.empty:
            return 0.0, {'reason': '数据不可用'}
        
        try:
            current_time = pd.Timestamp.utcnow()
            if current_time.tz is None:
                current_time = current_time.tz_localize('UTC')
            
            first_time = df_data.index[0]
            if hasattr(first_time, 'tz') and first_time.tz is None:
                first_time = first_time.tz_localize('UTC')
            elif not hasattr(first_time, 'tz'):
                first_time = pd.Timestamp(first_time).tz_localize('UTC')
            
            age_days = (current_time - first_time).total_seconds() / (24 * 3600)
            
            thresholds = self.thresholds['age']
            
            if age_days <= thresholds['new_coin_days']:
                score = 2.0  # 新币黄金期
                level = 'new_coin'
            elif age_days <= thresholds['mature_days']:
                score = 1.5  # 成长期
                level = 'growing'
            elif age_days <= thresholds['old_days']:
                score = 1.0  # 成熟期
                level = 'mature'
            else:
                score = 0.5  # 老币
                level = 'old'
            
            detail = {
                'age_days': age_days,
                'level': level,
                'first_time': first_time.isoformat(),
                'current_time': current_time.isoformat()
            }
            
            return score, detail
            
        except Exception as e:
            return 0.0, {'error': str(e)}
    
    def _calculate_momentum_score(self, df_data: pd.DataFrame) -> Tuple[float, Dict[str, Any]]:
        """计算动量评分"""
        if df_data is None or len(df_data) < 50:
            return 0.0, {'reason': '数据不足', 'length': len(df_data) if df_data is not None else 0}
        
        try:
            # 使用正确的列名
            close_col = 'close' if 'close' in df_data.columns else 'c'
            high_col = 'high' if 'high' in df_data.columns else 'h'
            low_col = 'low' if 'low' in df_data.columns else 'l'
            
            # 计算不同周期的动量
            periods = [10, 25]
            momentum_scores = []
            
            for period in periods:
                if len(df_data) >= period:
                    price_change = (df_data[close_col].iloc[-1] / df_data[close_col].iloc[-period] - 1) * 100
                    high_price = df_data[high_col].iloc[-period:].max()
                    low_price = df_data[low_col].iloc[-period:].min()
                    retracement = (high_price - low_price) / df_data[close_col].iloc[-period] * 100
                    
                    # 根据价格变化和回撤计算动量分数
                    if price_change > 5 and retracement < 20:
                        momentum_scores.append(2.0)
                    elif price_change > 2 and retracement < 30:
                        momentum_scores.append(1.5)
                    elif price_change > 0:
                        momentum_scores.append(1.0)
                    else:
                        momentum_scores.append(0.5)
            
            # 短期动量（25天）
            if len(df_data) >= 25:
                price_change = (df_data[close_col].iloc[-1] / df_data[close_col].iloc[-25] - 1) * 100
                high_price = df_data[high_col].iloc[-25:].max()
                low_price = df_data[low_col].iloc[-25:].min()
                retracement = (high_price - low_price) / df_data[close_col].iloc[-25] * 100
                
                calculation_type = 'standard'
            else:
                # 新币短期动量
                periods = len(df_data) - 1
                price_change = (df_data[close_col].iloc[-1] / df_data[close_col].iloc[-periods] - 1) * 100
                high_price = df_data[high_col].iloc[-periods:].max()
                low_price = df_data[low_col].iloc[-periods:].min()
                retracement = (high_price - low_price) / df_data[close_col].iloc[-periods] * 100
                
                calculation_type = 'short_term'
            
            if retracement < 0.1:  # 避免除零
                score = 1.0 if price_change > 0 else 0.0
                momentum_ratio = float('inf') if price_change > 0 else 0
                level = 'low_volatility'
            else:
                momentum_ratio = price_change / retracement
                
                thresholds = self.thresholds['momentum']
                
                if price_change > 0:
                    if momentum_ratio >= thresholds['excellent_ratio']:
                        score = 2.0
                        level = 'excellent'
                    elif momentum_ratio >= thresholds['good_ratio']:
                        score = 1.0
                        level = 'good'
                    elif price_change >= thresholds['minimum_change']:
                        score = 0.5
                        level = 'minimum'
                    else:
                        score = 0.0
                        level = 'insufficient'
                else:
                    score = 0.0
                    level = 'negative'
            
            detail = {
                'price_change': price_change,
                'retracement': retracement,
                'momentum_ratio': momentum_ratio,
                'level': level,
                'calculation_type': calculation_type,
                'periods': periods if calculation_type == 'short_term' else 25
            }
            
            # 添加详细的调试日志
            self.logger.info(f"动量评分详情 - 价格变化: {price_change:.2f}%, 回撤: {retracement:.2f}%, "
                           f"动量比率: {momentum_ratio:.2f}, 等级: {level}, 评分: {score:.2f}")
            
            return score, detail
            
        except Exception as e:
            return 0.0, {'error': str(e)}
    
    def _calculate_channel_score(self, df_data: pd.DataFrame) -> Tuple[float, Dict[str, Any]]:
        """计算通道评分 - 基于通道位置"""
        try:
            if df_data is None or len(df_data) < 50:
                return 0.0, {'reason': '数据不足', 'length': len(df_data) if df_data is not None else 0}
            
            # 使用正确的列名
            high_col = 'high' if 'high' in df_data.columns else 'h'
            low_col = 'low' if 'low' in df_data.columns else 'l'
            close_col = 'close' if 'close' in df_data.columns else 'c'
            
            # 计算通道（使用20周期）
            n = 20
            upper_band = df_data[high_col].rolling(n).max().iloc[-1]
            lower_band = df_data[low_col].rolling(n).min().iloc[-1]
            middle_band = (upper_band + lower_band) / 2
            
            current_price = df_data[close_col].iloc[-1]
            
            # 判断位置并评分
            if current_price >= upper_band * 0.95:  # 接近或突破上轨
                if current_price >= upper_band:
                    position = "突破上轨"
                    score = 3.0  # 最高分
                else:
                    position = "接近上轨"
                    score = 2.5  # 高分
            elif current_price <= lower_band * 1.05:  # 接近或跌破下轨
                if current_price <= lower_band:
                    position = "跌破下轨"
                    score = 0.0  # 最低分
                else:
                    position = "接近下轨"
                    score = 0.5  # 低分
            elif current_price >= middle_band:
                position = "中轨上方"
                score = 2.0  # 中等偏上
            else:
                position = "中轨下方"
                score = 1.0  # 中等偏下
            
            # 计算位置百分比
            channel_range = upper_band - lower_band
            if channel_range > 0:
                position_pct = (current_price - lower_band) / channel_range * 100
            else:
                position_pct = 50.0
            
            detail = {
                'position': position,
                'position_pct': position_pct,
                'current_price': current_price,
                'upper_band': upper_band,
                'middle_band': middle_band,
                'lower_band': lower_band,
                'channel_range': channel_range
            }
            
            return score, detail
            
        except Exception as e:
            return 0.0, {'error': str(e)}
    
    def _calculate_volatility_score(self, df_data: pd.DataFrame) -> Tuple[float, Dict[str, Any]]:
        """计算波动率评分"""
        self.logger.info(f"开始计算波动率评分，数据行数: {len(df_data) if df_data is not None else 0}")
        
        if df_data is None or len(df_data) < 20:
            self.logger.info(f"波动率评分 - 数据不足，返回0分")
            return 0.0, {'reason': '数据不足', 'length': len(df_data) if df_data is not None else 0}
        
        try:
            # 使用正确的列名
            close_col = 'close' if 'close' in df_data.columns else 'c'
            self.logger.info(f"波动率评分 - 使用列名: {close_col}")
            
            # 计算收益率
            returns = df_data[close_col].pct_change().dropna()
            self.logger.info(f"波动率评分 - 收益率数据行数: {len(returns)}")
            
            if len(returns) < 10:
                self.logger.info(f"波动率评分 - 收益率数据不足，返回0分")
                return 0.0, {'reason': '收益率数据不足'}
            
            # 计算波动率（标准差）
            volatility = returns.std() * 100  # 转换为百分比
            
            thresholds = self.thresholds['volatility']
            
            # 添加调试日志
            self.logger.info(f"波动率评分详情 - 波动率: {volatility:.2f}%, 阈值: {thresholds}")
            
            if volatility <= thresholds['low']:
                score = 0.5  # 波动率过低，流动性可能不足
                level = 'too_low'
            elif volatility <= thresholds['optimal']:
                score = 2.0  # 最优波动率
                level = 'optimal'
            elif volatility <= thresholds['high']:
                score = 1.0  # 适中波动率
                level = 'moderate'
            else:
                score = 0.0  # 波动率过高，风险太大
                level = 'too_high'
            
            detail = {
                'volatility': volatility,
                'level': level,
                'returns_count': len(returns),
                'thresholds': thresholds
            }
            
            self.logger.info(f"波动率评分详情 - 波动率: {volatility:.2f}%, 等级: {level}, 评分: {score:.2f}")
            
            return score, detail
            
        except Exception as e:
            self.logger.error(f"波动率评分计算异常: {str(e)}")
            return 0.0, {'error': str(e)}
    
    def _calculate_liquidity_score(self, df_data: pd.DataFrame, depth_data: float = None) -> Tuple[float, Dict[str, Any]]:
        """计算流动性评分"""
        if df_data is None or df_data.empty:
            return 0.0, {'reason': '数据不可用'}
        
        try:
            # 使用正确的列名
            volume_col = 'volume' if 'volume' in df_data.columns else 'v'
            
            # 基于成交量和深度的综合流动性评分
            volume_score = 0.0
            depth_score = 0.0
            
            # 成交量流动性
            if len(df_data) >= 10:
                recent_volume = df_data[volume_col].iloc[-10:].mean()
                if recent_volume > 0:
                    volume_score = min(2.0, recent_volume / 1e6)  # 100万为满分
            
            # 深度流动性
            if depth_data is not None and depth_data > 0:
                depth_score = min(2.0, depth_data / 1e6)  # 100万为满分
            
            # 综合流动性评分
            liquidity_score = (volume_score + depth_score) / 2
            
            detail = {
                'volume_score': volume_score,
                'depth_score': depth_score,
                'recent_volume': df_data[volume_col].iloc[-10:].mean() if len(df_data) >= 10 else 0,
                'depth_data': depth_data
            }
            
            return liquidity_score, detail
            
        except Exception as e:
            return 0.0, {'error': str(e)}
    
    def get_score_explanation(self, result: ScoreResult) -> str:
        """获取评分详细说明"""
        explanation = f"=== {result.symbol} 评分详情 ===\n"
        explanation += f"总分: {result.total_score:.2f} (数据质量: {result.data_quality:.2f})\n"
        explanation += f"计算耗时: {result.calculation_time:.3f}秒\n\n"
        
        for component, score in result.component_scores.items():
            weight = getattr(self.weights, component, 1.0)
            weighted_score = score * weight
            detail = result.component_details.get(component, {})
            
            explanation += f"{component.upper()}: {score:.1f} × {weight} = {weighted_score:.1f}\n"
            
            if isinstance(detail, dict):
                for key, value in detail.items():
                    if key != 'error':
                        explanation += f"  {key}: {value}\n"
            
            explanation += "\n"
        
        return explanation