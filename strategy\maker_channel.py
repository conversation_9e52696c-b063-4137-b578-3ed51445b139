import pandas as pd
import numpy as np
import datetime
import logging
import time
import json
import os
import sys
import asyncio  # 添加asyncio导入
import gc  # 添加垃圾回收模块
import psutil  # 添加系统资源监控模块
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

# ↓↓↓ 复用你的原模块 ↓↓↓
from binance_trader import BinanceTrader
from binance_trader import ApiRateLimiter, OrderOperationQueue
# ↓↓↓ 缓存管理器 ↓↓↓
from cache_manager import CacheManager
# ↓↓↓ 监控系统 ↓↓↓
from 核心指标监控系统 import MetricsCollector
# ↓↓↓ 策略优化器 ↓↓↓
from strategy_optimizer import StrategyOptimizer, OptimizationConfig
# ↓↓↓ 动态时间框架辅助模块 ↓↓↓
from strategy.dynamic_tf_helper import dynamic_tf_for_channel
# ↓↓↓ 增强评分计算器 ↓↓↓
from enhanced_score_calculator import EnhancedScoreCalculator
# ↓↓↓ 持仓监控器 ↓↓↓
from position_monitor import PositionMonitor, MonitoringConfig
# ↓↓↓ 批次监控和恢复系统 ↓↓↓
from .batch_monitor import BatchMonitor
from .batch_recovery import BatchRecoveryManager, BatchMonitorWithRecovery
from .batch_config import BatchConfig
from .pullback_confirmation import PullbackConfirmation
from .volume_validator import VolumeValidator
from .chip_pressure_scanner import ChipPressureScanner
from new_coin_handler import NewCoinHandler

class MakerChannelStrategy:
    """依赖 BinanceTrader 完成所有下单/查询"""
    def __init__(self, trader: BinanceTrader, config: dict):
        self.trader = trader
        self.cfg = config
        self.log = logging.getLogger('MakerChannel')
        self.symbol_selector_log = logging.getLogger('ReferenceFolder.symbol_selector')
        self.symbol_scorer_log = logging.getLogger('ReferenceFolder.symbol_scorer')
        self.symbol = None
        self.pos = None
        self.add_count = 0
        self.original_nominal = config['first_nominal']
        self.cache = CacheManager()
        self.cand_cache = {}  # 内存缓存候选池
        self.last_full_scan = 0  # 上次全量扫描时间
        self.last_candidate_scan = 0  # 上次候选池扫描时间
        self.margin_alert_symbols = set()  # 保证金警报币种集合
        self.active_orders = {}  # 活跃订单跟踪 {order_id: {symbol, type, create_time, ttl}}
        self.order_ttl = config.get('order_ttl', 300)  # 限价单TTL，默认5分钟
        self.network_check_interval = config.get('network_check_interval', 60)  # 网络检测间隔，默认60秒
        self.last_network_check = 0  # 上次网络检测时间
        self.network_status = {'connected': True, 'latency': 0, 'last_error': None}  # 网络状态
        
        # 失败币种管理 - 避免无限重试
        self.failed_symbols = {}  # {symbol: {'count': int, 'last_fail_time': float, 'error_type': str}}
        self.max_symbol_failures = config.get('max_symbol_failures', 5)  # 单个币种最大失败次数
        self.failure_cooldown = config.get('failure_cooldown', 3600)  # 失败冷却时间（秒），默认1小时
        self.network_error_cooldown = config.get('network_error_cooldown', 300)  # 网络错误冷却时间（秒），默认5分钟
        
        # 加载通道突破配置
        self.channel_config = self._load_channel_config()
        
        # 初始化回踩确认模块
        from .pullback_confirmation import PullbackConfirmation
        pullback_config = self.channel_config.get('pullback_confirmation', {})
        self.pullback_confirmation = PullbackConfirmation(trader, pullback_config)
        
        # 初始化双重成交量验证模块
        volume_config = self.channel_config.get('volume_validation', {})
        self.volume_validator = VolumeValidator(trader, volume_config)
        
        # 初始化筹码抛压扫描器
        chip_config = self.channel_config.get('chip_analysis', {})
        self.chip_scanner = ChipPressureScanner(trader, chip_config)
        
        # 异步首次打分相关
        self.first_scoring_completed = False
        self.scoring_batch_index = 0
        self.last_scoring_time = 0
        # 异步评分配置：优化覆盖范围和频率
        self.scoring_batch_size = 20  # 每批处理20个币种（从10增加到20）
        self.scoring_interval = 20  # 每20秒处理一批（从30秒减少到20秒）
        self.max_scoring_symbols = 200  # 最大评分币种数量（新增）
        
        # 初始化监控系统
        monitoring_config = {
            'monitoring_interval': config.get('monitoring_interval', 60),
            'alert_thresholds': {
                'order_success_rate': config.get('min_order_success_rate', 0.9),
                'api_success_rate': config.get('min_api_success_rate', 0.95),
                'max_drawdown': config.get('max_drawdown_threshold', 0.1),
                'elimination_frequency': config.get('max_elimination_frequency', 5.0)
            }
        }
        self.metrics_collector = MetricsCollector(monitoring_config)
        self.metrics_collector.start_monitoring()
        
        # 初始化持仓监控器
        position_monitoring_config = MonitoringConfig(
            breakeven_trigger_pct=config.get('breakeven_trigger_pct', 0.05),  # 浮盈5%触发保本止损
            breakeven_stop_loss_pct=config.get('breakeven_stop_loss_pct', 0.004),  # 保本止损成本+0.4%
            trailing_stop_enabled=config.get('trailing_stop_enabled', True),
            trailing_stop_ratio=config.get('trailing_stop_ratio', 0.01),  # 1%移动止损
            trailing_stop_trigger_pct=config.get('trailing_stop_trigger_pct', 0.02),  # 浮盈2%启用移动止损
            max_positions=config.get('max_positions', 3),
            elimination_check_interval=config.get('elimination_check_interval', 3600),  # 1小时检查
            position_check_interval=config.get('position_check_interval', 60),  # 1分钟检查持仓
            price_update_interval=config.get('price_update_interval', 30)  # 30秒更新价格
        )
        self.position_monitor = PositionMonitor(trader, position_monitoring_config, self.log)
        
        # 内存监控和缓存管理配置
        self.memory_config = {
            'max_memory_usage': config.get('max_memory_usage', 0.85),  # 最大内存使用率85%
            'memory_check_interval': config.get('memory_check_interval', 300),  # 5分钟检查一次内存
            'cache_cleanup_threshold': config.get('cache_cleanup_threshold', 0.80),  # 内存使用率80%时清理缓存
            'gc_collection_interval': config.get('gc_collection_interval', 600),  # 10分钟执行一次垃圾回收
            'max_cache_size': config.get('max_cache_size', 1000),  # 最大缓存条目数
            'cache_ttl': config.get('cache_ttl', 3600),  # 缓存生存时间1小时
        }
        self.last_memory_check = 0
        self.last_gc_collection = 0
        self.memory_stats = {
            'peak_usage': 0,
            'cleanup_count': 0,
            'gc_count': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # 初始化策略优化器
        optimization_config = OptimizationConfig(
            max_concurrent_tasks=config.get('max_concurrent_tasks', 10),
            max_worker_threads=config.get('max_worker_threads', 5),
            enable_caching=config.get('enable_optimization_caching', True),
            cache_ttl=config.get('optimization_cache_ttl', 300),
            batch_size=config.get('optimization_batch_size', 50),
            enable_profiling=config.get('enable_profiling', True)
        )
        self.strategy_optimizer = StrategyOptimizer(optimization_config)
        self.strategy_optimizer.start()
        
        # 初始化新币种处理器
        self.new_coin_handler = NewCoinHandler(trader, self.log)
        
        # 初始化批次监控和恢复系统
        batch_config = BatchConfig(
            batch_size=config.get('batch_size', 50),
            scan_interval=config.get('scan_interval', 300),
            max_cycle_time=config.get('max_cycle_time', 3600),
            max_retry_count=config.get('batch_retry_count', 3),
            batch_timeout=config.get('batch_retry_delay', 60)
        )
        self.batch_monitor = BatchMonitor(batch_config)
        self.recovery_manager = BatchRecoveryManager()
        self.batch_monitor_with_recovery = BatchMonitorWithRecovery(self.batch_monitor, self.recovery_manager)
        
        # 全市场扫描统计信息
        self.scan_statistics = {
            'total_symbols': 0,
            'scanned_symbols': 0,
            'skipped_symbols': 0,
            'skipped_in_candidates': 0,
            'skipped_age_too_young': 0,
            'skipped_no_data': 0,
            'failed_symbols': 0,
            'breakthrough_symbols': 0,
            'low_score_symbols': 0,
            'new_breakthrough_count': 0,
            'scan_duration': 0
        }
        
        self.symbol_selector_log.info(f"币种选择器初始化完成，选择前{config.get('symbol_limit', 50)}个币种，最低评分要求大于:{self.channel_config.get('candidate_selection', {}).get('min_score', 7)}")
        self.log.info("监控系统、持仓监控器、策略优化器和批次恢复系统已启动")
        
    def _load_channel_config(self):
        """加载通道突破配置文件"""
        try:
            config_path = Path(__file__).parent.parent / 'config' / 'channel_config.json'
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.log.info(f"已加载通道配置文件: {config_path}")
                return config
            else:
                self.log.warning(f"通道配置文件不存在: {config_path}，使用默认配置")
                return self._get_default_channel_config()
        except Exception as e:
            self.log.error(f"加载通道配置文件失败: {e}，使用默认配置")
            return self._get_default_channel_config()
    
    def _get_default_channel_config(self):
        """获取默认通道配置"""
        return {
            "channel_breakthrough": {
                "close_ratio_threshold": 1.002,
                "high_ratio_threshold": 1.008,
                "max_breakthrough_pct": 0.15,
                "max_consecutive_near_upper": 8,
                "min_volume_ratio": 1.2
            },
            "dynamic_timeframe": {
                "base_period": 20,
                "age_multiplier": 0.5,
                "min_period": 10,
                "max_period": 100
            },
            "scoring_weights": {
                "price_change": 0.3,
                "volume": 0.25,
                "channel_position": 0.2,
                "breakthrough_potential": 0.15,
                "volatility": 0.1
            },
            "risk_management": {
                "max_position_size": 0.1,
                "stop_loss_pct": 0.02,
                "take_profit_pct": 0.05,
                "max_drawdown": 0.1
            }
        }
        
    # ---------- 新增：启动预热 ----------
    def warmup(self):
        """程序启动时轻量预热：仅获取基础信息，不拉取K线"""
        try:
            self.log.info("执行轻量启动预热（仅基础信息）...")

            # 首先检查并恢复实际持仓状态
            self._recover_positions_on_startup()

            # 获取所有交易对24小时行情（使用HttpClient的全局超时设置）
            info = self.trader.http.get('/fapi/v1/ticker/24hr')
            if not info or not isinstance(info, list):
                self.log.warning(f"启动预热失败：未获取到24hr行情数据 ({type(info)})")
                return

            # 获取交易所信息来过滤无效交易对
            exchange_info = self.trader.http.get('/fapi/v1/exchangeInfo')
            valid_symbols = set()
            if exchange_info and 'symbols' in exchange_info:
                for s in exchange_info['symbols']:
                    # 只选择状态为TRADING的USDT永续合约
                    if (isinstance(s, dict) and 
                        s.get('symbol', '').endswith('USDT') and
                        s.get('contractType') == 'PERPETUAL' and
                        s.get('status') == 'TRADING'):
                        valid_symbols.add(s['symbol'])

            # 过滤有效交易对
            usdt_pairs = []
            for s in info:
                symbol = s.get('symbol', '')
                if symbol in valid_symbols:
                    # 额外过滤条件：成交量大于1000 USDT，价格大于0.001
                    quote_volume = float(s.get('quoteVolume', 0.0))
                    last_price = float(s.get('lastPrice', 0.0))
                    price_change_percent = float(s.get('priceChangePercent', 0.0))
                    
                    # 过滤掉成交量过低、价格异常或涨幅异常的无效交易对
                    if (quote_volume > 1000 and 
                        last_price > 0.001 and 
                        abs(price_change_percent) < 10000):  # 过滤异常涨幅
                        usdt_pairs.append(s)
            
            self.log.info(f"交易所有效交易对数量: {len(valid_symbols)}, 过滤后有效交易对数量: {len(usdt_pairs)}")

            # 按涨幅排序，取前25
            top_gainers = sorted(
                usdt_pairs,
                key=lambda x: float(x.get('priceChangePercent', 0.0)),
                reverse=True
            )[:25]

            # 按成交额排序（quoteVolume = USDT成交额），取前25
            top_volume = sorted(
                usdt_pairs,
                key=lambda x: float(x.get('quoteVolume', 0.0)),
                reverse=True
            )[:25]

            # 合并去重，构建候选池
            candidates = {}
            for s in top_gainers + top_volume:
                symbol = s['symbol']
                candidates[symbol] = {
                    'symbol': symbol,
                    'price_change_percent': float(s.get('priceChangePercent', 0.0)),
                    'quote_volume': float(s.get('quoteVolume', 0.0)),
                    'last_price': float(s.get('lastPrice', 0.0)),
                    'score': 0.0,  # 初始评分为0，后续异步计算
                    'last_update': time.time()
                }

            self.cand_cache = candidates
            self.log.info(f"预热完成，候选池包含 {len(candidates)} 个交易对")
            
            # 记录监控指标
            self.metrics_collector.record_order({'type': 'warmup_candidates', 'count': len(candidates), 'status': 'success'})

        except Exception as e:
            self.log.error(f"启动预热失败: {e}")
            # 记录监控指标
            self.metrics_collector.record_order({'type': 'warmup_error', 'error': str(e), 'status': 'failed'})

    def _recover_positions_on_startup(self):
        """启动时恢复持仓状态"""
        try:
            positions = self.trader.http.get('/fapi/v2/positionRisk')
            if not positions:
                self.log.info("启动时无持仓")
                return

            active_positions = []
            for pos in positions:
                if isinstance(pos, dict):
                    size = float(pos.get('positionAmt', 0))
                    if abs(size) > 0.001:  # 有实际持仓
                        symbol = pos.get('symbol', '')
                        entry_price = float(pos.get('entryPrice', 0))
                        unrealized_pnl = float(pos.get('unRealizedProfit', 0))
                        
                        active_positions.append({
                            'symbol': symbol,
                            'size': size,
                            'entry_price': entry_price,
                            'unrealized_pnl': unrealized_pnl,
                            'side': 'LONG' if size > 0 else 'SHORT'
                        })

            if active_positions:
                self.log.info(f"启动时发现 {len(active_positions)} 个活跃持仓")
                for pos in active_positions:
                    self.log.info(f"持仓: {pos['symbol']} {pos['side']} {pos['size']} @ {pos['entry_price']}, PnL: {pos['unrealized_pnl']}")
                
                # 检查这些持仓的止损单状态
                self._check_all_positions_stop_orders(active_positions)
                
                # 记录监控指标
                for pos in active_positions:
                    self.metrics_collector.record_position_metric(
                        pos['symbol'], 
                        pos['unrealized_pnl'] > 0, 
                        pos['unrealized_pnl']
                    )
            else:
                self.log.info("启动时无活跃持仓")

        except Exception as e:
            self.log.error(f"恢复持仓状态失败: {e}")

    def _check_all_positions_stop_orders(self, active_positions):
        """检查所有持仓的止损单状态"""
        try:
            # 获取所有挂单
            open_orders = self.trader.http.get('/fapi/v1/openOrders')
            if not open_orders:
                self.log.warning("获取挂单列表失败或为空")
                return

            # 按symbol分组挂单
            orders_by_symbol = {}
            for order in open_orders:
                if isinstance(order, dict):
                    symbol = order.get('symbol', '')
                    if symbol not in orders_by_symbol:
                        orders_by_symbol[symbol] = []
                    orders_by_symbol[symbol].append(order)

            # 检查每个持仓的止损单
            for pos in active_positions:
                symbol = pos['symbol']
                side = pos['side']
                
                symbol_orders = orders_by_symbol.get(symbol, [])
                stop_orders = [o for o in symbol_orders if o.get('type') in ['STOP_MARKET', 'TAKE_PROFIT_MARKET']]
                
                if not stop_orders:
                    self.log.warning(f"持仓 {symbol} ({side}) 缺少止损单，需要补充")
                    # 这里可以添加补充止损单的逻辑
                else:
                    self.log.info(f"持仓 {symbol} ({side}) 已有 {len(stop_orders)} 个止损单")

        except Exception as e:
            self.log.error(f"检查持仓止损单失败: {e}")

    def enhanced_async_scoring(self):
        """
        增强的异步评分机制：
        1. 扩大评分覆盖范围（从40个增加到200个）
        2. 智能批次调度
        3. 动态优先级调整
        """
        try:
            current_time = time.time()
            
            # 检查是否需要处理下一批
            if current_time - self.last_scoring_time < self.scoring_interval:
                return
            
            # 获取所有可评分的币种（包括候选池 + 新发现的币种）
            all_symbols = set()
            
            # 1. 候选池中的币种
            all_symbols.update(self.cand_cache.keys())
            
            # 2. 从缓存中获取更多币种（按交易量排序的前200个）
            try:
                symbols_info = self.cache.load_symbols(self.trader)
                if symbols_info:
                    # symbols_info 是列表格式，直接过滤和排序
                    trading_symbols = [
                        info for info in symbols_info 
                        if info.get('symbol', '').endswith('USDT') and info.get('status') == 'TRADING'
                    ]
                    
                    # 按24h交易量排序，取前200个
                    sorted_symbols = sorted(
                        trading_symbols, 
                        key=lambda x: x.get('volume24h', 0), 
                        reverse=True
                    )[:self.max_scoring_symbols]
                    
                    for info in sorted_symbols:
                        symbol = info.get('symbol')
                        if symbol:
                            all_symbols.add(symbol)
            except Exception as e:
                self.log.warning(f"获取缓存币种失败: {e}")
                # 记录详细的错误信息用于调试
                import traceback
                self.log.debug(f"获取缓存币种异常详情: {traceback.format_exc()}")
            
            # 转换为列表并限制数量
            candidates = list(all_symbols)[:self.max_scoring_symbols]
            
            if not candidates:
                self.log.warning("候选币种列表为空，跳过增强评分 - 请检查币种数据获取是否正常")
                return
            
            # 计算当前批次范围
            start_idx = self.scoring_batch_index * self.scoring_batch_size
            end_idx = min(start_idx + self.scoring_batch_size, len(candidates))
            
            # 如果已处理完所有币种，重置批次索引
            if start_idx >= len(candidates):
                self.scoring_batch_index = 0
                min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
                qualified_count = sum(1 for data in self.cand_cache.values() if data.get('score', 0) >= min_score)
                self.log.info(f"评分周期完成，共处理 {len(candidates)} 个币种，候选池数量: {len(self.cand_cache)}, 达标币种(≥{min_score}分): {qualified_count}")
                return
            
            # 处理当前批次
            batch_symbols = candidates[start_idx:end_idx]
            batch_qualified = []
            batch_removed = []
            
            self.log.info(f"增强评分批次 {self.scoring_batch_index + 1}: 处理 {len(batch_symbols)} 个币种 [{start_idx+1}-{end_idx}]")
            
            for symbol in batch_symbols:
                try:
                    # 获取币龄
                    age_minutes = self.get_symbol_age_minutes(symbol)
                    if age_minutes is None:
                        continue
                    
                    age_days = age_minutes / 1440
                    
                    # 获取K线数据
                    tf = self.dynamic_tf(age_days)
                    interval = f'{tf}m'
                    df = self.get_klines(symbol, interval, 200)
                    
                    # 增强K线数据验证 - 根据币种年龄动态调整要求
                    if df is None:
                        # K线数据获取失败，可能是网络问题，保留在候选池中
                        self.log.warning(f"{symbol}: K线数据获取失败，可能是网络问题，保留在候选池中")
                        continue
                    
                    # 根据币种年龄动态设置最小数据要求
                    if age_days < 1:
                        min_required = 5   # 极新币最低5条
                    elif age_days < 7:
                        min_required = 15  # 新币最低15条
                    elif age_days < 30:
                        min_required = 30  # 较新币最低30条
                    else:
                        min_required = 50  # 老币最低50条
                    
                    if len(df) < min_required:
                        # 数据不足，记录详细信息
                        self.log.warning(f"{symbol}: K线数据不足，仅有{len(df)}条，需要{min_required}条（币龄{age_days:.1f}天）")
                        
                        # 对于数据极少的情况（<5条），直接移除避免无限重试
                        if len(df) < 5:
                            self.log.warning(f"{symbol}: 数据极少（{len(df)}条），直接移除避免重试")
                            if symbol in self.cand_cache:
                                del self.cand_cache[symbol]
                                batch_removed.append(f"{symbol}(数据极少:{len(df)}条)")
                            continue
                        
                        # 检查是否是新币，如果是新币且有基本数据，给予宽容
                        if age_days < 3 and len(df) >= 5:
                            self.log.info(f"{symbol}: 新币（{age_days:.1f}天）数据不足但已有{len(df)}条，暂时保留")
                            continue
                        else:
                            # 非新币或数据严重不足，从候选池移除
                            if symbol in self.cand_cache:
                                del self.cand_cache[symbol]
                                batch_removed.append(f"{symbol}(数据不足:{len(df)}条)")
                        continue
                    
                    # K线数据充足，继续处理
                    try:
                        # 检查通道突破条件
                        if self.check_channel_breakthrough(symbol, df, age_days):
                            score = self.score_symbol(df, age_days, symbol)
                            
                            # 获取最低评分要求
                            min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
                            
                            # 如果评分≥min_score分，加入或更新候选池
                            if score >= min_score:
                                # 获取深度数据
                                depth = self.get_depth01pct(symbol)
                                
                                symbol_data = {
                                    'symbol': symbol,
                                    'score': score,
                                    'age': age_days,
                                    'depth': depth,
                                    'price': df['close'].iloc[-1],
                                    'timestamp': current_time,
                                    'fail_count': 0,
                                    'last_check': current_time
                                }
                                
                                self.cand_cache[symbol] = symbol_data
                                batch_qualified.append(f"{symbol}({score:.1f}分)")
                                
                                self.log.info(f"新增/更新候选: {symbol}, 评分={score:.1f}, 深度={depth:.0f}$")
                        else:
                            # 不满足通道突破条件，从候选池移除
                            if symbol in self.cand_cache:
                                del self.cand_cache[symbol]
                                batch_removed.append(f"{symbol}(未突破)")
                    except Exception as e:
                        # 评分或通道突破检查过程中出错
                        error_msg = str(e).lower()
                        if any(keyword in error_msg for keyword in ['connection', 'timeout', 'network', 'proxy', 'ssl']):
                            self.log.warning(f"{symbol}: 评分过程中网络错误，保留在候选池中: {e}")
                            continue
                        else:
                            self.log.error(f"评分处理{symbol}失败: {e}")
                            # 非网络错误，记录但不立即移除
                            continue
                
                except Exception as e:
                    # 检查是否是网络相关错误
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ['connection', 'timeout', 'network', 'proxy', 'ssl']):
                        self.log.warning(f"{symbol}: 网络相关错误，保留在候选池中: {e}")
                        continue
                    else:
                        self.log.error(f"评分处理{symbol}失败: {e}")
                        import traceback
                        self.log.debug(f"评分处理异常详情: {traceback.format_exc()}")
                        continue
            
            # 记录批次开始时的候选池状态
            initial_cache_size = len(self.cand_cache)
            
            # 更新批次状态
            self.scoring_batch_index += 1
            self.last_scoring_time = current_time
            
            # 输出批次处理结果（增强日志）
            if batch_qualified:
                self.log.info(f"批次 {self.scoring_batch_index} 达标币种: {', '.join(batch_qualified)}")
            else:
                self.log.warning(f"批次 {self.scoring_batch_index} 警告: 无达标币种，请检查评分逻辑或网络状态")
            
            if batch_removed:
                self.log.info(f"批次 {self.scoring_batch_index} 移除币种: {', '.join(batch_removed)}")
            else:
                self.log.info(f"批次 {self.scoring_batch_index} 无移除币种")
            
            # 记录批次处理统计（增强详情）
            final_cache_size = len(self.cand_cache)
            processed_count = len(batch_symbols)
            qualified_count = len(batch_qualified)
            removed_count = len(batch_removed)
            cache_change = final_cache_size - initial_cache_size
            
            self.log.info(f"批次 {self.scoring_batch_index} 处理统计: 处理{processed_count}个, 达标{qualified_count}个, 移除{removed_count}个, 候选池变化: {initial_cache_size}→{final_cache_size} ({cache_change:+d})")
            
            # 记录详细处理情况，便于调试
            if processed_count == 0:
                self.log.warning(f"批次 {self.scoring_batch_index} 警告: 未处理任何币种，候选池可能异常")
            elif removed_count == processed_count:
                self.log.warning(f"批次 {self.scoring_batch_index} 警告: 所有{processed_count}个币种均被移除，可能存在系统性问题")
            elif qualified_count == 0 and processed_count > 0:
                self.log.warning(f"批次 {self.scoring_batch_index} 警告: 处理了{processed_count}个币种但无一达标，请检查评分标准或数据质量")
            
            # 记录网络状态影响
            network_status = self._check_network_status()
            if not network_status.get('connected', False):
                self.log.error(f"批次 {self.scoring_batch_index} 处理期间网络异常: {network_status.get('last_error', '未知错误')}")
            elif network_status.get('latency', 0) > 2000:
                self.log.warning(f"批次 {self.scoring_batch_index} 处理期间网络延迟较高: {network_status.get('latency', 0)}ms")
                
        except Exception as e:
            self.log.error(f"增强异步评分异常: {e}")
            # 记录详细的错误信息用于调试
            import traceback
            self.log.error(f"增强异步评分异常详情: {traceback.format_exc()}")
            
            # 保存当前批次索引，以便后续恢复
            self.last_failed_batch_index = self.scoring_batch_index
            self.last_error_time = time.time()
            self.log.warning(f"批次处理中断，已保存进度点: 批次{self.scoring_batch_index}，将在下次尝试恢复")

    def _async_first_score_batch(self):
        """异步首次打分：分批处理候选池，避免阻塞主循环"""
        try:
            current_time = time.time()
            
            # 检查是否需要处理下一批
            if current_time - self.last_scoring_time < self.scoring_interval:
                return
            
            if self.first_scoring_completed:
                return
            
            # 获取候选池列表
            candidates = list(self.cand_cache.keys())
            if not candidates:
                self.log.warning("候选池为空，跳过异步打分")
                self.first_scoring_completed = True
                return
            
            # 检查是否需要从上次失败的批次恢复
            if hasattr(self, 'last_failed_batch_index') and self.last_failed_batch_index is not None:
                self.log.info(f"从上次失败的批次 {self.last_failed_batch_index} 恢复处理")
                self.scoring_batch_index = self.last_failed_batch_index
                self.last_failed_batch_index = None  # 重置失败标记
            
            # 计算当前批次范围
            start_idx = self.scoring_batch_index * self.scoring_batch_size
            end_idx = min(start_idx + self.scoring_batch_size, len(candidates))
            
            if start_idx >= len(candidates):
                self.first_scoring_completed = True
                # 统计最终结果
                min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
                qualified_count = sum(1 for data in self.cand_cache.values() if data.get('score', 0) >= min_score)
                self.log.info(f"异步首次打分完成，共处理 {len(candidates)} 个币种，候选池数量: {len(self.cand_cache)}, 达标币种(≥{min_score}分): {qualified_count}")
                
                # 异步评分完成后，执行开仓逻辑
                self._execute_opening_logic()
                return
            
            # 处理当前批次
            batch_symbols = candidates[start_idx:end_idx]
            batch_qualified = []  # 记录本批次达标的币种
            batch_removed = []    # 记录本批次移除的币种
            
            self.log.info(f"异步评分批次 {self.scoring_batch_index + 1}: 处理 {len(batch_symbols)} 个币种 [{start_idx+1}-{end_idx}]")
            
            # 记录批次开始时的候选池状态
            initial_cache_size = len(self.cand_cache)
            
            for symbol in batch_symbols:
                try:
                    # 检查网络状态，如果网络异常则保留候选池
                    network_status = self._check_network_status()
                    if not network_status.get('binance_api_ok', True):
                        self.log.warning(f"网络状态异常，跳过 {symbol} 的处理以保护候选池")
                        continue
                    
                    # 获取币龄
                    age_minutes = self.get_symbol_age_minutes(symbol)
                    if age_minutes is None:
                        self.log.warning(f"{symbol}: 无法获取币龄信息，跳过处理")
                        continue
                    
                    age_days = age_minutes / (60 * 24)
                    
                    # 获取K线数据进行评分
                    tf = self.dynamic_tf(age_days)
                    interval = f'{tf}m'
                    df = self.get_klines(symbol, interval, 200)
                    
                    # 增强K线数据验证
                    if df is None:
                        # K线数据获取失败，检查是否是网络问题
                        self.log.warning(f"{symbol}: K线数据获取失败，可能是网络问题，保留在候选池中")
                        # 不移除，给网络恢复的机会
                        continue
                    
                    # 检查K线数据是否足够
                    if len(df) < 50:
                        # 对于新币（小于7天），如果有至少15根K线，可以尝试评分
                        if age_days < 7 and len(df) >= 15:
                            self.log.warning(f"{symbol}: 新币K线数据不足 ({len(df)}条)，但尝试评分")
                        elif age_days < 1 and len(df) >= 5:
                            # 对于极新币种（小于1天），最低5条K线就尝试评分
                            self.log.warning(f"{symbol}: 极新币K线数据极少 ({len(df)}条)，尝试评分")
                        else:
                            # 数据确实不足，从候选池移除
                            if symbol in self.cand_cache:
                                del self.cand_cache[symbol]
                                batch_removed.append(f"{symbol}(数据不足:{len(df)}条)")
                                self.log.debug(f"移除币种: {symbol} - 数据不足，仅有{len(df)}条K线")
                            continue
                    
                    try:
                        # 首先检查通道突破条件
                        if not self.check_channel_breakthrough(symbol, df, age_days):
                            # 如果不满足通道突破条件，从候选池中移除
                            if symbol in self.cand_cache:
                                del self.cand_cache[symbol]
                                batch_removed.append(f"{symbol}(未满足通道突破)")
                                self.log.debug(f"移除币种: {symbol} - 未满足通道突破条件")
                            continue
                        
                        score = self.score_symbol(df, age_days, symbol)
                        self.cand_cache[symbol]['score'] = score
                        self.cand_cache[symbol]['last_update'] = current_time
                        
                        # 添加调试日志
                        self.log.info(f"评分计算完成: {symbol} = {score:.2f}分")
                        
                        # 检查是否达到候选池准入标准
                        min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
                        if score >= min_score:
                            channel_pos = self._get_channel_position(symbol)
                            batch_qualified.append(f"{symbol}({score}分,{channel_pos})")
                        
                        # 记录监控指标
                        self.metrics_collector.record_order({'type': 'score_calculation', 'symbol': symbol, 'score': score, 'status': 'success'})
                    except Exception as scoring_e:
                        # 检查是否是网络相关错误
                        error_msg = str(scoring_e).lower()
                        if any(keyword in error_msg for keyword in ['connection', 'timeout', 'network', 'proxy', 'ssl']):
                            self.log.warning(f"{symbol}: 评分过程中发生网络错误，保留在候选池中: {scoring_e}")
                            continue
                        else:
                            # 非网络错误，记录详细信息但仍保留在候选池中
                            self.log.error(f"{symbol}: 评分过程中发生错误: {scoring_e}")
                            import traceback
                            self.log.error(f"{symbol} 评分错误详情: {traceback.format_exc()}")
                            # 不移除，给下次评分的机会
                    
                except Exception as e:
                    self.log.error(f"处理{symbol}时出错: {e}")
                    # 检查是否是网络相关错误
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ['connection', 'timeout', 'network', 'proxy', 'ssl']):
                        self.log.warning(f"{symbol}: 网络相关错误，保留在候选池中等待网络恢复")
                        continue
                    else:
                        # 记录详细错误信息，但仍保留在候选池中
                        import traceback
                        self.log.error(f"{symbol} 处理错误详情: {traceback.format_exc()}")
                        # 不移除，给下次评分的机会
            
            # 输出批次处理结果（增强日志）
            if batch_qualified:
                self.log.info(f"批次 {self.scoring_batch_index + 1} 达标币种: {', '.join(batch_qualified)}")
            else:
                self.log.warning(f"批次 {self.scoring_batch_index + 1} 警告: 无达标币种，请检查评分逻辑或网络状态")
            
            if batch_removed:
                self.log.info(f"批次 {self.scoring_batch_index + 1} 移除币种: {', '.join(batch_removed)}")
            else:
                self.log.info(f"批次 {self.scoring_batch_index + 1} 无移除币种")
            
            # 记录批次处理统计（增强详情）
            final_cache_size = len(self.cand_cache)
            processed_count = len(batch_symbols)
            qualified_count = len(batch_qualified)
            removed_count = len(batch_removed)
            cache_change = final_cache_size - initial_cache_size
            
            self.log.info(f"批次 {self.scoring_batch_index + 1} 处理统计: 处理{processed_count}个, 达标{qualified_count}个, 移除{removed_count}个, 候选池变化: {initial_cache_size}→{final_cache_size} ({cache_change:+d})")
            
            # 记录详细处理情况，便于调试
            if processed_count == 0:
                self.log.warning(f"批次 {self.scoring_batch_index + 1} 警告: 未处理任何币种，候选池可能异常")
            elif removed_count == processed_count:
                self.log.warning(f"批次 {self.scoring_batch_index + 1} 警告: 所有{processed_count}个币种均被移除，可能存在系统性问题")
            elif qualified_count == 0 and processed_count > 0:
                self.log.warning(f"批次 {self.scoring_batch_index + 1} 警告: 处理了{processed_count}个币种但无一达标，请检查评分标准或数据质量")
            
            # 记录网络状态影响
            network_status = self._check_network_status()
            if not network_status.get('connected', False):
                self.log.error(f"批次 {self.scoring_batch_index + 1} 处理期间网络异常: {network_status.get('last_error', '未知错误')}")
            elif network_status.get('latency', 0) > 2000:
                self.log.warning(f"批次 {self.scoring_batch_index + 1} 处理期间网络延迟较高: {network_status.get('latency', 0)}ms")
            
            # 更新批次索引和时间
            self.scoring_batch_index += 1
            self.last_scoring_time = current_time
            
        except Exception as e:
            self.log.error(f"异步首次打分批处理失败: {e}")
            # 记录详细的错误信息用于调试
            import traceback
            self.log.error(f"异步打分批处理异常详情: {traceback.format_exc()}")
            
            # 保存当前批次索引，以便后续恢复
            self.last_failed_batch_index = self.scoring_batch_index
            self.last_error_time = time.time()
            self.log.warning(f"批次处理中断，已保存进度点: 批次{self.scoring_batch_index}，将在下次尝试恢复")

    def calculate_atr(self, high, low, close, length=14):
        """计算ATR（平均真实波幅）"""
        try:
            # 计算真实波幅
            high_low = high - low
            high_close_prev = abs(high - close.shift(1))
            low_close_prev = abs(low - close.shift(1))
            
            # 取三者最大值作为真实波幅
            true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
            
            # 计算ATR（简单移动平均）
            atr = true_range.rolling(window=length).mean()
            return atr.iloc[-1] if not atr.empty else 0.0
        except Exception as e:
            self.log.error(f"ATR计算失败: {e}")
            return 0.0

    def calculate_dynamic_thresholds(self, df_d, symbol, age_days):
        """基于ATR计算动态阈值"""
        try:
            # 支持新旧列名格式
            high_col = 'high' if 'high' in df_d.columns else 'h'
            low_col = 'low' if 'low' in df_d.columns else 'l'
            close_col = 'close' if 'close' in df_d.columns else 'c'
            
            # 计算ATR
            atr = self.calculate_atr(df_d[high_col], df_d[low_col], df_d[close_col], 14)
            current_price = df_d[close_col].iloc[-1]
            
            # 获取基础配置
            config = self.channel_config.get('channel_breakthrough', {})
            base_close_threshold = config.get('close_ratio_threshold', 0.97)
            base_high_threshold = config.get('high_ratio_threshold', 0.985)
            
            # ATR波动系数（ATR相对于当前价格的比例）
            volatility_coefficient = atr / current_price if current_price > 0 else 0
            
            # 动态调整阈值：波动率高时放宽阈值，波动率低时收紧阈值
            # 使用0.3和1.0作为调整系数，参考KIMI AI建议
            dynamic_close_threshold = 1 + 0.3 * volatility_coefficient
            dynamic_high_threshold = 1 + 1.0 * volatility_coefficient
            
            # 确保动态阈值在合理范围内
            dynamic_close_threshold = max(0.95, min(1.05, dynamic_close_threshold))
            dynamic_high_threshold = max(0.98, min(1.10, dynamic_high_threshold))
            
            self.log.debug(f"{symbol} ATR动态阈值 - ATR: {atr:.6f}, 波动系数: {volatility_coefficient:.4f}, "
                          f"动态收盘阈值: {dynamic_close_threshold:.4f}, 动态最高价阈值: {dynamic_high_threshold:.4f}")
            
            return dynamic_close_threshold, dynamic_high_threshold, volatility_coefficient
            
        except Exception as e:
            self.log.error(f"{symbol} 动态阈值计算失败: {e}")
            # 返回默认阈值
            config = self.channel_config.get('channel_breakthrough', {})
            return config.get('close_ratio_threshold', 0.97), config.get('high_ratio_threshold', 0.985), 0.0

    def check_fake_breakthrough_filter(self, df_d, symbol):
        """检查针尖假突破过滤机制"""
        try:
            # 支持新旧列名格式
            high_col = 'high' if 'high' in df_d.columns else 'h'
            close_col = 'close' if 'close' in df_d.columns else 'c'
            open_col = 'open' if 'open' in df_d.columns else 'o'
            
            # 检查最近3根K线的上影线情况
            if len(df_d) < 3:
                return True  # 数据不足，通过检查
                
            recent3 = df_d.iloc[-3:]
            
            # 计算上影线长度和实体长度
            upper_shadow = recent3[high_col] - recent3[close_col]
            body_length = abs(recent3[close_col] - recent3[open_col])
            
            # 检查是否有连续的长上影线（上影线长度 > 2倍实体长度）
            long_upper_shadow_count = (upper_shadow > 2 * body_length).sum()
            
            if long_upper_shadow_count >= 2:
                self.log.debug(f"{symbol} 检测到连续上影线假突破，过滤该信号")
                return False
                
            return True
            
        except Exception as e:
            self.log.error(f"{symbol} 假突破过滤检查失败: {e}")
            return True  # 出错时通过检查

    def get_depth01pct(self, symbol):
        """获取0.1%深度数据"""
        try:
            # 先检查缓存
            if hasattr(self, 'cache') and self.cache:
                cached_depth = self.cache.load_depth(symbol)
                if cached_depth is not None:
                    self.log.debug(f"使用缓存深度数据: {symbol}")
                    return cached_depth
            
            import time
            # 重试机制，最多重试3次
            for attempt in range(3):
                try:
                    book = self.trader.http.get('/fapi/v1/depth', {'symbol': symbol, 'limit': 100})
                    if not book or 'bids' not in book or 'asks' not in book:
                        self.log.warning(f"获取{symbol}深度数据失败，第{attempt+1}次重试")
                        time.sleep(0.5)
                        continue
                    
                    bids, asks = book['bids'], book['asks']
                    if not bids or not asks:
                        self.log.warning(f"{symbol}深度数据为空，第{attempt+1}次重试")
                        time.sleep(0.5)
                        continue
                    
                    mid = (float(bids[0][0]) + float(asks[0][0])) / 2
                    bid_depth = sum([float(v) for p, v in bids if float(p) >= mid * 0.999])
                    ask_depth = sum([float(v) for p, v in asks if float(p) <= mid * 1.001])
                    
                    depth = min(bid_depth, ask_depth)
                    if depth <= 0:
                        self.log.warning(f"{symbol}深度计算异常: {depth}")
                        time.sleep(0.5)
                        continue
                    
                    # 保存到缓存
                    if hasattr(self, 'cache') and self.cache:
                        self.cache.save_depth(symbol, depth)
                    
                    return depth
                    
                except Exception as e:
                    self.log.warning(f"获取{symbol}深度数据异常，第{attempt+1}次重试: {e}")
                    time.sleep(0.5)
                    continue
            
            # 所有重试都失败，返回默认值
            self.log.error(f"获取{symbol}深度数据失败，使用默认值")
            return 100000  # 默认深度值
            
        except Exception as e:
            self.log.error(f"获取{symbol}深度数据失败: {e}")
            return 100000  # 默认深度值

    def get_klines(self, symbol, interval='15m', limit=200):
        """获取K线数据，增强新币处理"""
        try:
            # 使用策略优化器的缓存功能
            cache_key = f"klines_{symbol}_{interval}_{limit}"
            cached_data = self.strategy_optimizer.cache_manager.get(cache_key)
            if cached_data is not None:
                self.log.debug(f"使用缓存K线数据: {symbol}_{interval}")
                return cached_data
            
            # 获取币龄，判断是否为新币种
            age_minutes = self.get_symbol_age_minutes(symbol)
            age_days = age_minutes / (60 * 24) if age_minutes else 500
            
            # 检查是否为新币种，如果是则使用专门的处理器
            if self.new_coin_handler.is_new_coin(symbol, age_days):
                self.log.info(f"{symbol}: 检测到新币种（币龄{age_days:.1f}天），使用专门处理器")
                df = self.new_coin_handler.get_klines_for_new_coin(symbol, interval, limit, age_days)
                
                # 验证新币种数据
                is_valid, message = self.new_coin_handler.validate_new_coin_data(symbol, df)
                if is_valid:
                    # 缓存有效数据
                    self.strategy_optimizer.cache_manager.set(cache_key, df)
                    self.log.info(f"{symbol}: 新币种数据验证通过 - {message}")
                    return df
                else:
                    self.log.warning(f"{symbol}: 新币种数据验证失败 - {message}")
                    return None
            
            # 对于非新币种，使用原有逻辑但优化参数
            # 记录网络状态（优化：不因网络检查失败而直接返回None）
            network_status = self._check_network_status()
            if not network_status.get('connected', False):
                self.log.debug(f"网络状态检查异常，但继续尝试获取{symbol}的K线数据: {network_status.get('last_error', '未知错误')}")
                # 不直接返回None，而是继续尝试获取数据
            
            # 添加重试机制
            max_retries = 3
            retry_delay = 1.0
            
            # 记录详细的币龄信息（改为DEBUG级别，避免刷屏）
            self.log.debug(f"{symbol}: 币龄 {age_days:.1f} 天，使用 {interval} 周期，限制 {limit} 条K线，重试次数 {max_retries}")
            
            for attempt in range(max_retries):
                try:
                    # 使用BinanceTrader的get_klines方法，而不是直接调用HTTP API
                    klines = self.trader.get_klines(symbol, interval, limit)
                    
                    if not klines or len(klines) == 0:
                        self.log.warning(f"获取{symbol}的K线数据为空，第{attempt+1}次重试")
                        time.sleep(retry_delay)
                        continue
                        
                    # 检查数据有效性 - 根据币种年龄调整要求
                    if age_days < 1:
                        min_klines = 5   # 极新币最低5条
                    elif age_days < 7:
                        min_klines = 15  # 新币最低15条
                    else:
                        min_klines = 20  # 老币最低20条
                        
                    if len(klines) < min_klines:
                        self.log.warning(f"获取{symbol}的K线数据不足，仅有{len(klines)}条（需要{min_klines}条），第{attempt+1}次重试")
                        time.sleep(retry_delay)
                        continue
                    
                    # BinanceTrader.get_klines返回原始列表格式，需要转换为DataFrame
                    if isinstance(klines, list) and len(klines) > 0:
                        try:
                            df = pd.DataFrame(klines, columns=['t', 'o', 'h', 'l', 'c', 'v', 'ct', 'qv', 'n', 'tbbav', 'tbqav', 'ignore'])
                            df = df[['t', 'o', 'h', 'l', 'c', 'v']].astype(float)
                            df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                            df.set_index('timestamp', inplace=True)
                        except Exception as convert_e:
                            self.log.warning(f"转换{symbol}的K线数据格式时发生错误: {convert_e}")
                            time.sleep(retry_delay)
                            continue
                    else:
                        self.log.warning(f"获取{symbol}的K线数据格式异常或为空，类型: {type(klines)}, 长度: {len(klines) if hasattr(klines, '__len__') else 'N/A'}")
                        time.sleep(retry_delay)
                        continue
                    
                    # 二次验证数据质量，确保只缓存有效数据
                    if len(df) >= min_klines and not df.empty:
                        # 缓存验证通过的结果
                        self.strategy_optimizer.cache_manager.set(cache_key, df)
                        self.log.debug(f"成功获取{symbol}的K线数据，共{len(df)}条，已缓存")
                        return df
                    else:
                        self.log.warning(f"{symbol}数据质量验证失败，DataFrame长度{len(df)}，不进行缓存")
                        time.sleep(retry_delay)
                        continue
                    
                except Exception as retry_e:
                    # 检查是否是网络相关错误
                    error_msg = str(retry_e).lower()
                    is_network_error = any(keyword in error_msg for keyword in ['connection', 'timeout', 'network', 'proxy', 'ssl'])
                    
                    if is_network_error:
                        self.log.warning(f"获取{symbol}的K线数据时发生网络错误，第{attempt+1}次重试: {retry_e}")
                    else:
                        self.log.warning(f"获取{symbol}的K线数据时发生错误，第{attempt+1}次重试: {retry_e}")
                        
                    # 记录详细的错误信息用于调试
                    self.log.debug(f"错误详情 - 类型: {type(retry_e)}, 消息: {str(retry_e)}")
                    time.sleep(retry_delay)
                    continue
            
            # 所有重试都失败
            self.log.error(f"获取{symbol}的K线数据失败，已重试{max_retries}次")
            return None
            
        except Exception as e:
            # 检查是否是网络相关错误
            error_msg = str(e).lower()
            is_network_error = any(keyword in error_msg for keyword in ['connection', 'timeout', 'network', 'proxy', 'ssl'])
            
            if is_network_error:
                self.log.error(f"获取{symbol}的K线数据时发生网络错误: {e}")
            else:
                self.log.error(f"获取{symbol}的K线数据时发生错误: {e}")
                import traceback
                self.log.error(f"详细错误信息: {traceback.format_exc()}")
            return None

    def score_symbol(self, df_d, age_days=None, symbol=None):
        """计算币种评分"""
        try:
            # 使用增强评分计算器，传递权重配置
            from enhanced_score_calculator import EnhancedScoreCalculator
            
            # 构建权重配置
            scoring_config = {
                'weights': self.channel_config.get('scoring_weights', {
                    'depth': 1.5,
                    'volume': 1.0,
                    'age': 1.5,
                    'momentum': 4.0,
                    'channel': 3.5,
                    'volatility': 1.0,
                    'liquidity': 1.0
                })
            }
            
            calculator = EnhancedScoreCalculator(scoring_config)
            
            # 添加详细调试日志
            self.log.debug(f"开始计算 {symbol} 评分，数据行数: {len(df_d) if df_d is not None else 0}, 币龄: {age_days}")
            
            # 获取深度数据
            depth_data = None
            if symbol:
                try:
                    depth_data = self.get_depth01pct(symbol)
                    self.log.debug(f"{symbol} 深度数据获取成功: {depth_data}")
                except Exception as depth_e:
                    self.log.warning(f"{symbol} 深度数据获取失败: {depth_e}")
                    depth_data = None
            
            score_result = calculator.calculate_comprehensive_score(
                symbol or "unknown", 
                df_d, 
                depth_data=depth_data,  # 传递实际的深度数据
                additional_data={'age_days': age_days} if age_days else None
            )
            score = score_result.total_score if hasattr(score_result, 'total_score') else score_result
            
            self.log.info(f"{symbol} 评分计算结果: {score}, 类型: {type(score_result)}")
            
            # 记录监控指标
            self.metrics_collector.record_order({'type': 'symbol_score', 'symbol': symbol or 'unknown', 'score': score, 'status': 'success'})
            
            return score
            
        except Exception as e:
            self.log.error(f"评分计算失败 {symbol}: {e}", exc_info=True)
            return 0.0

    def check_channel_breakthrough(self, symbol, df_d, age_days):
        """检查通道突破条件 - 使用配置文件参数和动态阈值"""
        try:
            # 获取配置参数
            config = self.channel_config.get('channel_breakthrough', {})
            max_breakthrough_pct = config.get('max_breakthrough_pct', 0.15)
            max_consecutive_near_upper = config.get('max_consecutive_near_upper', 8)
            min_volume_ratio = config.get('min_volume_ratio', 1.2)
            
            # 获取基础过滤配置
            filter_config = self.channel_config.get('basic_filtering', {})
            min_volume_absolute = filter_config.get('min_volume_absolute', 1000000)
            max_volatility_pct = filter_config.get('max_volatility_pct', 0.30)
            
            # 使用动态时间框架计算通道周期
            n = dynamic_tf_for_channel(age_days)  # 直接返回K线数量
            n = max(2, int(n))  # 确保至少2根K线
            self.log.debug(f"通道突破检查 - 币种: {symbol}, 币龄: {age_days}天, 动态周期: {n}根K线")
            
            # 支持新旧列名格式
            high_col = 'high' if 'high' in df_d.columns else 'h'
            close_col = 'close' if 'close' in df_d.columns else 'c'
            volume_col = 'volume' if 'volume' in df_d.columns else 'v'
            
            # 计算通道上轨（历史最高价）
            upper_band = df_d[high_col].rolling(n).max().iloc[-1]
            current_price = df_d[close_col].iloc[-1]
            high_price = df_d[high_col].iloc[-1]  # 当前K线最高价
            current_volume = df_d[volume_col].iloc[-1]
            avg_volume = df_d[volume_col].rolling(n).mean().iloc[-1]
            
            self.log.debug(f"通道数据 - 上轨: {upper_band}, 当前价: {current_price}, 最高价: {high_price}")
            
            # 基础过滤条件检查
            # 1. 绝对成交量检查
            if current_volume < min_volume_absolute:
                self.log.debug(f"绝对成交量不足: {current_volume} < {min_volume_absolute}")
                return False
                
            # 2. 波动率检查
            if len(df_d) >= 2:
                price_change = abs(current_price - df_d[close_col].iloc[-2]) / df_d[close_col].iloc[-2]
                if price_change > max_volatility_pct:
                    self.log.debug(f"价格波动过大: {price_change:.4f} > {max_volatility_pct}")
                    return False
            
            # 3. 假突破过滤检查
            if not self.check_fake_breakthrough_filter(df_d, symbol):
                return False
            
            # 计算动态阈值
            dynamic_close_threshold, dynamic_high_threshold, volatility_coefficient = self.calculate_dynamic_thresholds(df_d, symbol, age_days)
            
            # 条件1：收盘价或最高价突破检查（使用动态阈值）
            close_ratio = current_price / upper_band
            high_ratio = high_price / upper_band
            
            self.log.debug(f"条件1检查 - 收盘比例: {close_ratio:.4f} (动态阈值: {dynamic_close_threshold:.4f}), "
                          f"最高价比例: {high_ratio:.4f} (动态阈值: {dynamic_high_threshold:.4f})")
            
            # 检查是否满足突破条件（使用动态阈值）
            breakthrough_condition = (close_ratio >= dynamic_close_threshold) or (high_ratio >= dynamic_high_threshold)
            if not breakthrough_condition:
                self.log.debug(f"未满足动态突破条件: 收盘比例={close_ratio:.4f}, 最高价比例={high_ratio:.4f}")
                return False
            
            # 条件2：突破幅度限制
            if high_ratio >= 1.0:
                breakthrough_pct = (high_price - upper_band) / upper_band
                self.log.debug(f"条件2检查 - 突破幅度: {breakthrough_pct:.4f} (限制: {max_breakthrough_pct})")
                if breakthrough_pct > max_breakthrough_pct:
                    self.log.debug(f"突破幅度过大: {breakthrough_pct:.4f} > {max_breakthrough_pct}")
                    return False
            
            # 条件3：连续接近上轨检查
            recent_above_count = 0
            check_range = min(max_consecutive_near_upper, len(df_d))
            for i in range(-check_range, 0):
                if i >= -len(df_d):
                    historical_upper = df_d[high_col].rolling(n).max().iloc[i]
                    if df_d[high_col].iloc[i] >= historical_upper * 0.95:  # 95%以上算接近
                        recent_above_count += 1
            
            self.log.debug(f"条件3检查 - 近期接近上轨次数: {recent_above_count} (限制: {max_consecutive_near_upper})")
            if recent_above_count >= max_consecutive_near_upper:
                self.log.debug(f"接近上轨时间过长: 连续{recent_above_count}根K线接近")
                return False
            
            # 条件4：双重成交量验证
            volume_validation_result = self.volume_validator.validate_volume(
                symbol=symbol,
                current_volume=current_volume,
                df=df_d
            )
            
            self.log.debug(f"条件4检查 - 双重成交量验证: {volume_validation_result}")
            if not volume_validation_result['valid']:
                self.log.debug(f"双重成交量验证失败: {volume_validation_result['reason']}")
                return False
            
            # 条件5：筹码抛压扫描
            chip_pressure_result = self.chip_scanner.scan_chip_pressure(symbol, current_price)
            
            self.log.debug(f"条件5检查 - 筹码抛压扫描: {chip_pressure_result}")
            if not chip_pressure_result['valid']:
                self.log.debug(f"筹码抛压扫描失败: {chip_pressure_result['reason']}")
                return False
            
            # 记录突破信息
            breakthrough_info = (f"收盘比例={close_ratio:.4f}, 最高价比例={high_ratio:.4f}, "
                               f"动态周期={n}根K线, 双重成交量验证通过, 筹码抛压检测通过, "
                               f"波动系数={volatility_coefficient:.4f}")
            if high_ratio >= 1.0:
                breakthrough_pct = (high_price - upper_band) / upper_band
                breakthrough_info += f", 突破幅度={breakthrough_pct:.4f}"
            
            self.log.info(f"通过通道突破检查: {symbol} - {breakthrough_info}")
            return True
            
        except Exception as e:
            self.log.error(f"通道突破检查失败: {e}")
            return False

    def full_market_scan(self):
        """全市场扫描：定期检查所有币种的通道突破情况"""
        current_time = time.time()
        
        # 每30分钟执行一次全市场扫描
        if not hasattr(self, 'last_full_market_scan'):
            self.last_full_market_scan = 0
            
        if current_time - self.last_full_market_scan < 1800:  # 30分钟
            return
            
        self.last_full_market_scan = current_time
        self.log.info("开始全市场扫描，寻找新的通道突破币种...")
        
        # 初始化统计变量
        scan_stats = {
            'total_symbols': 0,
            'scanned_symbols': 0,
            'skipped_in_candidates': 0,
            'skipped_age_too_young': 0,
            'skipped_no_data': 0,
            'failed_symbols': 0,
            'breakthrough_symbols': 0,
            'low_score_symbols': 0,
            'new_breakthrough_count': 0,
            'scan_start_time': current_time,
            'network_issues': 0,  # 新增网络问题统计
            'data_quality_issues': 0,  # 新增数据质量问题统计
            'batch_progress': 0,  # 新增批次进度
            'high_quality_breakthroughs': 0,  # 新增高质量突破统计
            'symbol_details': []  # 新增详细记录
        }
        
        try:
            # 获取所有交易对基础信息
            symbols_info = self.cache.load_symbols(self.trader)
            if not symbols_info:
                symbols_info = self.cold_start_symbols()
                self.cache.save_symbols(symbols_info)
            
            # 将缓存数据赋值给实例属性，供get_symbol_age_minutes使用
            self.symbols_info = symbols_info
            
            all_symbols = [s['symbol'] for s in symbols_info]
            current_candidates = set(self.cand_cache.keys())
            scan_stats['total_symbols'] = len(all_symbols)
            
            self.log.info(f"全市场扫描开始：总币种数量 {scan_stats['total_symbols']}，当前候选池 {len(current_candidates)} 个")
            
            # 分批扫描，避免API限制
            batch_size = 20
            total_batches = (len(all_symbols) + batch_size - 1) // batch_size
            
            for i in range(0, len(all_symbols), batch_size):
                batch_num = (i // batch_size) + 1
                scan_stats['batch_progress'] = batch_num
                
                batch_symbols = all_symbols[i:i+batch_size]
                
                # 批次开始日志
                self.log.info(f"扫描批次 {batch_num}/{total_batches}: {len(batch_symbols)} 个币种")
                
                for symbol in batch_symbols:
                    # 检查币种是否在失败黑名单中
                    if self._should_skip_failed_symbol(symbol):
                        scan_stats['skipped_no_data'] += 1
                        continue
                    
                    # 跳过已在候选池的币种
                    if symbol in current_candidates:
                        scan_stats['skipped_in_candidates'] += 1
                        continue
                    
                    scan_stats['scanned_symbols'] += 1
                    
                    try:
                        # 获取币龄
                        age_minutes = self.get_symbol_age_minutes(symbol)
                        if age_minutes < 1440:  # 跳过上线不足1天的币种
                            scan_stats['skipped_age_too_young'] += 1
                            continue
                        
                        # 动态时间框架
                        age_days = age_minutes / (60 * 24)
                        tf = self.dynamic_tf(age_days)
                        
                        # 获取K线数据
                        interval = f'{tf}m'
                        df = self.get_klines(symbol, interval, 200)
                        if df.empty:
                            scan_stats['skipped_no_data'] += 1
                            # 区分是网络问题还是数据质量问题
                            if hasattr(self, 'network_status') and not self.network_status.get('connected', True):
                                scan_stats['network_issues'] += 1
                            else:
                                scan_stats['data_quality_issues'] += 1
                            continue
                        
                        # 检查通道突破
                        if self.check_channel_breakthrough(symbol, df, age_days):
                            scan_stats['breakthrough_symbols'] += 1
                            
                            # 计算评分
                            age_days = age_minutes / 1440
                            score = self.score_symbol(df, age_days, symbol)
                            M = 0  # M变量暂时设为默认值
                            
                            min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
                            if score >= min_score:
                                # 获取深度数据
                                depth = self.get_depth01pct(symbol)
                                
                                # 添加到候选池
                                symbol_data = {
                                    'symbol': symbol,
                                    'score': score,
                                    'M': M,
                                    'age': age_days,
                                    'depth': depth,
                                    'price': df['close'].iloc[-1],
                                    'timestamp': time.time()
                                }
                                
                                self.cand_cache[symbol] = symbol_data
                                scan_stats['new_breakthrough_count'] += 1
                                
                                # 记录高质量突破
                                if score >= min_score + 2:  # 评分高于最低要求2分以上
                                    scan_stats['high_quality_breakthroughs'] += 1
                                
                                # 记录详细突破信息
                                symbol_detail = {
                                    'symbol': symbol,
                                    'score': score,
                                    'depth': depth,
                                    'age_days': age_days,
                                    'price': df['close'].iloc[-1],
                                    'is_high_quality': score >= min_score + 2
                                }
                                scan_stats['symbol_details'].append(symbol_detail)
                                
                                self.log.debug(f"发现新突破币种: {symbol}, 评分={score:.1f}, 深度={depth:.0f}$, 币龄={age_days:.0f}天")
                            else:
                                scan_stats['low_score_symbols'] += 1
                                self.log.debug(f"币种 {symbol} 突破但评分不足: {score:.1f} < {min_score}")
                        
                    except Exception as e:
                        scan_stats['failed_symbols'] += 1
                        # 记录失败币种
                        self._record_symbol_failure(symbol, e)
                        
                        # 区分错误类型
                        error_msg = str(e).lower()
                        if any(keyword in error_msg for keyword in ['connection', 'timeout', 'network', 'api']):
                            scan_stats['network_issues'] += 1
                        else:
                            scan_stats['data_quality_issues'] += 1
                        self.log.debug(f"扫描币种{symbol}失败: {e}")
                        continue
                
                # 批次间暂停，避免API限制
                time.sleep(0.5)
            
            # 计算扫描耗时
            scan_duration = time.time() - scan_stats['scan_start_time']
            
            # 输出详细统计信息
            self.log.info(f"全市场扫描完成，耗时 {scan_duration:.1f}秒")
            self.log.info(f"扫描统计：")
            self.log.info(f"  总币种数量: {scan_stats['total_symbols']}")
            self.log.info(f"  实际扫描: {scan_stats['scanned_symbols']}")
            self.log.info(f"  跳过（已在候选池）: {scan_stats['skipped_in_candidates']}")
            self.log.debug(f"  跳过（币龄不足）: {scan_stats['skipped_age_too_young']}")
            self.log.info(f"  跳过（无数据）: {scan_stats['skipped_no_data']}")
            self.log.info(f"  扫描失败: {scan_stats['failed_symbols']}")
            self.log.info(f"  通道突破: {scan_stats['breakthrough_symbols']}")
            self.log.info(f"  评分不足: {scan_stats['low_score_symbols']}")
            self.log.info(f"  新增候选: {scan_stats['new_breakthrough_count']}")
            self.log.info(f"  高质量突破: {scan_stats['high_quality_breakthroughs']}")
            self.log.info(f"  网络问题: {scan_stats['network_issues']}")
            self.log.info(f"  数据质量问题: {scan_stats['data_quality_issues']}")
            
            # 输出高质量突破币种详情
            if scan_stats['high_quality_breakthroughs'] > 0:
                high_quality_symbols = [detail for detail in scan_stats['symbol_details'] if detail['is_high_quality']]
                high_quality_symbols.sort(key=lambda x: x['score'], reverse=True)
                
                self.log.info(f"高质量突破币种详情（评分≥{min_score + 2}分）：")
                for detail in high_quality_symbols[:5]:  # 只显示前5个
                    self.log.debug(f"  {detail['symbol']}: 评分={detail['score']:.1f}, 深度={detail['depth']:.0f}$, 币龄={detail['age_days']:.0f}天, 价格=${detail['price']:.4f}")
                
                if len(high_quality_symbols) > 5:
                    self.log.info(f"  ... 还有 {len(high_quality_symbols) - 5} 个高质量突破币种")
            
            # 计算扫描效率指标
            scan_efficiency = (scan_stats['new_breakthrough_count'] / max(scan_stats['scanned_symbols'], 1)) * 100
            breakthrough_rate = (scan_stats['breakthrough_symbols'] / max(scan_stats['scanned_symbols'], 1)) * 100
            
            self.log.info(f"扫描效率指标：")
            self.log.info(f"  扫描效率: {scan_efficiency:.2f}% (新增候选/实际扫描)")
            self.log.info(f"  突破率: {breakthrough_rate:.2f}% (通道突破/实际扫描)")
            
            # 保存扫描统计到实例变量，供其他方法使用
            self.last_scan_stats = scan_stats
            
            # 清理过期的失败记录
            self._cleanup_failed_symbols()
            
        except Exception as e:
            self.log.error(f"全市场扫描异常: {e}")
            # 即使异常也要记录统计信息
            if 'scan_stats' in locals():
                scan_duration = time.time() - scan_stats['scan_start_time']
                self.log.error(f"扫描异常前统计：扫描 {scan_stats['scanned_symbols']}/{scan_stats['total_symbols']} 个币种，耗时 {scan_duration:.1f}秒")
                self.log.error(f"异常时统计：突破 {scan_stats['breakthrough_symbols']} 个，新增候选 {scan_stats['new_breakthrough_count']} 个")

    def incremental_scan(self):
        """优化后的增量扫描：轻量并行处理 + 末位淘汰 + 全市场监控"""
        current_time = time.time()
        now_utc = pd.Timestamp.utcnow()
        
        # ① 每日冷启动：只拉基础信息（00:10执行）
        if now_utc.hour == 0 and now_utc.minute == 10 and current_time - self.last_full_scan > 3600:
            self.log.info("执行每日冷启动：仅更新全币种基础信息")
            symbols_info = self.cold_start_symbols()
            self.cache.save_symbols(symbols_info)
            self.last_full_scan = current_time
        
        # ② 全市场扫描：定期发现新突破币种（30分钟频率）
        self.full_market_scan()
        
        # ③ 持仓币轻量更新（1分钟频率）
        if self.pos:
            symbol = self.pos['symbol']
            # 只拉50根1分钟K线
            df = self.get_klines(symbol, '1m', 50)
            if not df.empty:
                current_price = df['close'].iloc[-1]
                self.check_position_conditions(symbol, current_price)
        
        # ④ 候选池并行更新（15分钟频率） + 智能管理 + 末位淘汰
        candidates = list(self.cand_cache.keys())[:20]  # 只处理前20个高评分候选
        if current_time - self.last_candidate_scan > 900 and candidates:  # 15分钟
            # 先执行智能候选池管理
            self.manage_candidate_pool(max_candidates=50)
            
            self.last_candidate_scan = current_time
            self.log.info(f"并行更新候选池: {len(candidates)} 个币种")
            self.parallel_update(candidates, '15m', 50)  # 只拉50根K线
            
            # 末位淘汰：只留前3名
            top3 = self.select_top3(candidates)
            if top3:
                self.log.info(f"末位淘汰完成: {len(candidates)} → {len(top3)} 个币种")
                for i, candidate in enumerate(top3):
                    self.log.debug(f"Top{i+1}: {candidate['symbol']} 得分:{candidate['score']} 深度:{candidate['depth']:.0f}$ 币龄:{candidate['age_days']:.0f}天")
        
        # ⑤ 深度数据缓存更新（5分钟频率）
        if current_time % 300 < 30:  # 每5分钟
            # 只更新持仓币和候选池的深度
            symbols_to_update = []
            if self.pos:
                symbols_to_update.append(self.pos['symbol'])
            symbols_to_update.extend(candidates[:10])  # 只更新前10个候选
            
            for symbol in symbols_to_update:
                depth = self.get_depth01pct(symbol)
                self.cache.save_depth(symbol, depth)

    def update_symbol(self, symbol, interval, limit):
        """更新单个币种数据并重新评分"""
        try:
            # 获取K线数据
            df = self.get_klines(symbol, interval, limit)
            if df.empty:
                self.log.warning(f"{symbol} K线数据为空")
                return
            
            # 计算币龄 - 优先使用onboardDate，回退到K线数据
            age_days = 500  # 默认值
            current_time = pd.Timestamp.utcnow()
            if current_time.tz is None:
                current_time = current_time.tz_localize('UTC')
            
            try:
                # 优先使用缓存的onboardDate
                symbols_info = self.cache.load_symbols(self.trader)
                symbol_info = next((s for s in symbols_info if s['symbol'] == symbol), None)
                
                if symbol_info and 'age_days' in symbol_info:
                    age_days = symbol_info['age_days']
                    self.log.debug(f"{symbol} 使用缓存的币龄: {age_days}天")
                else:
                    # 回退到API获取onboardDate
                    symbol_info_api = self.trader.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
                    if (symbol_info_api and 'symbols' in symbol_info_api and 
                        len(symbol_info_api['symbols']) > 0 and 
                        'onboardDate' in symbol_info_api['symbols'][0]):
                        
                        onboard_timestamp = symbol_info_api['symbols'][0]['onboardDate']
                        launch_ts = pd.to_datetime(onboard_timestamp, unit='ms')
                        if launch_ts.tz is None:
                            launch_ts = launch_ts.tz_localize('UTC')
                        age_days = (current_time - launch_ts).days
                        self.log.debug(f"{symbol} 使用API的onboardDate计算币龄: {age_days}天")
                    else:
                        # 最后回退到K线数据
                        if not df.empty:
                            first_time = df.index[0]
                            if hasattr(first_time, 'tz') and first_time.tz is None:
                                first_time = first_time.tz_localize('UTC')
                            elif not hasattr(first_time, 'tz'):
                                first_time = pd.Timestamp(first_time).tz_localize('UTC')
                            
                            age_days = (current_time - first_time).days
                            self.log.debug(f"{symbol} 使用K线数据计算币龄: {age_days}天")
            except Exception as e:
                self.log.warning(f"{symbol} 币龄计算异常: {e}, 使用默认值: {age_days}天")
            
            # 重新评分
            score = self.score_symbol(df, age_days, symbol)
            M = 0  # M变量暂时设为默认值，后续检查是否需要
            
            # 更新缓存
            depth = self.get_depth01pct(symbol)
            symbol_data = {
                'symbol': symbol,
                'score': score,
                'M': M,
                'age': age_days,
                'depth': depth,
                'price': df['close'].iloc[-1] if not df.empty else 0,
                'timestamp': time.time()
            }
            
            self.cand_cache[symbol] = symbol_data
            
            # 如果评分≥min_score且未持仓，加入候选池
            min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
            if score >= min_score and symbol not in self.cache.load_positions():
                current_candidates = self.cache.load_candidates()
                if symbol not in current_candidates:
                    current_candidates.append(symbol)
                    self.cache.save_candidates(current_candidates)
            
            self.log.debug(f"{symbol} 更新完成: 评分={score}, M={M}, 币龄={age_days}天")
            
        except Exception as e:
            self.log.error(f"更新币种{symbol}失败: {e}")

    def manage_candidate_pool(self, max_candidates=50):
        """
        智能候选池管理：
        1. 动态容量管理（最大50个币种）
        2. 多层次淘汰机制
        3. 缓冲移除逻辑（连续3次不达标才移除）
        """
        current_time = time.time()
        
        # 为每个候选添加失败计数器（如果没有的话）
        for symbol in self.cand_cache:
            if 'fail_count' not in self.cand_cache[symbol]:
                self.cand_cache[symbol]['fail_count'] = 0
            if 'last_check' not in self.cand_cache[symbol]:
                self.cand_cache[symbol]['last_check'] = current_time
        
        # 检查并标记不达标的币种
        to_remove = []
        for symbol, data in list(self.cand_cache.items()):
            score = data.get('score', 0)
            
            # 检查是否达标（评分≥min_score分）
            min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
            if score < min_score:
                data['fail_count'] += 1
                data['last_check'] = current_time
                
                # 连续3次不达标才移除（缓冲机制）
                if data['fail_count'] >= 3:
                    to_remove.append(symbol)
                    self.log.info(f"移除候选池: {symbol} (连续{data['fail_count']}次评分不达标)")
            else:
                # 达标则重置失败计数
                data['fail_count'] = 0
        
        # 移除不达标的币种
        for symbol in to_remove:
            if symbol in self.cand_cache:
                del self.cand_cache[symbol]
        
        # 如果候选池超过容量限制，进行智能淘汰
        if len(self.cand_cache) > max_candidates:
            self.smart_candidate_elimination(max_candidates)
    
    def smart_candidate_elimination(self, target_size):
        """
        智能候选池淘汰：
        1. 优先保留持仓币种
        2. 考虑网络状态和数据质量
        3. 按综合评分排序
        4. 淘汰低分币种
        """
        try:
            # 记录清理前状态
            before_size = len(self.cand_cache)
            self.log.info(f"开始智能候选池清理，当前大小: {before_size}，目标大小: {target_size}")
            
            # 1. 优先保留有持仓的币种
            with_positions = []
            without_positions = []
            
            for symbol in list(self.cand_cache.keys()):
                if symbol in self.positions or (hasattr(self, 'pos') and self.pos and self.pos.get('symbol') == symbol):
                    with_positions.append(symbol)
                else:
                    without_positions.append(symbol)
            
            self.log.info(f"候选池中有持仓币种: {len(with_positions)}个，无持仓币种: {len(without_positions)}个")
            
            # 如果有持仓的币种数量已经超过目标大小，则不移除任何币种
            if len(with_positions) >= target_size:
                self.log.warning(f"有持仓币种数量({len(with_positions)})已超过目标大小({target_size})，不进行清理")
                return
                
            # 计算需要保留的无持仓币种数量
            keep_without_positions = target_size - len(with_positions)
            
            # 如果无需移除任何币种
            if keep_without_positions >= len(without_positions):
                self.log.info(f"无需移除任何币种，当前无持仓币种数量({len(without_positions)})未超过保留限制({keep_without_positions})")
                return
                
            # 2. 对无持仓币种进行多维度评分
            scored_candidates = []
            
            # 检查网络状态
            network_healthy = True
            network_latency = 0
            if hasattr(self, 'network_status') and self.network_status:
                network_healthy = self.network_status.get('connected', True)
                network_latency = self.network_status.get('latency', 0)
                
            if not network_healthy or network_latency > 2000:
                self.log.warning(f"网络状态异常，将减少币种移除数量。网络连接: {network_healthy}, 延迟: {network_latency}ms")
                # 网络状态不佳时，增加保留数量，减少移除
                keep_without_positions = min(len(without_positions), int(target_size * 1.2) - len(with_positions))
            
            for symbol in without_positions:
                data = self.cand_cache[symbol]
                
                # 持仓币种优先级最高
                priority_bonus = 0
                
                # 获取评分、深度和币龄
                score = data.get('score', 0)
                depth = data.get('depth', 0)
                age_days = data.get('age', 500)  # 默认为较老的币
                
                # 计算网络状态评分因子（如果近期有网络问题，提高保留概率）
                network_factor = 1.0
                if not network_healthy or network_latency > 2000:
                    network_factor = 1.2
                    
                # 计算失败次数因子（失败次数越多，保留概率越低）
                failure_factor = 1.0
                failure_count = data.get('fail_count', 0)
                if failure_count > 0:
                    failure_factor = max(0.5, 1.0 - (failure_count * 0.1))  # 每次失败降低10%，最低降至50%
                
                # 计算数据质量因子
                data_quality_factor = 1.0
                if data.get('data_quality', 'normal') == 'poor':
                    data_quality_factor = 1.2  # 如果数据质量差，提高保留概率
                
                # 归一化各项指标
                score_norm = min(score / 10, 1.0)  # 评分满分10分
                depth_norm = min(depth / 50000, 1.0)  # 深度满分5万美元
                age_norm = min(age_days / 365, 1.0)  # 币龄满分365天
                
                # 计算综合评分 (归一化处理)
                composite_score = (
                    score_norm * 0.4 +  # 评分权重
                    depth_norm * 0.35 +  # 深度权重
                    (1 - age_norm) * 0.25  # 币龄权重（新币优先）
                ) * network_factor * failure_factor * data_quality_factor + priority_bonus
                
                scored_candidates.append({
                    'symbol': symbol,
                    'composite_score': composite_score,
                    'score': score,
                    'depth': depth,
                    'age': age_days,
                    'network_factor': network_factor,
                    'failure_factor': failure_factor,
                    'data_quality_factor': data_quality_factor
                })
                
                # 添加详细日志，便于调试
                if hasattr(self, 'debug') and self.debug:
                    self.log.debug(f"币种评分详情 - {symbol}: 原始分数={score:.2f}, 深度={depth:.2f}, "
                                  f"币龄={age_days}天, 归一化分数={score_norm:.3f}, 归一化深度={depth_norm:.3f}, "
                                  f"归一化币龄={1-age_norm:.3f}, 网络因子={network_factor:.2f}, "
                                  f"失败因子={failure_factor:.2f}, 数据质量因子={data_quality_factor:.2f}, "
                                  f"综合评分={composite_score:.4f}")
            
            # 3. 按综合评分排序
            scored_candidates.sort(key=lambda x: x['composite_score'], reverse=True)
            
            # 4. 保留评分较高的币种
            keep_symbols = [item['symbol'] for item in scored_candidates[:keep_without_positions]] + with_positions
            
            # 5. 执行移除
            removed_symbols = []
            for symbol in list(self.cand_cache.keys()):
                if symbol not in keep_symbols:
                    removed_symbols.append({
                        'symbol': symbol,
                        'score': self.cand_cache[symbol].get('score', 0),
                        'depth': self.cand_cache[symbol].get('depth', 0),
                        'age': self.cand_cache[symbol].get('age', 0)
                    })
                    del self.cand_cache[symbol]
            
            if removed_symbols:
                # 按评分排序输出被移除的币种
                removed_symbols.sort(key=lambda x: x['score'], reverse=True)
                removed_info = ", ".join([f"{s['symbol']}(评分:{s['score']:.2f})" for s in removed_symbols[:5]])
                if len(removed_symbols) > 5:
                    removed_info += f" 等{len(removed_symbols)}个"
                    
                self.log.info(f"智能候选池清理完成，移除低分币种: {removed_info}")
                self.log.info(f"当前候选池大小: {len(self.cand_cache)}/{target_size}")
            
        except Exception as e:
            self.log.error(f"智能候选池清理失败: {e}")
            import traceback
            self.log.error(f"智能候选池清理错误详情: {traceback.format_exc()}")

    def select_top3(self, candidate_list):
        """
        对候选池进行排序：
        1. 总分降序
        2. 深度降序（0.1 % 深度）
        3. 币龄升序（新币优先）
        只留前 3 名，其余立即平仓（若持仓中）。
        """
        if not candidate_list:
            return []
            
        # 实时拉深度（缓存 5 min 内）
        scored_candidates = []
        for symbol in candidate_list:
            if symbol in self.cand_cache:
                data = self.cand_cache[symbol]
                # 获取最新深度
                depth = self.cache.load_depth(symbol)
                if depth is None:
                    depth = self.get_depth01pct(symbol)
                    self.cache.save_depth(symbol, depth)
                
                # 构建候选数据
                candidate_data = {
                    'symbol': symbol,
                    'score': data.get('score', 0),
                    'depth': depth,
                    'age_days': data.get('age', 500),
                    'price': data.get('price', 0)
                }
                scored_candidates.append(candidate_data)
        
        if not scored_candidates:
            return []
            
        # 排序键：总分 ↓ 深度 ↓ 币龄 ↑
        scored_candidates.sort(key=lambda x: (
            x['score'],
            x['depth'],
            -x['age_days']
        ), reverse=True)

        top3 = scored_candidates[:3]
        top3_symbols = {c['symbol'] for c in top3}

        # 持仓中但不在 top3 → 立即平仓
        if self.pos and self.pos.get('symbol') not in top3_symbols:
            symbol_to_close = self.pos['symbol']
            self.log.info(f"末位淘汰: {symbol_to_close} 不在前3名，立即平仓")
            # 市价平仓
            try:
                self.trader.open_position(symbol_to_close, 'SELL', self.pos['qty'], timeInForce='IOC')
                self.pos = None
                self.add_count = 0
                self.log.info(f"末位淘汰平仓完成: {symbol_to_close}")
            except Exception as e:
                self.log.error(f"末位淘汰平仓失败: {symbol_to_close}, 错误: {e}")

        return top3

    def parallel_update(self, symbols, interval='15m', limit=50):
        """并行更新候选池币种数据"""
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        if not symbols:
            self.log.warning("parallel_update: 传入的币种列表为空")
            return
            
        self.log.debug(f"开始并行更新 {len(symbols)} 个币种，间隔: {interval}, K线数量: {limit}")
        
        def _update_one(symbol):
            """单个币种的更新任务"""
            try:
                self.update_symbol(symbol, interval, limit)
                return symbol, True
            except Exception as e:
                self.log.error(f"并行更新币种 {symbol} 失败: {e}")
                return symbol, False
        
        # 使用线程池并行更新
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = {executor.submit(_update_one, symbol): symbol for symbol in symbols}
            
            success_count = 0
            for future in as_completed(futures):
                symbol = futures[future]
                try:
                    symbol_result, success = future.result()
                    if success:
                        success_count += 1
                except Exception as e:
                    self.log.error(f"并行更新币种 {symbol} 异常: {e}")
            
            self.log.info(f"并行更新完成: {success_count}/{len(symbols)} 个币种成功")

    def cold_start_symbols(self):
        """每日一次：仅拉取交易对精度等信息，不拉 K 线"""
        try:
            info = self.trader.http.get('/fapi/v1/exchangeInfo')
            if not info or 'symbols' not in info:
                return []
            symbols = [s for s in info['symbols']
                       if s['contractType'] == 'PERPETUAL'
                       and s['status'] == 'TRADING'
                       and s['symbol'].endswith('USDT')]
            # 只保存精度、上线日
            symbol_data = []
            for s in symbols:
                try:
                    # 计算币龄
                    age_days = 500
                    if 'onboardDate' in s:
                        onboard_timestamp = s['onboardDate']
                        launch_ts = pd.to_datetime(onboard_timestamp, unit='ms')
                        if launch_ts.tz is None:
                            launch_ts = launch_ts.tz_localize('UTC')
                        age_days = (pd.Timestamp.utcnow() - launch_ts).days
                    
                    symbol_data.append({
                        'symbol': s['symbol'],
                        'tick_size': float(s['filters'][0]['tickSize']),
                        'step_size': float(s['filters'][1]['stepSize']),
                        'min_qty': float(s['filters'][1]['minQty']),
                        'onboardDate': s.get('onboardDate', 0),
                        'age_days': age_days
                    })
                except Exception as e:
                    self.log.warning(f"处理交易对{s['symbol']}信息失败: {e}")
                    continue
            
            self.log.info(f"冷启动完成，共 {len(symbol_data)} 个交易对基础信息")
            return symbol_data
        except Exception as e:
            self.log.error(f"冷启动获取交易对信息失败: {e}")
            return []

    async def loop(self):
        """主循环"""
        try:
            self.log.info("策略主循环启动")
            
            # 启动预热
            self.warmup()
            
            loop_count = 0
            consecutive_errors = 0  # 连续错误计数
            max_consecutive_errors = 5  # 最大连续错误次数
            
            while True:
                try:
                    loop_start_time = time.time()
                    loop_count += 1
                    
                    # 每10个循环输出一次状态信息
                    if loop_count % 10 == 0:
                        self._log_status_info()
                    
                    # 使用策略优化器执行主循环逻辑
                    optimized_tasks = [
                        (self._async_first_score_batch, (), {}),
                        (self.enhanced_async_scoring, (), {}),  # 新增增强异步评分
                        (self.incremental_scan, (), {}),  # 新增增量扫描任务
                        (self._check_network_status, (), {}),
                        (self._manage_order_lifecycle, (), {}),
                        (self._process_retry_queue, (), {}),
                        (self._run_position_monitoring, (), {}),  # 新增持仓监控任务
                        (self._monitor_memory_usage, (), {})  # 新增内存监控任务
                    ]
                    
                    # 并行执行优化任务，增加超时控制
                    try:
                        results = await asyncio.wait_for(
                            self.strategy_optimizer.parallel_execute(optimized_tasks),
                            timeout=60  # 60秒超时
                        )
                        consecutive_errors = 0  # 重置错误计数
                    except asyncio.TimeoutError:
                        self.log.error("主循环任务执行超时，跳过本次循环")
                        consecutive_errors += 1
                        if consecutive_errors >= max_consecutive_errors:
                            self.log.error(f"连续{consecutive_errors}次错误，策略退出")
                            break
                        time.sleep(10)
                        continue
                    
                    # 记录性能指标
                    loop_duration = time.time() - loop_start_time
                    self.metrics_collector.record_order({'type': 'loop_performance', 'duration': loop_duration, 'status': 'success'})
                    
                    # 检查告警
                    alerts = self.metrics_collector.check_alerts()
                    if alerts:
                        for alert in alerts:
                            self.log.warning(f"监控告警: {alert}")
                    
                    time.sleep(1)
                    
                except Exception as e:
                    consecutive_errors += 1
                    self.log.error(f"主循环执行错误 (第{consecutive_errors}次): {e}")
                    # 记录错误指标
                    self.metrics_collector.record_order({'type': 'loop_error', 'error': str(e), 'status': 'failed'})
                    
                    # 如果连续错误过多，退出循环
                    if consecutive_errors >= max_consecutive_errors:
                        self.log.error(f"连续{consecutive_errors}次错误，策略退出")
                        break
                    
                    # 错误后等待更长时间
                    sleep_time = min(5 * consecutive_errors, 30)  # 最多等待30秒
                    self.log.info(f"等待{sleep_time}秒后重试...")
                    time.sleep(sleep_time)
                    
        except KeyboardInterrupt:
            self.log.info("收到停止信号，正在关闭...")
            self.metrics_collector.stop_monitoring()
            self.strategy_optimizer.stop()
        except Exception as e:
            self.log.error(f"主循环致命错误: {e}")
            self.metrics_collector.stop_monitoring()
            self.strategy_optimizer.stop()

    def _log_status_info(self):
        """输出状态信息"""
        try:
            # 获取配置的最低评分要求
            min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
            
            # 候选池状态
            candidate_count = len(self.cand_cache)
            high_score_count = sum(1 for data in self.cand_cache.values() if data.get('score', 0) >= min_score)
            
            # 持仓状态（从持仓监控器获取）
            position_summary = self.position_monitor.get_position_summary()
            position_count = position_summary['total_positions']
            total_pnl = position_summary['total_unrealized_pnl']
            avg_pnl_pct = position_summary['avg_pnl_pct']
            
            # 网络状态
            network_status = "正常" if self.network_status.get('connected', False) else "异常"
            latency = self.network_status.get('latency', 0)
            
            # 输出状态信息
            self.log.info(f"状态: 候选池 {candidate_count} 个币种 (高评分≥{min_score}: {high_score_count}), "
                         f"持仓 {position_count} 个 (总盈亏: {total_pnl:.2f} USDT, 平均: {avg_pnl_pct:.2%}), "
                         f"网络 {network_status} (延迟:{latency:.1f}ms)")
            
            # 输出候选池详细评分情况
            if candidate_count > 0:
                scored_symbols = []
                unscored_symbols = []
                
                for symbol, data in self.cand_cache.items():
                    score = data.get('score', None)
                    if score is not None:
                        scored_symbols.append((symbol, score))
                    else:
                        unscored_symbols.append(symbol)
                
                # 按评分降序排列已评分币种
                scored_symbols.sort(key=lambda x: x[1], reverse=True)
                
                # 输出已评分币种详情
                if scored_symbols:
                    self.log.info(f"已评分币种 ({len(scored_symbols)}个):")
                    for symbol, score in scored_symbols:
                        channel_pos = self._get_channel_position_display(symbol)
                        self.log.info(f"  {symbol}: 评分={score}分, 通道位置={channel_pos}")
                
                # 输出未评分币种
                if unscored_symbols:
                    self.log.info(f"未评分币种 ({len(unscored_symbols)}个): {', '.join(unscored_symbols)}")
            
            # 如果有持仓，输出详细信息
            if position_count > 0:
                for pos_info in position_summary['positions']:
                    self.log.info(f"  持仓: {pos_info['symbol']} {pos_info['side']} "
                                f"盈亏: {pos_info['unrealized_pnl_pct']:.2%} "
                                f"({'已保本' if pos_info['is_breakeven_adjusted'] else '未保本'}) "
                                f"({'移动止损' if pos_info['has_trailing_stop'] else '固定止损'})")
            
        except Exception as e:
            self.log.error(f"状态信息输出失败: {e}")

    def _get_channel_position_display(self, symbol):
        """获取币种在通道中的位置（用于显示）"""
        try:
            # 获取币龄
            age_minutes = self.get_symbol_age_minutes(symbol)
            if age_minutes is None:
                return "未知"
            
            age_days = age_minutes / (60 * 24)
            
            # 获取K线数据
            tf = self.dynamic_tf(age_days)
            interval = f'{tf}m'
            df = self.get_klines(symbol, interval, 50)
            
            if df is None or len(df) < 20:
                return "数据不足"
            
            # 计算通道参数
            n = dynamic_tf_for_channel(age_days)
            if len(df) < n:
                return "数据不足"
            
            # 计算通道上下轨
            recent_data = df.tail(n)
            upper_band = recent_data['high'].max()
            lower_band = recent_data['low'].min()
            middle_band = (upper_band + lower_band) / 2
            
            current_price = df.iloc[-1]['close']
            
            # 判断位置
            upper_threshold = middle_band + (upper_band - middle_band) * 0.7
            lower_threshold = middle_band - (middle_band - lower_band) * 0.7
            
            if current_price >= upper_threshold:
                return f"上轨区域({current_price:.6f})"
            elif current_price <= lower_threshold:
                return f"下轨区域({current_price:.6f})"
            else:
                return f"中轨区域({current_price:.6f})"
                
        except Exception as e:
            self.log.error(f"获取{symbol}通道位置失败: {e}")
            return "计算失败"

    def _run_position_monitoring(self):
        """执行持仓监控"""
        try:
            # 运行持仓监控周期
            results = self.position_monitor.run_monitoring_cycle()
            
            # 记录监控结果
            if results['breakeven_adjusted']:
                for symbol in results['breakeven_adjusted']:
                    self.log.info(f"保本止损调整: {symbol}")
                    self.metrics_collector.record_order({
                        'type': 'breakeven_adjustment', 
                        'symbol': symbol, 
                        'status': 'success'
                    })
            
            if results['trailing_stop_updated']:
                for symbol in results['trailing_stop_updated']:
                    self.log.info(f"移动止损更新: {symbol}")
                    self.metrics_collector.record_order({
                        'type': 'trailing_stop_update', 
                        'symbol': symbol, 
                        'status': 'success'
                    })
            
            if results['eliminated']:
                for symbol in results['eliminated']:
                    self.log.warning(f"末位淘汰执行: {symbol}")
                    self.metrics_collector.record_order({
                        'type': 'elimination', 
                        'symbol': symbol, 
                        'status': 'success'
                    })
            
            # 记录错误
            for error in results.get('errors', []):
                self.log.error(f"持仓监控错误: {error}")
                self.metrics_collector.record_order({
                    'type': 'position_monitoring_error', 
                    'error': error, 
                    'status': 'failed'
                })
                
        except Exception as e:
            self.log.error(f"持仓监控执行失败: {e}")
            self.metrics_collector.record_order({
                'type': 'position_monitoring_error', 
                'error': str(e), 
                'status': 'failed'
            })

    def _manage_order_lifecycle(self):
        """管理订单生命周期"""
        try:
            current_time = time.time()
            expired_orders = []
            
            # 管理主策略的订单
            for order_id, order_info in self.active_orders.items():
                if current_time - order_info['create_time'] > order_info['ttl']:
                    expired_orders.append(order_id)
            
            for order_id in expired_orders:
                order_info = self.active_orders.pop(order_id)
                symbol = order_info.get('symbol', 'unknown')
                self.log.info(f"订单 {order_id} 已过期，移除跟踪")
                
                # 记录监控指标
                self.metrics_collector.record_order({'type': 'order_expired', 'symbol': symbol, 'order_id': order_id, 'status': 'expired'})
            
            # 管理回调确认模块的限价单
            if hasattr(self, 'pullback_confirmation'):
                self.pullback_confirmation.manage_limit_orders()
                
        except Exception as e:
            self.log.error(f"订单生命周期管理失败: {e}")

    def _process_retry_queue(self):
        """处理重试队列"""
        # 这里可以添加重试队列处理逻辑
        pass

    def _check_network_status(self):
        """检查网络状态"""
        try:
            current_time = time.time()
            if current_time - self.last_network_check < self.network_check_interval:
                return self.network_status
            
            # 增加代理连接测试
            try:
                # 先测试代理服务器本身是否可用
                import requests
                proxy_url = "http://127.0.0.1:7897"
                proxies = {
                    'http': proxy_url,
                    'https': proxy_url
                }
                
                proxy_test_response = requests.get(
                    "http://www.google.com",
                    proxies=proxies,
                    timeout=5,
                    verify=False
                )
                if proxy_test_response.status_code != 200:
                    self.network_status = {
                        'connected': False,
                        'binance_api_ok': False,
                        'latency': 0,
                        'last_error': '代理服务器连接测试失败'
                    }
                    self.log.error("代理服务器连接测试失败")
                    return self.network_status
            except:
                self.network_status = {
                    'connected': False,
                    'binance_api_ok': False,
                    'latency': 0,
                    'last_error': '代理服务器不可用'
                }
                self.log.error("代理服务器不可用")
                return self.network_status
            
            # 简单的网络检查
            start_time = time.time()
            response = self.trader.http.get('/fapi/v1/ping')
            latency = (time.time() - start_time) * 1000
            
            if response is not None:
                self.network_status = {
                    'connected': True,
                    'binance_api_ok': True,
                    'latency': latency,
                    'last_error': None
                }
                # 记录API调用成功
                self.metrics_collector.record_api_call(success=True, response_time=latency/1000)
            else:
                self.network_status = {
                    'connected': False,
                    'binance_api_ok': False,
                    'latency': 0,
                    'last_error': 'Ping failed'
                }
                # 记录API调用失败
                self.metrics_collector.record_api_call(success=False)
            
            self.last_network_check = current_time
            return self.network_status
            
        except Exception as e:
            self.network_status = {
                'connected': False,
                'binance_api_ok': False,
                'latency': 0,
                'last_error': str(e)
            }
            self.log.error(f"网络状态检查失败: {e}")
            return self.network_status

    def get_symbol_age_minutes(self, symbol):
        """获取币种上线时间（分钟）"""
        self.log.debug(f"获取{symbol}的币龄信息...")
        
        try:
            # 首先尝试从symbols_info获取onboardDate
            if hasattr(self, 'symbols_info') and self.symbols_info:
                for info in self.symbols_info:
                    if info.get('symbol') == symbol:
                        onboard_date = info.get('onboardDate')
                        if onboard_date:
                            # onboardDate是毫秒时间戳
                            onboard_time = pd.to_datetime(onboard_date, unit='ms')
                            current_time = pd.Timestamp.utcnow()
                            age_minutes = (current_time - onboard_time).total_seconds() / 60
                            age_days = age_minutes / (24 * 60)
                            self.log.debug(f"{symbol}: 从symbols_info获取币龄 {age_days:.1f} 天")
                            return max(1, age_minutes)  # 至少1分钟
            
            # 如果没有symbols_info，尝试通过K线数据推算
            try:
                # 获取1天K线数据，尽可能多的数据
                df = self.get_klines(symbol, '1d', 1000)
                if df is not None and len(df) > 0:
                    # 第一根K线的时间作为上线时间
                    first_time = df.index[0]
                    current_time = pd.Timestamp.utcnow()
                    if hasattr(first_time, 'tz') and first_time.tz is None:
                        first_time = first_time.tz_localize('UTC')
                    if current_time.tz is None:
                        current_time = current_time.tz_localize('UTC')
                    
                    age_minutes = (current_time - first_time).total_seconds() / 60
                    age_days = age_minutes / (24 * 60)
                    self.log.debug(f"{symbol}: 从K线数据推算币龄 {age_days:.1f} 天")
                    return max(1, age_minutes)  # 至少1分钟
                else:
                    self.log.debug(f"{symbol}: 无法获取K线数据来推算币龄")
            except Exception as e:
                self.log.debug(f"通过K线推算{symbol}币龄失败: {e}")
            
            # 如果都失败了，返回默认值500天（避免所有币种都是30天）
            self.log.debug(f"{symbol}: 使用默认币龄 500 天")
            return 60 * 24 * 500  # 500天作为默认值
            
        except Exception as e:
            self.log.error(f"获取{symbol}币龄失败: {e}")
            return 60 * 24 * 500  # 500天作为默认值

    def dynamic_tf(self, age_days):
        """动态时间框架"""
        # age_days 参数是以天为单位的浮点数
        if 7 <= age_days < 365:              # 7天-365天
            return 3  # 修复：2m无效，改为3m
        elif age_days >= 365:                # >365天
            return 1
        elif 0.25 <= age_days < 7:           # 6小时-7天（新币也满分）
            return 15
        else:                                # <6小时
            return 30

    def _execute_opening_logic(self):
        """执行开仓逻辑：选择最佳币种进行开仓"""
        try:
            # 检查是否已有持仓
            if hasattr(self, 'position_monitor') and self.position_monitor.get_position_summary()['total_positions'] > 0:
                self.log.debug("已有持仓，跳过开仓逻辑")
                return
            
            # 获取所有达标币种（评分≥min_score分）
            qualified_candidates = []
            min_score = self.channel_config.get('candidate_selection', {}).get('min_score', 7)
            for symbol, data in self.cand_cache.items():
                score = data.get('score', 0)
                if score >= min_score:
                    # 获取深度数据
                    depth = self.get_depth01pct(symbol)
                    age_days = data.get('age_days', self.get_symbol_age_minutes(symbol) / (60 * 24))
                    
                    qualified_candidates.append({
                        'symbol': symbol,
                        'score': score,
                        'depth': depth,
                        'age_days': age_days
                    })
            
            if not qualified_candidates:
                self.log.info(f"没有达标币种（评分≥{min_score}分），跳过开仓")
                return
            
            # 按评分、深度、币龄排序选择最佳币种
            qualified_candidates.sort(key=lambda x: (x['score'], x['depth'], -x['age_days']), reverse=True)
            
            best_candidate = qualified_candidates[0]
            best_symbol = best_candidate['symbol']
            
            self.log.debug(f"选择最佳币种开仓: {best_symbol} (评分: {best_candidate['score']:.2f}, 深度: {best_candidate['depth']:.0f}$, 币龄: {best_candidate['age_days']:.1f}天)")
            
            # 执行开仓
            self._execute_single_opening(best_symbol, best_candidate)
            
        except Exception as e:
            self.log.error(f"执行开仓逻辑失败: {e}")

    def _execute_single_opening(self, symbol, candidate_data):
        """执行单个币种的开仓操作"""
        try:
            # 获取最新K线数据确认通道位置
            age_days = candidate_data['age_days']
            tf = self.dynamic_tf(age_days)
            interval = f'{tf}m'
            df = self.get_klines(symbol, interval, 200)
            
            if df is None or len(df) < 50:
                self.log.warning(f"{symbol}数据不足，跳过开仓")
                return
            
            # 再次确认通道突破
            if not self.check_channel_breakthrough(symbol, df, age_days):
                self.log.warning(f"{symbol}不满足通道突破条件，跳过开仓")
                return
            
            # 获取通道位置
            channel_pos = self._get_channel_position(symbol)
            
            # 计算开仓参数
            current_price = df['close'].iloc[-1]
            
            # 策略只做多，所有开仓都是BUY
            direction = "BUY"
            self.log.info(f"{symbol}通道突破确认，准备执行做多开仓，价格: {current_price}，通道位置: {channel_pos}")
            
            # 动态计算开仓数量
            quantity = self._calculate_position_size(symbol, current_price)
            if quantity <= 0:
                self.log.warning(f"{symbol}计算的开仓数量为0或负数，跳过开仓")
                return
            
            # 使用回踩确认模块等待回踩并执行开仓
            self.log.info(f"{symbol}启动回踩确认机制，等待0.38%回踩")
            result = self.pullback_confirmation.wait_pullback(
                symbol=symbol,
                direction=direction,
                quantity=quantity,
                current_price=current_price
            )
            
            if result and result.get('success'):
                self.log.info(f"开仓成功: {symbol} {direction} {quantity:.6f} @ {result.get('fill_price', current_price)}")
                
                # 立即设置止损止盈
                self._set_stop_loss_take_profit(symbol, direction, quantity, result.get('fill_price', current_price))
                
                # 记录监控指标
                if hasattr(self, 'metrics_collector'):
                    self.metrics_collector.record_order({
                        'type': 'open_position',
                        'symbol': symbol,
                        'side': direction,
                        'quantity': quantity,
                        'price': result.get('fill_price', current_price),
                        'status': 'success',
                        'entry_type': result.get('entry_type', 'unknown')
                    })
            else:
                self.log.warning(f"{symbol}回踩确认失败或超时，跳过开仓")
                
        except Exception as e:
            self.log.error(f"执行{symbol}开仓失败: {e}")

    def _calculate_position_size(self, symbol, current_price):
        """计算开仓数量"""
        try:
            # 获取账户余额
            account_balance = 0
            if hasattr(self.trader, 'get_total_balance'):
                account_balance = self.trader.get_total_balance()
            else:
                self.log.warning("无法获取账户余额，使用配置的默认值")
                account_balance = 1000  # 默认值
            
            # 获取杠杆配置
            leverage = getattr(self.trader, 'leverage_config', {}).get('target_leverage', 3)
            
            # 使用配置的首单名义金额
            target_position_usd = self.original_nominal
            
            # 计算所需保证金（考虑杠杆）
            required_margin = target_position_usd / leverage
            
            # 安全系数，留出缓冲
            safety_factor = 0.8
            max_usable_balance = account_balance * safety_factor
            
            # 检查余额是否充足
            if required_margin > max_usable_balance:
                # 余额不足，按比例缩小仓位
                adjusted_position_usd = max_usable_balance * leverage
                self.log.warning(f"{symbol}余额不足，目标仓位{target_position_usd}USDT，"
                               f"可用余额{account_balance:.2f}USDT，调整为{adjusted_position_usd:.2f}USDT")
                target_position_usd = adjusted_position_usd
            
            # 应用最小和最大仓位限制
            min_position_usd = 10  # 最小仓位
            max_position_usd = 1000  # 最大仓位
            
            if target_position_usd < min_position_usd:
                self.log.warning(f"{symbol}计算的仓位{target_position_usd:.2f}USDT小于最小限制{min_position_usd}USDT，跳过开仓")
                return 0
            
            target_position_usd = min(target_position_usd, max_position_usd)
            
            # 计算数量
            quantity = target_position_usd / current_price
            
            self.log.info(f"{symbol}仓位计算: 账户余额{account_balance:.2f}USDT, "
                         f"杠杆{leverage}x, 目标仓位{target_position_usd:.2f}USDT, "
                         f"所需保证金{target_position_usd/leverage:.2f}USDT, "
                         f"开仓数量{quantity:.6f}")
            
            return quantity
            
        except Exception as e:
            self.log.error(f"计算{symbol}开仓数量失败: {e}")
            return 0

    def _get_channel_position(self, symbol):
        """获取币种的通道位置"""
        try:
            # 从缓存中获取或重新计算
            if symbol in self.cand_cache:
                data = self.cand_cache[symbol]
                age_days = data.get('age_days', self.get_symbol_age_minutes(symbol) / (60 * 24))
                
                tf = self.dynamic_tf(age_days)
                interval = f'{tf}m'
                df = self.get_klines(symbol, interval, 200)
                
                if df is not None and len(df) >= 50:
                    # 计算通道位置
                    tf_minutes = self.dynamic_tf(age_days)  # 返回整数分钟数
                    n = max(2, int(tf_minutes))  # 直接使用分钟数作为K线数量
                    
                    high_col = 'high' if 'high' in df.columns else 'h'
                    low_col = 'low' if 'low' in df.columns else 'l'
                    close_col = 'close' if 'close' in df.columns else 'c'
                    
                    upper_band = df[high_col].rolling(n).max().iloc[-1]
                    lower_band = df[low_col].rolling(n).min().iloc[-1]
                    current_price = df[close_col].iloc[-1]
                    
                    # 判断位置
                    channel_range = upper_band - lower_band
                    upper_threshold = upper_band - channel_range * 0.1  # 上轨区域
                    lower_threshold = lower_band + channel_range * 0.1  # 下轨区域
                    
                    if current_price >= upper_threshold:
                        return "上轨区域"
                    elif current_price <= lower_threshold:
                        return "下轨区域"
                    else:
                        return "中轨区域"
            
            return "未知区域"
            
        except Exception as e:
            self.log.error(f"获取{symbol}通道位置失败: {e}")
            return "未知区域"

    def _set_stop_loss_take_profit(self, symbol, direction, quantity, entry_price):
        """开仓后立即设置止损止盈"""
        try:
            # 计算止损价格（-3%基础止损）
            if direction == "BUY":
                stop_loss_price = entry_price * 0.97  # 多头止损价格
                take_profit_price = entry_price * 1.60  # 多头止盈价格（60%）
            else:
                stop_loss_price = entry_price * 1.03  # 空头止损价格
                take_profit_price = entry_price * 0.40  # 空头止盈价格（60%）
            
            # 设置止损单
            if hasattr(self.trader, 'place_stop_loss_order'):
                stop_result = self.trader.place_stop_loss_order(
                    symbol=symbol,
                    quantity=quantity,
                    stop_price=stop_loss_price
                )
                if stop_result:
                    self.log.info(f"{symbol}止损单设置成功: {stop_loss_price:.6f} (-3%)")
                else:
                    self.log.error(f"{symbol}止损单设置失败")
            
            # 设置止盈单（部分平仓30%）
            if hasattr(self.trader, 'place_take_profit_order'):
                take_profit_quantity = quantity * 0.3  # 30%仓位止盈
                take_profit_result = self.trader.place_take_profit_order(
                    symbol=symbol,
                    quantity=take_profit_quantity,
                    take_price=take_profit_price
                )
                if take_profit_result:
                    self.log.info(f"{symbol}止盈单设置成功: {take_profit_price:.6f} (+60%, 平仓30%)")
                else:
                    self.log.error(f"{symbol}止盈单设置失败")
                    
        except Exception as e:
            self.log.error(f"{symbol}设置止损止盈失败: {e}")
    
    def _should_skip_failed_symbol(self, symbol):
        """检查是否应该跳过失败的币种"""
        if symbol not in self.failed_symbols:
            return False
        
        failure_info = self.failed_symbols[symbol]
        current_time = time.time()
        
        # 检查失败次数是否超过限制
        if failure_info['count'] >= self.max_symbol_failures:
            # 根据错误类型选择不同的冷却时间
            if failure_info['error_type'] == 'network':
                cooldown = self.network_error_cooldown
            else:
                cooldown = self.failure_cooldown
            
            # 检查是否还在冷却期内
            if current_time - failure_info['last_fail_time'] < cooldown:
                return True
            else:
                # 冷却期结束，重置失败计数
                self.failed_symbols[symbol] = {
                    'count': 0,
                    'last_fail_time': current_time,
                    'error_type': failure_info['error_type']
                }
                return False
        
        return False
    
    def _record_symbol_failure(self, symbol, error):
        """记录币种失败信息"""
        current_time = time.time()
        error_msg = str(error).lower()
        
        # 判断错误类型
        is_network_error = any(keyword in error_msg for keyword in 
                              ['connection', 'timeout', 'network', 'proxy', 'ssl', 'api'])
        error_type = 'network' if is_network_error else 'data'
        
        if symbol in self.failed_symbols:
            self.failed_symbols[symbol]['count'] += 1
            self.failed_symbols[symbol]['last_fail_time'] = current_time
            self.failed_symbols[symbol]['error_type'] = error_type
        else:
            self.failed_symbols[symbol] = {
                'count': 1,
                'last_fail_time': current_time,
                'error_type': error_type
            }
        
        # 记录日志
        failure_info = self.failed_symbols[symbol]
        if failure_info['count'] >= self.max_symbol_failures:
            cooldown_minutes = (self.network_error_cooldown if error_type == 'network' 
                               else self.failure_cooldown) / 60
            self.log.warning(f"币种 {symbol} 失败次数达到上限({failure_info['count']})，"
                           f"将冷却 {cooldown_minutes:.0f} 分钟")
    
    def _cleanup_failed_symbols(self):
        """清理过期的失败记录"""
        current_time = time.time()
        expired_symbols = []
        
        for symbol, failure_info in self.failed_symbols.items():
            # 如果冷却期已过且失败次数已重置，则清理记录
            if failure_info['count'] == 0:
                cooldown = (self.network_error_cooldown if failure_info['error_type'] == 'network' 
                           else self.failure_cooldown)
                if current_time - failure_info['last_fail_time'] > cooldown * 2:  # 双倍冷却期后清理
                    expired_symbols.append(symbol)
        
        for symbol in expired_symbols:
            del self.failed_symbols[symbol]
        
        if expired_symbols:
            self.log.debug(f"清理了 {len(expired_symbols)} 个过期失败记录")
    
    def _monitor_memory_usage(self):
        """监控内存使用情况并执行清理"""
        try:
            current_time = time.time()
            
            # 检查是否需要进行内存检查
            if current_time - self.last_memory_check < self.memory_check_interval:
                return
            
            # 获取当前内存使用情况
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # 更新内存统计
            self.memory_stats['current_memory'] = memory_mb
            self.memory_stats['peak_memory'] = max(self.memory_stats['peak_memory'], memory_mb)
            self.last_memory_check = current_time
            
            # 检查是否超过内存阈值
            if memory_mb > self.max_memory_usage:
                self.log.warning(f"内存使用超过阈值: {memory_mb:.1f}MB > {self.max_memory_usage}MB，开始清理")
                self._perform_memory_cleanup()
            
            # 定期执行垃圾回收
            if current_time - self.last_gc_collection > self.gc_collection_interval:
                collected = gc.collect()
                self.last_gc_collection = current_time
                if collected > 0:
                    self.log.debug(f"垃圾回收清理了 {collected} 个对象")
            
            # 记录内存使用情况（每10分钟记录一次）
            if current_time - self.memory_stats.get('last_log_time', 0) > 600:
                self.log.info(f"内存使用情况: 当前 {memory_mb:.1f}MB, 峰值 {self.memory_stats['peak_memory']:.1f}MB")
                self.memory_stats['last_log_time'] = current_time
                
        except Exception as e:
            self.log.error(f"内存监控异常: {e}")
    
    def _perform_memory_cleanup(self):
        """执行内存清理操作"""
        try:
            cleanup_count = 0
            
            # 1. 清理缓存管理器
            if hasattr(self.cache, 'cleanup_expired'):
                expired_count = self.cache.cleanup_expired()
                cleanup_count += expired_count
                self.log.debug(f"清理过期缓存: {expired_count} 项")
            
            # 2. 清理候选池缓存
            if len(self.cand_cache) > self.max_cache_size:
                # 保留最近使用的缓存项
                sorted_items = sorted(self.cand_cache.items(), 
                                    key=lambda x: x[1].get('last_access', 0), 
                                    reverse=True)
                keep_items = dict(sorted_items[:self.max_cache_size // 2])
                removed_count = len(self.cand_cache) - len(keep_items)
                self.cand_cache = keep_items
                cleanup_count += removed_count
                self.log.debug(f"清理候选池缓存: {removed_count} 项")
            
            # 3. 清理失败符号记录
            self._cleanup_failed_symbols()
            
            # 4. 清理过期的网络状态记录
            current_time = time.time()
            if hasattr(self, 'network_status_cache'):
                expired_keys = [k for k, v in self.network_status_cache.items() 
                              if current_time - v.get('timestamp', 0) > 3600]  # 1小时过期
                for key in expired_keys:
                    del self.network_status_cache[key]
                cleanup_count += len(expired_keys)
            
            # 5. 强制垃圾回收
            collected = gc.collect()
            
            # 6. 获取清理后的内存使用情况
            process = psutil.Process()
            new_memory_mb = process.memory_info().rss / 1024 / 1024
            memory_saved = self.memory_stats['current_memory'] - new_memory_mb
            
            self.log.info(f"内存清理完成: 清理项目 {cleanup_count}, 垃圾回收 {collected}, "
                         f"节省内存 {memory_saved:.1f}MB, 当前内存 {new_memory_mb:.1f}MB")
            
            # 更新内存统计
            self.memory_stats['current_memory'] = new_memory_mb
            self.memory_stats['cleanup_count'] += 1
            
        except Exception as e:
            self.log.error(f"内存清理异常: {e}")
    
    def _optimize_data_structures(self):
        """优化数据结构的内存占用"""
        try:
            # 1. 优化DataFrame内存使用
            for symbol, data in self.cand_cache.items():
                if 'df' in data and isinstance(data['df'], pd.DataFrame):
                    # 转换数据类型以节省内存
                    df = data['df']
                    for col in df.select_dtypes(include=['float64']).columns:
                        df[col] = pd.to_numeric(df[col], downcast='float')
                    for col in df.select_dtypes(include=['int64']).columns:
                        df[col] = pd.to_numeric(df[col], downcast='integer')
            
            # 2. 清理不必要的中间变量
            if hasattr(self, '_temp_calculations'):
                self._temp_calculations.clear()
            
            self.log.debug("数据结构内存优化完成")
            
        except Exception as e:
            self.log.error(f"数据结构优化异常: {e}")
    
    def get_memory_stats(self):
        """获取内存使用统计"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'current_memory_mb': memory_info.rss / 1024 / 1024,
                'peak_memory_mb': self.memory_stats['peak_memory'],
                'cache_size': len(self.cand_cache),
                'failed_symbols_count': len(self.failed_symbols),
                'cleanup_count': self.memory_stats['cleanup_count']
            }
        except Exception as e:
            self.log.error(f"获取内存统计异常: {e}")
            return {}