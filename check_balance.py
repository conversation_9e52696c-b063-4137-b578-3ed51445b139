#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from binance_trader import BinanceTrader
from config_manager import ConfigManager
from strategy.maker_channel import MakerChannelStrategy
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_position_calculation():
    """测试仓位计算逻辑"""
    try:
        # 直接加载yaml配置文件
        import yaml
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 初始化交易器
        trader = BinanceTrader(config)
        
        # 初始化策略
        strategy = MakerChannelStrategy(trader, config)
        
        # 获取账户信息
        account_balance = trader.get_total_balance()
        print(f"=== 账户信息 ===")
        print(f"总余额: {account_balance} USDT")
        
        # 测试ENAUSDT的仓位计算
        symbol = "ENAUSDT"
        ticker = trader.get_symbol_ticker(symbol)
        if ticker and 'price' in ticker:
            current_price = float(ticker['price'])
            print(f"\n=== {symbol} 价格信息 ===")
            print(f"当前价格: {current_price}")
            
            # 使用策略的仓位计算方法
            quantity = strategy._calculate_position_size(symbol, current_price)
            position_value = quantity * current_price
            
            print(f"\n=== 策略仓位计算结果 ===")
            print(f"开仓数量: {quantity:.6f}")
            print(f"开仓金额: {position_value:.2f} USDT")
            
            # 获取杠杆配置
            leverage = getattr(trader, 'leverage_config', {}).get('target_leverage', 3)
            required_margin = position_value / leverage
            
            print(f"\n=== 杠杆和保证金信息 ===")
            print(f"配置杠杆: {leverage}x")
            print(f"所需保证金: {required_margin:.2f} USDT")
            
            if required_margin <= account_balance:
                print(f"✅ 余额充足，可以开仓")
            else:
                shortage = required_margin - account_balance
                print(f"❌ 余额不足，缺少: {shortage:.2f} USDT")
        else:
            print(f"无法获取{symbol}价格信息")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_position_calculation()