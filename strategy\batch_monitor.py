"""
批次处理监控和优化系统
解决批次扫描异常重启和批次混乱问题
"""

import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, field
import logging
import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from batch_config import BatchConfig, DEFAULT_CONFIG

@dataclass
class BatchProgress:
    """批次进度信息"""
    batch_id: str
    batch_index: int
    total_batches: int
    symbols_in_batch: List[str]
    start_time: float
    end_time: Optional[float] = None
    status: str = "pending"  # pending, processing, completed, failed
    processed_symbols: Set[str] = None
    failed_symbols: Set[str] = None
    
    def __post_init__(self):
        if self.processed_symbols is None:
            self.processed_symbols = set()
        if self.failed_symbols is None:
            self.failed_symbols = set()

@dataclass
class ScanCycle:
    """扫描周期信息"""
    cycle_id: str
    start_time: float
    end_time: Optional[float] = None
    total_symbols: int = 0
    processed_symbols: int = 0
    qualified_symbols: int = 0
    batches: List[BatchProgress] = None
    status: str = "active"  # active, completed, failed
    
    def __post_init__(self):
        if self.batches is None:
            self.batches = []

class BatchMonitor:
    """批次处理监控器"""
    
    def __init__(self, config: BatchConfig = None):
        self.config = config or DEFAULT_CONFIG
        self.log = logging.getLogger("BatchMonitor")
        
        # 验证配置
        if not self.config.validate():
            raise ValueError("批次配置验证失败")
        
        # 监控状态
        self.current_cycle: Optional[ScanCycle] = None
        self.scan_history: List[ScanCycle] = []
        self.processed_symbols_global: Set[str] = set()
        self.duplicate_scans: Dict[str, int] = {}
        
        # 配置参数
        self.batch_size = self.config.batch_size
        self.max_scan_interval = self.config.scan_interval
        self.cycle_timeout = self.config.max_cycle_time
        
        # 监控锁
        self.lock = threading.Lock()
        
        # 启动监控线程
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.log.info(f"批次监控系统已启动 - 配置: {self.config.batch_size}个币种/批次")
        
        # 打印配置摘要
        self.config.print_summary()

    def start_scan_cycle(self, symbols: List[str]) -> str:
        """开始新的扫描周期"""
        with self.lock:
            # 检查是否有未完成的周期
            if self.current_cycle and self.current_cycle.status == "active":
                self.log.warning(f"检测到未完成的扫描周期: {self.current_cycle.cycle_id}")
                self._force_complete_cycle("interrupted")
            
            # 创建新周期
            cycle_id = f"cycle_{int(time.time())}"
            total_batches = (len(symbols) + self.batch_size - 1) // self.batch_size
            
            self.current_cycle = ScanCycle(
                cycle_id=cycle_id,
                start_time=time.time(),
                total_symbols=len(symbols),
                batches=[]
            )
            
            # 预创建所有批次
            for i in range(total_batches):
                start_idx = i * self.batch_size
                end_idx = min(start_idx + self.batch_size, len(symbols))
                batch_symbols = symbols[start_idx:end_idx]
                
                batch = BatchProgress(
                    batch_id=f"{cycle_id}_batch_{i+1}",
                    batch_index=i,
                    total_batches=total_batches,
                    symbols_in_batch=batch_symbols,
                    start_time=0  # 将在实际开始时设置
                )
                self.current_cycle.batches.append(batch)
            
            self.log.info(f"开始扫描周期 {cycle_id}: {len(symbols)} 个币种, {total_batches} 个批次")
            return cycle_id

    def start_batch(self, batch_index: int) -> Optional[str]:
        """开始处理批次"""
        with self.lock:
            if not self.current_cycle:
                self.log.error("没有活跃的扫描周期")
                return None
            
            if batch_index >= len(self.current_cycle.batches):
                self.log.error(f"批次索引超出范围: {batch_index}")
                return None
            
            batch = self.current_cycle.batches[batch_index]
            
            # 检查批次状态
            if batch.status != "pending":
                self.log.warning(f"批次 {batch.batch_id} 状态异常: {batch.status}")
                return None
            
            # 更新批次状态
            batch.status = "processing"
            batch.start_time = time.time()
            
            # 检查重复扫描
            for symbol in batch.symbols_in_batch:
                if symbol in self.processed_symbols_global:
                    self.duplicate_scans[symbol] = self.duplicate_scans.get(symbol, 0) + 1
                    self.log.warning(f"检测到重复扫描: {symbol} (第{self.duplicate_scans[symbol]+1}次)")
            
            self.log.info(f"开始批次 {batch.batch_id}: 处理 {len(batch.symbols_in_batch)} 个币种")
            return batch.batch_id

    def complete_batch(self, batch_index: int, processed_symbols: List[str], 
                      failed_symbols: List[str] = None):
        """完成批次处理"""
        with self.lock:
            if not self.current_cycle:
                return
            
            if batch_index >= len(self.current_cycle.batches):
                return
            
            batch = self.current_cycle.batches[batch_index]
            
            # 更新批次状态
            batch.status = "completed"
            batch.end_time = time.time()
            batch.processed_symbols = set(processed_symbols)
            batch.failed_symbols = set(failed_symbols or [])
            
            # 更新全局处理记录
            self.processed_symbols_global.update(processed_symbols)
            
            # 更新周期统计
            self.current_cycle.processed_symbols += len(processed_symbols)
            
            processing_time = batch.end_time - batch.start_time
            self.log.info(f"完成批次 {batch.batch_id}: 处理 {len(processed_symbols)} 个币种, "
                         f"耗时 {processing_time:.1f}秒")
            
            # 检查是否完成整个周期
            self._check_cycle_completion()

    def fail_batch(self, batch_index: int, error: str):
        """标记批次失败"""
        with self.lock:
            if not self.current_cycle:
                return
            
            if batch_index >= len(self.current_cycle.batches):
                return
            
            batch = self.current_cycle.batches[batch_index]
            batch.status = "failed"
            batch.end_time = time.time()
            
            self.log.error(f"批次 {batch.batch_id} 处理失败: {error}")
            
            # 检查是否需要重试或终止周期
            self._handle_batch_failure(batch_index)

    def get_progress_status(self) -> Dict:
        """获取当前进度状态"""
        with self.lock:
            if not self.current_cycle:
                return {"status": "no_active_cycle"}
            
            completed_batches = sum(1 for b in self.current_cycle.batches if b.status == "completed")
            processing_batches = sum(1 for b in self.current_cycle.batches if b.status == "processing")
            failed_batches = sum(1 for b in self.current_cycle.batches if b.status == "failed")
            
            current_time = time.time()
            elapsed_time = current_time - self.current_cycle.start_time
            
            # 计算预估完成时间
            if completed_batches > 0:
                avg_batch_time = elapsed_time / completed_batches
                remaining_batches = len(self.current_cycle.batches) - completed_batches - processing_batches
                estimated_remaining = remaining_batches * avg_batch_time
            else:
                estimated_remaining = 0
            
            return {
                "cycle_id": self.current_cycle.cycle_id,
                "status": self.current_cycle.status,
                "total_symbols": self.current_cycle.total_symbols,
                "processed_symbols": self.current_cycle.processed_symbols,
                "qualified_symbols": self.current_cycle.qualified_symbols,
                "total_batches": len(self.current_cycle.batches),
                "completed_batches": completed_batches,
                "processing_batches": processing_batches,
                "failed_batches": failed_batches,
                "progress_percentage": (completed_batches / len(self.current_cycle.batches)) * 100,
                "elapsed_time": elapsed_time,
                "estimated_remaining": estimated_remaining,
                "duplicate_scans": len(self.duplicate_scans),
                "current_batch_details": self._get_current_batch_details()
            }

    def _get_current_batch_details(self) -> List[Dict]:
        """获取当前批次详情"""
        if not self.current_cycle:
            return []
        
        details = []
        for batch in self.current_cycle.batches:
            if batch.status in ["processing", "completed"]:
                details.append({
                    "batch_id": batch.batch_id,
                    "batch_index": batch.batch_index + 1,
                    "status": batch.status,
                    "symbols_count": len(batch.symbols_in_batch),
                    "processed_count": len(batch.processed_symbols),
                    "failed_count": len(batch.failed_symbols),
                    "processing_time": (batch.end_time or time.time()) - batch.start_time
                })
        
        return details

    def _check_cycle_completion(self):
        """检查周期是否完成"""
        if not self.current_cycle:
            return
        
        all_completed = all(b.status in ["completed", "failed"] for b in self.current_cycle.batches)
        
        if all_completed:
            self.current_cycle.status = "completed"
            self.current_cycle.end_time = time.time()
            
            # 统计结果
            total_processed = sum(len(b.processed_symbols) for b in self.current_cycle.batches)
            total_failed = sum(len(b.failed_symbols) for b in self.current_cycle.batches)
            cycle_time = self.current_cycle.end_time - self.current_cycle.start_time
            
            self.log.info(f"扫描周期 {self.current_cycle.cycle_id} 完成: "
                         f"处理 {total_processed} 个币种, 失败 {total_failed} 个, "
                         f"耗时 {cycle_time:.1f}秒")
            
            # 保存到历史记录
            self.scan_history.append(self.current_cycle)
            self.current_cycle = None
            
            # 清理重复扫描记录
            self.duplicate_scans.clear()

    def _force_complete_cycle(self, reason: str):
        """强制完成当前周期"""
        if self.current_cycle:
            self.current_cycle.status = reason
            self.current_cycle.end_time = time.time()
            self.scan_history.append(self.current_cycle)
            self.log.warning(f"强制完成扫描周期 {self.current_cycle.cycle_id}: {reason}")
            self.current_cycle = None

    def _handle_batch_failure(self, batch_index: int):
        """处理批次失败"""
        failed_batches = sum(1 for b in self.current_cycle.batches if b.status == "failed")
        total_batches = len(self.current_cycle.batches)
        
        # 如果失败率超过30%，终止整个周期
        if failed_batches / total_batches > 0.3:
            self.log.error(f"批次失败率过高 ({failed_batches}/{total_batches}), 终止扫描周期")
            self._force_complete_cycle("high_failure_rate")

    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                with self.lock:
                    if self.current_cycle:
                        current_time = time.time()
                        elapsed = current_time - self.current_cycle.start_time
                        
                        # 检查周期超时
                        if elapsed > self.cycle_timeout:
                            self.log.error(f"扫描周期超时: {elapsed:.1f}秒")
                            self._force_complete_cycle("timeout")
                        
                        # 检查批次超时
                        for batch in self.current_cycle.batches:
                            if batch.status == "processing":
                                batch_elapsed = current_time - batch.start_time
                                if batch_elapsed > self.max_scan_interval:
                                    self.log.error(f"批次 {batch.batch_id} 处理超时: {batch_elapsed:.1f}秒")
                                    batch.status = "failed"
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                self.log.error(f"监控循环异常: {e}")
                time.sleep(60)

    def get_scan_statistics(self) -> Dict:
        """获取扫描统计信息"""
        with self.lock:
            total_cycles = len(self.scan_history)
            if self.current_cycle:
                total_cycles += 1
            
            if not self.scan_history:
                return {"total_cycles": total_cycles, "average_cycle_time": 0}
            
            completed_cycles = [c for c in self.scan_history if c.status == "completed"]
            
            if completed_cycles:
                avg_cycle_time = sum(c.end_time - c.start_time for c in completed_cycles) / len(completed_cycles)
                avg_symbols_per_cycle = sum(c.processed_symbols for c in completed_cycles) / len(completed_cycles)
            else:
                avg_cycle_time = 0
                avg_symbols_per_cycle = 0
            
            return {
                "total_cycles": total_cycles,
                "completed_cycles": len(completed_cycles),
                "average_cycle_time": avg_cycle_time,
                "average_symbols_per_cycle": avg_symbols_per_cycle,
                "total_duplicate_scans": sum(self.duplicate_scans.values()),
                "unique_duplicated_symbols": len(self.duplicate_scans)
            }

    def export_progress_report(self) -> str:
        """导出进度报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "current_status": self.get_progress_status(),
            "statistics": self.get_scan_statistics(),
            "recent_cycles": [asdict(c) for c in self.scan_history[-5:]],  # 最近5个周期
            "duplicate_scans": dict(self.duplicate_scans)
        }
        
        report_file = f"batch_progress_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.log.info(f"进度报告已导出: {report_file}")
        return report_file

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        self.log.info("批次监控系统已停止")