"""
筹码抛压扫描模块
实现0.1%深度检测，识别并避开庄家对倒行为
"""

import logging
import time
from typing import Dict, List, Optional, Tuple
import numpy as np


class ChipPressureScanner:
    """筹码抛压扫描器"""
    
    def __init__(self, trader, config: dict):
        """
        初始化筹码抛压扫描器
        
        Args:
            trader: 交易器实例
            config: 配置参数
        """
        self.trader = trader
        self.config = config
        self.log = logging.getLogger('ChipPressureScanner')
        
        # 配置参数
        self.depth_percentage = config.get('depth_percentage', 0.001)  # 0.1%深度
        self.max_pressure_ratio = config.get('max_pressure_ratio', 3.0)  # 最大抛压比例
        self.min_bid_ask_balance = config.get('min_bid_ask_balance', 0.3)  # 最小买卖平衡比例
        self.wash_trade_threshold = config.get('wash_trade_threshold', 0.8)  # 对倒检测阈值
        self.large_order_threshold = config.get('large_order_threshold', 50000)  # 大单阈值(USDT)
        
        # 缓存
        self.depth_cache = {}
        self.cache_ttl = config.get('cache_ttl', 30)  # 缓存30秒
        
    def scan_chip_pressure(self, symbol: str, current_price: float) -> Dict:
        """
        扫描筹码抛压情况
        
        Args:
            symbol: 交易对
            current_price: 当前价格
            
        Returns:
            Dict: 扫描结果
        """
        try:
            # 获取深度数据
            depth_data = self._get_depth_data(symbol)
            if not depth_data:
                return {
                    'valid': False,
                    'reason': '无法获取深度数据',
                    'pressure_ratio': 0,
                    'bid_ask_balance': 0,
                    'wash_trade_detected': False
                }
            
            # 计算0.1%深度范围
            price_range = current_price * self.depth_percentage
            upper_price = current_price + price_range
            lower_price = current_price - price_range
            
            # 分析买卖盘压力
            pressure_analysis = self._analyze_pressure(depth_data, current_price, upper_price, lower_price)
            
            # 检测对倒行为
            wash_trade_detected = self._detect_wash_trading(depth_data, current_price)
            
            # 检测大单压制
            large_order_pressure = self._detect_large_order_pressure(depth_data, current_price)
            
            # 综合判断
            is_valid = self._evaluate_chip_pressure(
                pressure_analysis, 
                wash_trade_detected, 
                large_order_pressure
            )
            
            result = {
                'valid': is_valid,
                'reason': self._get_rejection_reason(pressure_analysis, wash_trade_detected, large_order_pressure),
                'pressure_ratio': pressure_analysis['pressure_ratio'],
                'bid_ask_balance': pressure_analysis['bid_ask_balance'],
                'wash_trade_detected': wash_trade_detected,
                'large_order_pressure': large_order_pressure,
                'depth_analysis': pressure_analysis
            }
            
            self.log.debug(f"{symbol} 筹码抛压扫描结果: {result}")
            return result
            
        except Exception as e:
            self.log.error(f"{symbol} 筹码抛压扫描失败: {e}")
            return {
                'valid': False,
                'reason': f'扫描异常: {str(e)}',
                'pressure_ratio': 0,
                'bid_ask_balance': 0,
                'wash_trade_detected': False
            }
    
    def _get_depth_data(self, symbol: str) -> Optional[Dict]:
        """获取深度数据（带缓存）"""
        current_time = time.time()
        
        # 检查缓存
        if symbol in self.depth_cache:
            cache_data = self.depth_cache[symbol]
            if current_time - cache_data['timestamp'] < self.cache_ttl:
                return cache_data['data']
        
        try:
            # 获取深度数据
            depth = self.trader.http.get('/fapi/v1/depth', {'symbol': symbol, 'limit': 100})
            if not depth or 'bids' not in depth or 'asks' not in depth:
                return None
            
            # 缓存数据
            self.depth_cache[symbol] = {
                'data': depth,
                'timestamp': current_time
            }
            
            return depth
            
        except Exception as e:
            self.log.error(f"获取{symbol}深度数据失败: {e}")
            return None
    
    def _analyze_pressure(self, depth_data: Dict, current_price: float, upper_price: float, lower_price: float) -> Dict:
        """分析买卖盘压力"""
        try:
            bids = depth_data['bids']  # 买盘 [[price, quantity], ...]
            asks = depth_data['asks']  # 卖盘 [[price, quantity], ...]
            
            # 计算0.1%范围内的买卖量
            bid_volume_in_range = 0
            ask_volume_in_range = 0
            
            # 分析买盘（价格从高到低）
            for bid in bids:
                price = float(bid[0])
                quantity = float(bid[1])
                if price >= lower_price:
                    bid_volume_in_range += quantity * price  # 转换为USDT价值
                else:
                    break
            
            # 分析卖盘（价格从低到高）
            for ask in asks:
                price = float(ask[0])
                quantity = float(ask[1])
                if price <= upper_price:
                    ask_volume_in_range += quantity * price  # 转换为USDT价值
                else:
                    break
            
            # 计算压力比例
            total_volume = bid_volume_in_range + ask_volume_in_range
            pressure_ratio = ask_volume_in_range / bid_volume_in_range if bid_volume_in_range > 0 else float('inf')
            
            # 计算买卖平衡比例
            bid_ask_balance = bid_volume_in_range / total_volume if total_volume > 0 else 0
            
            return {
                'bid_volume': bid_volume_in_range,
                'ask_volume': ask_volume_in_range,
                'total_volume': total_volume,
                'pressure_ratio': pressure_ratio,
                'bid_ask_balance': bid_ask_balance,
                'price_range': {
                    'upper': upper_price,
                    'lower': lower_price,
                    'current': current_price
                }
            }
            
        except Exception as e:
            self.log.error(f"分析买卖盘压力失败: {e}")
            return {
                'bid_volume': 0,
                'ask_volume': 0,
                'total_volume': 0,
                'pressure_ratio': float('inf'),
                'bid_ask_balance': 0
            }
    
    def _detect_wash_trading(self, depth_data: Dict, current_price: float) -> bool:
        """检测对倒行为"""
        try:
            bids = depth_data['bids']
            asks = depth_data['asks']
            
            if not bids or not asks:
                return False
            
            # 检查最优买卖价差
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            spread = (best_ask - best_bid) / current_price
            
            # 价差过小可能是对倒
            if spread < 0.0001:  # 0.01%
                return True
            
            # 检查订单簿对称性
            symmetry_score = self._calculate_order_book_symmetry(bids, asks, current_price)
            if symmetry_score > self.wash_trade_threshold:
                return True
            
            # 检查异常大单对称
            large_bid_orders = self._find_large_orders(bids, current_price)
            large_ask_orders = self._find_large_orders(asks, current_price)
            
            if self._check_order_symmetry(large_bid_orders, large_ask_orders):
                return True
            
            return False
            
        except Exception as e:
            self.log.error(f"检测对倒行为失败: {e}")
            return False
    
    def _calculate_order_book_symmetry(self, bids: List, asks: List, current_price: float) -> float:
        """计算订单簿对称性得分"""
        try:
            # 取前10档进行对称性分析
            max_levels = min(10, len(bids), len(asks))
            symmetry_scores = []
            
            for i in range(max_levels):
                bid_price = float(bids[i][0])
                bid_quantity = float(bids[i][1])
                ask_price = float(asks[i][0])
                ask_quantity = float(asks[i][1])
                
                # 计算价格距离中心的比例
                bid_distance = abs(bid_price - current_price) / current_price
                ask_distance = abs(ask_price - current_price) / current_price
                
                # 计算数量相似度
                quantity_similarity = min(bid_quantity, ask_quantity) / max(bid_quantity, ask_quantity)
                
                # 计算距离相似度
                distance_similarity = 1 - abs(bid_distance - ask_distance) / max(bid_distance, ask_distance)
                
                # 综合对称性得分
                level_symmetry = (quantity_similarity + distance_similarity) / 2
                symmetry_scores.append(level_symmetry)
            
            return np.mean(symmetry_scores) if symmetry_scores else 0
            
        except Exception as e:
            self.log.error(f"计算订单簿对称性失败: {e}")
            return 0
    
    def _find_large_orders(self, orders: List, current_price: float) -> List[Dict]:
        """找出大单"""
        large_orders = []
        
        for order in orders:
            price = float(order[0])
            quantity = float(order[1])
            value = price * quantity
            
            if value >= self.large_order_threshold:
                large_orders.append({
                    'price': price,
                    'quantity': quantity,
                    'value': value,
                    'distance_pct': abs(price - current_price) / current_price
                })
        
        return large_orders
    
    def _check_order_symmetry(self, bid_orders: List[Dict], ask_orders: List[Dict]) -> bool:
        """检查大单对称性"""
        if not bid_orders or not ask_orders:
            return False
        
        # 检查是否有相似价值和距离的大单
        for bid_order in bid_orders:
            for ask_order in ask_orders:
                value_ratio = min(bid_order['value'], ask_order['value']) / max(bid_order['value'], ask_order['value'])
                distance_diff = abs(bid_order['distance_pct'] - ask_order['distance_pct'])
                
                # 如果价值相似且距离相似，可能是对倒
                if value_ratio > 0.9 and distance_diff < 0.001:  # 0.1%
                    return True
        
        return False
    
    def _detect_large_order_pressure(self, depth_data: Dict, current_price: float) -> Dict:
        """检测大单压制"""
        try:
            asks = depth_data['asks']
            
            # 找出卖盘中的大单
            large_sell_orders = []
            total_ask_value = 0
            large_order_value = 0
            
            for ask in asks[:20]:  # 检查前20档
                price = float(ask[0])
                quantity = float(ask[1])
                value = price * quantity
                total_ask_value += value
                
                if value >= self.large_order_threshold:
                    distance_pct = (price - current_price) / current_price
                    large_sell_orders.append({
                        'price': price,
                        'quantity': quantity,
                        'value': value,
                        'distance_pct': distance_pct
                    })
                    large_order_value += value
            
            # 计算大单压制比例
            pressure_ratio = large_order_value / total_ask_value if total_ask_value > 0 else 0
            
            # 检查是否有近价位大单压制
            near_price_pressure = any(
                order['distance_pct'] < 0.005 and order['value'] > self.large_order_threshold * 2
                for order in large_sell_orders
            )
            
            return {
                'large_orders': large_sell_orders,
                'pressure_ratio': pressure_ratio,
                'near_price_pressure': near_price_pressure,
                'total_large_value': large_order_value,
                'total_ask_value': total_ask_value
            }
            
        except Exception as e:
            self.log.error(f"检测大单压制失败: {e}")
            return {
                'large_orders': [],
                'pressure_ratio': 0,
                'near_price_pressure': False,
                'total_large_value': 0,
                'total_ask_value': 0
            }
    
    def _evaluate_chip_pressure(self, pressure_analysis: Dict, wash_trade_detected: bool, large_order_pressure: Dict) -> bool:
        """综合评估筹码抛压情况"""
        # 检查对倒行为
        if wash_trade_detected:
            return False
        
        # 检查抛压比例
        if pressure_analysis['pressure_ratio'] > self.max_pressure_ratio:
            return False
        
        # 检查买卖平衡
        if pressure_analysis['bid_ask_balance'] < self.min_bid_ask_balance:
            return False
        
        # 检查大单压制
        if large_order_pressure['near_price_pressure']:
            return False
        
        if large_order_pressure['pressure_ratio'] > 0.5:  # 大单占比超过50%
            return False
        
        return True
    
    def _get_rejection_reason(self, pressure_analysis: Dict, wash_trade_detected: bool, large_order_pressure: Dict) -> str:
        """获取拒绝原因"""
        if wash_trade_detected:
            return "检测到对倒行为"
        
        if pressure_analysis['pressure_ratio'] > self.max_pressure_ratio:
            return f"抛压过大: {pressure_analysis['pressure_ratio']:.2f} > {self.max_pressure_ratio}"
        
        if pressure_analysis['bid_ask_balance'] < self.min_bid_ask_balance:
            return f"买卖失衡: {pressure_analysis['bid_ask_balance']:.2f} < {self.min_bid_ask_balance}"
        
        if large_order_pressure['near_price_pressure']:
            return "检测到近价位大单压制"
        
        if large_order_pressure['pressure_ratio'] > 0.5:
            return f"大单压制过重: {large_order_pressure['pressure_ratio']:.2f} > 0.5"
        
        return "通过筹码抛压检测"
    
    def get_pressure_statistics(self, symbol: str) -> Dict:
        """获取压力统计信息"""
        try:
            depth_data = self._get_depth_data(symbol)
            if not depth_data:
                return {}
            
            current_price = (float(depth_data['bids'][0][0]) + float(depth_data['asks'][0][0])) / 2
            
            # 计算各档位压力分布
            pressure_levels = []
            for i in range(min(10, len(depth_data['asks']))):
                ask = depth_data['asks'][i]
                price = float(ask[0])
                quantity = float(ask[1])
                value = price * quantity
                distance_pct = (price - current_price) / current_price * 100
                
                pressure_levels.append({
                    'level': i + 1,
                    'price': price,
                    'quantity': quantity,
                    'value': value,
                    'distance_pct': distance_pct
                })
            
            return {
                'symbol': symbol,
                'current_price': current_price,
                'pressure_levels': pressure_levels,
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.log.error(f"获取{symbol}压力统计失败: {e}")
            return {}
    
    def batch_scan_pressure(self, symbols: List[str]) -> Dict[str, Dict]:
        """批量扫描筹码抛压"""
        results = {}
        
        for symbol in symbols:
            try:
                # 获取当前价格
                ticker = self.trader.http.get('/fapi/v1/ticker/price', {'symbol': symbol})
                if not ticker or 'price' not in ticker:
                    continue
                
                current_price = float(ticker['price'])
                
                # 扫描筹码抛压
                result = self.scan_chip_pressure(symbol, current_price)
                results[symbol] = result
                
            except Exception as e:
                self.log.error(f"批量扫描{symbol}失败: {e}")
                results[symbol] = {
                    'valid': False,
                    'reason': f'扫描异常: {str(e)}',
                    'pressure_ratio': 0,
                    'bid_ask_balance': 0,
                    'wash_trade_detected': False
                }
        
        return results
    
    def update_config(self, new_config: Dict):
        """更新配置"""
        self.config.update(new_config)
        
        # 更新配置参数
        self.depth_percentage = self.config.get('depth_percentage', 0.001)
        self.max_pressure_ratio = self.config.get('max_pressure_ratio', 3.0)
        self.min_bid_ask_balance = self.config.get('min_bid_ask_balance', 0.3)
        self.wash_trade_threshold = self.config.get('wash_trade_threshold', 0.8)
        self.large_order_threshold = self.config.get('large_order_threshold', 50000)
        self.cache_ttl = self.config.get('cache_ttl', 30)
        
        self.log.info(f"筹码抛压扫描器配置已更新: {new_config}")