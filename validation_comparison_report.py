#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证对比报告脚本
详细分析原快速脚本与增量版本脚本的评分差异
"""

import pandas as pd
import numpy as np
import pickle
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_score_calculator import EnhancedScoreCalculator

class ValidationComparisonAnalyzer:
    """验证对比分析器"""
    
    def __init__(self, cache_dir='cache'):
        """初始化分析器"""
        self.cache_dir = cache_dir
        self.logger = logging.getLogger('validation_comparison')
        self.logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        # 初始化增强评分计算器
        self.enhanced_calculator = EnhancedScoreCalculator()
        
        self.logger.info("验证对比分析器初始化完成")
    
    def load_candidate_cache(self):
        """加载候选池缓存"""
        cache_file = os.path.join(self.cache_dir, 'candidates.pkl')
        
        if not os.path.exists(cache_file):
            self.logger.error(f"候选池缓存文件不存在: {cache_file}")
            return {}
        
        try:
            with open(cache_file, 'rb') as f:
                candidates = pickle.load(f)
            
            self.logger.info(f"成功加载候选池缓存，包含 {len(candidates)} 个币种")
            return candidates
        
        except Exception as e:
            self.logger.error(f"加载候选池缓存失败: {e}")
            return {}
    
    def load_cached_klines(self, symbol, timeframe='15m', limit=200):
        """加载缓存的K线数据"""
        cache_file = os.path.join(self.cache_dir, f'klines_{symbol}_{timeframe}_{limit}.pkl')
        
        if not os.path.exists(cache_file):
            return None
        
        try:
            with open(cache_file, 'rb') as f:
                klines = pickle.load(f)
            return klines
        except Exception as e:
            self.logger.warning(f"加载K线缓存失败 {symbol}: {e}")
            return None
    
    def load_cached_depth(self, symbol):
        """加载缓存的深度数据"""
        cache_file = os.path.join(self.cache_dir, f'depth_{symbol}.json')
        
        if not os.path.exists(cache_file):
            return None
        
        try:
            with open(cache_file, 'r') as f:
                depth_data = json.load(f)
            
            # 提取深度值
            if isinstance(depth_data, dict):
                return depth_data.get('depth', 0)
            elif isinstance(depth_data, (int, float)):
                return float(depth_data)
            else:
                return 0
        except Exception as e:
            self.logger.warning(f"加载深度缓存失败 {symbol}: {e}")
            return None
    
    def get_symbol_age_days(self, symbol):
        """计算币种年龄（天数）"""
        try:
            # 从symbols.json获取上市时间
            symbols_file = os.path.join(self.cache_dir, 'symbols.json')
            if os.path.exists(symbols_file):
                with open(symbols_file, 'r') as f:
                    symbols_data = json.load(f)
                
                for item in symbols_data:
                    if item.get('symbol') == symbol:
                        onboard_date = item.get('onboardDate')
                        if onboard_date:
                            # 转换时间戳为天数
                            onboard_time = datetime.fromtimestamp(onboard_date / 1000)
                            age_days = (datetime.now() - onboard_time).days
                            return max(1, age_days)
            
            # 默认返回500天（老币）
            return 500
            
        except Exception as e:
            self.logger.warning(f"计算币龄失败 {symbol}: {e}")
            return 500
    
    def calculate_original_score(self, symbol, klines_15m, depth_data, age_days):
        """计算原快速脚本的评分"""
        try:
            if len(klines_15m) < 50:
                return None
            
            # 原脚本的评分逻辑
            total_score = 0.0
            
            # 1. 深度评分 (0-2分)
            if depth_data and depth_data > 0:
                if depth_data >= 1000000:  # 100万以上
                    depth_score = 2.0
                elif depth_data >= 500000:  # 50万以上
                    depth_score = 1.5
                elif depth_data >= 100000:  # 10万以上
                    depth_score = 1.0
                elif depth_data >= 50000:   # 5万以上
                    depth_score = 0.5
                else:
                    depth_score = 0.0
            else:
                depth_score = 0.0
            
            # 2. 成交量变化评分 (0-2分)
            volumes = klines_15m['volume'].astype(float)
            if len(volumes) >= 20:
                recent_vol = volumes.tail(10).mean()
                previous_vol = volumes.iloc[-20:-10].mean()
                if previous_vol > 0:
                    vol_change = (recent_vol - previous_vol) / previous_vol
                    if vol_change > 0.5:
                        volume_score = 2.0
                    elif vol_change > 0.2:
                        volume_score = 1.5
                    elif vol_change > 0:
                        volume_score = 1.0
                    else:
                        volume_score = 0.0
                else:
                    volume_score = 0.0
            else:
                volume_score = 0.0
            
            # 3. 币龄评分 (0-2分)
            if age_days <= 30:
                age_score = 0.5
            elif age_days <= 90:
                age_score = 1.0
            elif age_days <= 365:
                age_score = 1.5
            else:
                age_score = 2.0
            
            # 4. 通道评分 (0-2.5分)
            closes = klines_15m['close'].astype(float)
            highs = klines_15m['high'].astype(float)
            lows = klines_15m['low'].astype(float)
            
            if age_days <= 30:
                n = 10
            elif age_days <= 90:
                n = 15
            else:
                n = 20
            
            if len(closes) >= n:
                recent_closes = closes.tail(n)
                recent_highs = highs.tail(n)
                recent_lows = lows.tail(n)
                
                upper_channel = recent_highs.max()
                lower_channel = recent_lows.min()
                current_price = closes.iloc[-1]
                current_high = highs.iloc[-1]
                
                if upper_channel > lower_channel:
                    channel_position = (current_price - lower_channel) / (upper_channel - lower_channel)
                    high_position = (current_high - lower_channel) / (upper_channel - lower_channel)
                    
                    if channel_position >= 0.95 or high_position >= 0.98:
                        channel_score = 2.5
                    elif channel_position >= 0.9 or high_position >= 0.95:
                        channel_score = 2.0
                    elif channel_position >= 0.8:
                        channel_score = 1.5
                    elif channel_position >= 0.7:
                        channel_score = 1.0
                    elif channel_position >= 0.6:
                        channel_score = 0.5
                    else:
                        channel_score = 0.0
                else:
                    channel_score = 0.0
            else:
                channel_score = 0.0
            
            # 5. 动量评分 (0-1分)
            if len(closes) >= 5:
                recent_change = (closes.iloc[-1] - closes.iloc[-5]) / closes.iloc[-5]
                if recent_change > 0.05:
                    momentum_score = 1.0
                elif recent_change > 0.02:
                    momentum_score = 0.5
                else:
                    momentum_score = 0.0
            else:
                momentum_score = 0.0
            
            # 计算总分
            total_score = depth_score + volume_score + age_score + channel_score + momentum_score
            
            # 基础保护分数
            if total_score < 2.0:
                total_score = max(total_score, 1.0)
            
            return {
                'total_score': total_score,
                'depth_score': depth_score,
                'volume_score': volume_score,
                'age_score': age_score,
                'channel_score': channel_score,
                'momentum_score': momentum_score
            }
            
        except Exception as e:
            self.logger.error(f"计算原评分失败 {symbol}: {e}")
            return None
    
    def calculate_enhanced_score(self, symbol, klines_15m, depth_data, age_days):
        """计算增强版评分"""
        try:
            score_result = self.enhanced_calculator.calculate_comprehensive_score(
                symbol=symbol,
                df_data=klines_15m,
                depth_data=depth_data,
                additional_data={'age_days': age_days}
            )
            
            return {
                'total_score': score_result.total_score,
                'component_scores': score_result.component_scores,
                'component_details': score_result.component_details
            }
            
        except Exception as e:
            self.logger.error(f"计算增强评分失败 {symbol}: {e}")
            return None
    
    def compare_single_symbol(self, symbol):
        """对比单个币种的评分差异"""
        try:
            # 加载数据
            klines_15m = self.load_cached_klines(symbol, '15m', 200)
            if klines_15m is None or len(klines_15m) < 50:
                return None
            
            depth_data = self.load_cached_depth(symbol)
            age_days = self.get_symbol_age_days(symbol)
            
            # 计算两种评分
            original_score = self.calculate_original_score(symbol, klines_15m, depth_data, age_days)
            enhanced_score = self.calculate_enhanced_score(symbol, klines_15m, depth_data, age_days)
            
            if original_score is None or enhanced_score is None:
                return None
            
            # 构建对比结果
            result = {
                'symbol': symbol,
                'original_score': original_score,
                'enhanced_score': enhanced_score,
                'score_difference': enhanced_score['total_score'] - original_score['total_score'],
                'original_passing': original_score['total_score'] >= 7.0,
                'enhanced_passing': enhanced_score['total_score'] >= 7.0,
                'passing_difference': enhanced_score['total_score'] >= 7.0 and original_score['total_score'] < 7.0,
                'current_price': float(klines_15m['close'].iloc[-1]),
                'age_days': age_days,
                'depth': depth_data
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"对比币种失败 {symbol}: {e}")
            return None
    
    def analyze_all_differences(self):
        """分析所有币种的评分差异"""
        candidates = self.load_candidate_cache()
        if not candidates:
            return {'error': '候选池为空'}
        
        self.logger.info(f"开始对比分析 {len(candidates)} 个候选币种...")
        
        results = []
        success_count = 0
        
        for symbol in candidates:
            result = self.compare_single_symbol(symbol)
            if result:
                results.append(result)
                success_count += 1
                
                # 输出对比信息
                orig_score = result['original_score']['total_score']
                enh_score = result['enhanced_score']['total_score']
                diff = result['score_difference']
                
                self.logger.info(f"{symbol}: 原={orig_score:.2f}, 增强={enh_score:.2f}, 差异={diff:+.2f}")
        
        # 统计分析
        if results:
            original_passing = sum(1 for r in results if r['original_passing'])
            enhanced_passing = sum(1 for r in results if r['enhanced_passing'])
            passing_differences = sum(1 for r in results if r['passing_difference'])
            
            avg_original = np.mean([r['original_score']['total_score'] for r in results])
            avg_enhanced = np.mean([r['enhanced_score']['total_score'] for r in results])
            avg_difference = np.mean([r['score_difference'] for r in results])
            
            analysis_result = {
                'total_candidates': len(candidates),
                'success_analyses': success_count,
                'original_passing': original_passing,
                'enhanced_passing': enhanced_passing,
                'passing_differences': passing_differences,
                'avg_original_score': avg_original,
                'avg_enhanced_score': avg_enhanced,
                'avg_score_difference': avg_difference,
                'results': results,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        else:
            analysis_result = {'error': '没有成功分析的币种'}
        
        return analysis_result
    
    def generate_comparison_report(self, analysis_result):
        """生成对比分析报告"""
        if 'error' in analysis_result:
            return f"分析失败: {analysis_result['error']}"
        
        report = []
        report.append("=" * 80)
        report.append("评分系统对比验证报告")
        report.append("=" * 80)
        report.append(f"分析时间: {analysis_result['analysis_time']}")
        report.append("")
        
        # 总体统计对比
        report.append("📊 总体统计对比:")
        report.append(f"  候选池总数: {analysis_result['total_candidates']}")
        report.append(f"  成功分析: {analysis_result['success_analyses']}")
        report.append("")
        report.append(f"  原快速脚本达标: {analysis_result['original_passing']} 个币种")
        report.append(f"  增强版脚本达标: {analysis_result['enhanced_passing']} 个币种")
        report.append(f"  评分差异导致的达标变化: {analysis_result['passing_differences']} 个币种")
        report.append("")
        report.append(f"  原脚本平均评分: {analysis_result['avg_original_score']:.2f}")
        report.append(f"  增强版平均评分: {analysis_result['avg_enhanced_score']:.2f}")
        report.append(f"  平均评分差异: {analysis_result['avg_score_difference']:+.2f}")
        report.append("")
        
        # 评分差异最大的币种
        results = analysis_result['results']
        results_sorted = sorted(results, key=lambda x: x['score_difference'], reverse=True)
        
        report.append("🔍 评分差异最大的前10个币种:")
        report.append("-" * 80)
        for i, result in enumerate(results_sorted[:10]):
            symbol = result['symbol']
            orig = result['original_score']['total_score']
            enh = result['enhanced_score']['total_score']
            diff = result['score_difference']
            
            report.append(f"  {i+1:2d}. {symbol:<12} | 原={orig:5.2f}, 增强={enh:5.2f}, 差异={diff:+6.2f}")
        
        report.append("")
        
        # 达标状态变化的币种
        passing_changes = [r for r in results if r['passing_difference']]
        if passing_changes:
            report.append(f"🎯 因评分差异而达标的币种 ({len(passing_changes)}个):")
            report.append("-" * 80)
            for result in passing_changes:
                symbol = result['symbol']
                orig = result['original_score']['total_score']
                enh = result['enhanced_score']['total_score']
                
                report.append(f"  {symbol:<12} | 原={orig:5.2f}(不达标) → 增强={enh:5.2f}(达标)")
        
        report.append("")
        
        # 关键发现
        report.append("🔑 关键发现:")
        original_rate = (analysis_result['original_passing'] / analysis_result['success_analyses']) * 100
        enhanced_rate = (analysis_result['enhanced_passing'] / analysis_result['success_analyses']) * 100
        
        report.append(f"  1. 原快速脚本达标率: {original_rate:.1f}%")
        report.append(f"  2. 增强版脚本达标率: {enhanced_rate:.1f}%")
        report.append(f"  3. 达标率差异: {enhanced_rate - original_rate:+.1f}%")
        report.append(f"  4. 平均评分提升: {analysis_result['avg_score_difference']:+.2f}分")
        
        if analysis_result['avg_score_difference'] > 5:
            report.append("  5. ⚠️  评分差异巨大，可能存在系统性差异")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_report(self, report_content):
        """保存报告到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'validation_comparison_report_{timestamp}.txt'
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"对比报告已保存到: {filename}")
            return filename
        
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")
            return None

def main():
    """主函数"""
    print("=" * 60)
    print("评分系统对比验证分析")
    print("原快速脚本 vs 增强版脚本")
    print("=" * 60)
    
    # 创建分析器
    analyzer = ValidationComparisonAnalyzer()
    
    # 执行对比分析
    print("正在对比分析两个评分系统...")
    analysis_result = analyzer.analyze_all_differences()
    
    # 生成报告
    print("正在生成对比报告...")
    report = analyzer.generate_comparison_report(analysis_result)
    
    # 保存报告
    filename = analyzer.save_report(report)
    
    # 输出关键统计
    if 'error' not in analysis_result:
        print(f"\n📊 对比结果:")
        print(f"  总候选数: {analysis_result['total_candidates']}")
        print(f"  原脚本达标: {analysis_result['original_passing']}")
        print(f"  增强版达标: {analysis_result['enhanced_passing']}")
        print(f"  平均评分差异: {analysis_result['avg_score_difference']:+.2f}")
        
        if filename:
            print(f"\n📄 详细对比报告已保存到: {filename}")
    else:
        print(f"\n❌ 分析失败: {analysis_result['error']}")

if __name__ == "__main__":
    main()