#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略模块初始化文件
全币种均线通道量化策略包
"""

__version__ = "1.0.0"
__author__ = "Strategy Team"
__description__ = "全币种均线通道+利润雪崩+抗洗盘复位+负费率复利量化策略"

# 导入主要策略类
try:
    from .maker_channel import MakerChannelStrategy
    __all__ = ['MakerChannelStrategy']
except ImportError as e:
    # 如果导入失败，记录错误但不中断
    import logging
    logging.warning(f"策略模块导入失败: {e}")
    __all__ = []

# 策略元信息
STRATEGY_INFO = {
    "name": "全币种均线通道策略",
    "version": __version__,
    "description": __description__,
    "features": [
        "均线通道突破",
        "末位淘汰机制", 
        "利润雪崩",
        "抗洗盘复位",
        "负费率复利"
    ],
    "supported_exchanges": ["binance"],
    "supported_markets": ["futures"],
    "risk_level": "medium",
    "min_capital": 1000
}

def get_strategy_info():
    """获取策略信息"""
    return STRATEGY_INFO

def get_version():
    """获取版本号"""
    return __version__