#!/usr/bin/env python3
"""
精确参数测试脚本 - 使用与maker_channel.py完全相同的参数
"""

import sys
import json
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from binance_trader import BinanceTrader

def test_exact_params():
    """使用与maker_channel.py完全相同的参数测试API"""
    
    # 加载配置
    with open('config/config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 添加缺失的配置项
    config['first_nominal'] = 100
    config['max_nominal'] = 1000
    
    # 初始化交易器
    trader = BinanceTrader(config)
    
    # 测试参数 - 与maker_channel.py完全一致
    test_cases = [
        {'symbol': 'SSVUSDT', 'interval': '15m', 'limit': 200},  # 默认参数
        {'symbol': 'SSVUSDT', 'interval': '15m', 'limit': 50},   # 常用参数
        {'symbol': 'BTCUSDT', 'interval': '15m', 'limit': 200},  # 对比测试
        {'symbol': 'BCHUSDT', 'interval': '15m', 'limit': 200},  # 对比测试
    ]
    
    print("测试与maker_channel.py完全相同的API调用参数:")
    print("=" * 60)
    
    for case in test_cases:
        symbol = case['symbol']
        interval = case['interval']
        limit = case['limit']
        
        try:
            print(f"\n测试 {symbol} (interval={interval}, limit={limit}):")
            
            # 使用与maker_channel.py完全相同的API调用
            klines = trader.http.get('/fapi/v1/klines', {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            })
            
            if klines:
                print(f"  ✓ 成功获取 {len(klines)} 条K线数据")
                if len(klines) >= 1:
                    print(f"  - 第一条: {klines[0]}")
                if len(klines) >= 2:
                    print(f"  - 第二条: {klines[1]}")
                if len(klines) < 20:
                    print(f"  ⚠️  警告: 数据不足20条，仅有{len(klines)}条")
            else:
                print(f"  ❌ 获取失败: 返回空数据")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    test_exact_params()