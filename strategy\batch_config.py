"""
批次处理配置管理
定义扫描周期、监督机制和可视化参数
"""

from dataclasses import dataclass
from typing import Dict, Any
import json
import os

@dataclass
class BatchConfig:
    """批次处理配置"""
    
    # 基本配置
    batch_size: int = 20  # 每批次处理币种数量
    total_symbols: int = 490  # 总币种数量
    initial_pool_size: int = 50  # 初始候选池大小（涨幅榜前25 + 成交额前25）
    
    # 扫描周期配置
    scan_interval: int = 1800  # 全市场扫描间隔（秒），30分钟
    batch_interval: int = 30  # 批次间隔（秒）
    max_cycle_time: int = 1500  # 最大周期时间（秒），25分钟
    
    # 监督机制配置
    enable_duplicate_check: bool = True  # 启用重复扫描检查
    enable_timeout_check: bool = True  # 启用超时检查
    batch_timeout: int = 300  # 批次超时时间（秒），5分钟
    max_retry_count: int = 3  # 最大重试次数
    
    # 可视化配置
    enable_console_display: bool = True  # 启用控制台显示
    enable_html_report: bool = True  # 启用HTML报告
    display_update_interval: int = 10  # 显示更新间隔（秒）
    html_refresh_interval: int = 30  # HTML刷新间隔（秒）
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "batch_monitor.log"
    enable_detailed_logging: bool = True
    
    # 性能配置
    max_concurrent_batches: int = 1  # 最大并发批次数
    memory_threshold: float = 0.8  # 内存使用阈值
    cpu_threshold: float = 0.9  # CPU使用阈值
    
    @property
    def remaining_symbols(self) -> int:
        """需要分批处理的币种数量"""
        return self.total_symbols - self.initial_pool_size
    
    @property
    def total_batches(self) -> int:
        """总批次数量"""
        return (self.remaining_symbols + self.batch_size - 1) // self.batch_size
    
    @property
    def estimated_cycle_time(self) -> int:
        """预估周期时间（秒）"""
        return self.total_batches * self.batch_interval
    
    def validate(self) -> bool:
        """验证配置有效性"""
        errors = []
        
        # 检查基本配置
        if self.batch_size <= 0:
            errors.append("batch_size 必须大于0")
        
        if self.total_symbols <= 0:
            errors.append("total_symbols 必须大于0")
        
        if self.initial_pool_size < 0:
            errors.append("initial_pool_size 不能为负数")
        
        # 检查时间配置
        if self.scan_interval <= 0:
            errors.append("scan_interval 必须大于0")
        
        if self.batch_interval <= 0:
            errors.append("batch_interval 必须大于0")
        
        if self.max_cycle_time <= self.estimated_cycle_time:
            errors.append(f"max_cycle_time ({self.max_cycle_time}) 应该大于预估周期时间 ({self.estimated_cycle_time})")
        
        # 检查监督配置
        if self.batch_timeout <= 0:
            errors.append("batch_timeout 必须大于0")
        
        if self.max_retry_count < 0:
            errors.append("max_retry_count 不能为负数")
        
        # 检查性能配置
        if not 0 < self.memory_threshold <= 1:
            errors.append("memory_threshold 必须在 (0, 1] 范围内")
        
        if not 0 < self.cpu_threshold <= 1:
            errors.append("cpu_threshold 必须在 (0, 1] 范围内")
        
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'batch_size': self.batch_size,
            'total_symbols': self.total_symbols,
            'initial_pool_size': self.initial_pool_size,
            'scan_interval': self.scan_interval,
            'batch_interval': self.batch_interval,
            'max_cycle_time': self.max_cycle_time,
            'enable_duplicate_check': self.enable_duplicate_check,
            'enable_timeout_check': self.enable_timeout_check,
            'batch_timeout': self.batch_timeout,
            'max_retry_count': self.max_retry_count,
            'enable_console_display': self.enable_console_display,
            'enable_html_report': self.enable_html_report,
            'display_update_interval': self.display_update_interval,
            'html_refresh_interval': self.html_refresh_interval,
            'log_level': self.log_level,
            'log_file': self.log_file,
            'enable_detailed_logging': self.enable_detailed_logging,
            'max_concurrent_batches': self.max_concurrent_batches,
            'memory_threshold': self.memory_threshold,
            'cpu_threshold': self.cpu_threshold
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BatchConfig':
        """从字典创建配置"""
        return cls(**data)
    
    def save_to_file(self, filepath: str):
        """保存配置到文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_from_file(cls, filepath: str) -> 'BatchConfig':
        """从文件加载配置"""
        if not os.path.exists(filepath):
            # 如果文件不存在，创建默认配置
            config = cls()
            config.save_to_file(filepath)
            return config
        
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return cls.from_dict(data)
    
    def print_summary(self):
        """打印配置摘要"""
        print("="*60)
        print("📋 批次处理配置摘要")
        print("="*60)
        print(f"🔢 基本配置:")
        print(f"   • 每批次处理: {self.batch_size} 个币种")
        print(f"   • 总币种数量: {self.total_symbols}")
        print(f"   • 初始候选池: {self.initial_pool_size} 个币种")
        print(f"   • 需分批处理: {self.remaining_symbols} 个币种")
        print(f"   • 总批次数量: {self.total_batches}")
        
        print(f"\n⏰ 时间配置:")
        print(f"   • 扫描间隔: {self.scan_interval} 秒 ({self.scan_interval//60} 分钟)")
        print(f"   • 批次间隔: {self.batch_interval} 秒")
        print(f"   • 预估周期时间: {self.estimated_cycle_time} 秒 ({self.estimated_cycle_time//60} 分钟)")
        print(f"   • 最大周期时间: {self.max_cycle_time} 秒 ({self.max_cycle_time//60} 分钟)")
        
        print(f"\n🛡️ 监督机制:")
        print(f"   • 重复扫描检查: {'✅' if self.enable_duplicate_check else '❌'}")
        print(f"   • 超时检查: {'✅' if self.enable_timeout_check else '❌'}")
        print(f"   • 批次超时: {self.batch_timeout} 秒 ({self.batch_timeout//60} 分钟)")
        print(f"   • 最大重试: {self.max_retry_count} 次")
        
        print(f"\n📊 可视化:")
        print(f"   • 控制台显示: {'✅' if self.enable_console_display else '❌'}")
        print(f"   • HTML报告: {'✅' if self.enable_html_report else '❌'}")
        print(f"   • 显示更新间隔: {self.display_update_interval} 秒")
        
        print("="*60)


# 预定义配置模板
class ConfigTemplates:
    """配置模板"""
    
    @staticmethod
    def get_fast_scan_config() -> BatchConfig:
        """快速扫描配置 - 适用于测试环境"""
        return BatchConfig(
            batch_size=50,  # 更大批次
            scan_interval=600,  # 10分钟间隔
            batch_interval=10,  # 10秒间隔
            max_cycle_time=300,  # 5分钟最大周期
            display_update_interval=5  # 5秒更新
        )
    
    @staticmethod
    def get_stable_scan_config() -> BatchConfig:
        """稳定扫描配置 - 适用于生产环境"""
        return BatchConfig(
            batch_size=20,  # 标准批次
            scan_interval=1800,  # 30分钟间隔
            batch_interval=30,  # 30秒间隔
            max_cycle_time=1500,  # 25分钟最大周期
            display_update_interval=10  # 10秒更新
        )
    
    @staticmethod
    def get_conservative_scan_config() -> BatchConfig:
        """保守扫描配置 - 适用于高负载环境"""
        return BatchConfig(
            batch_size=10,  # 小批次
            scan_interval=3600,  # 60分钟间隔
            batch_interval=60,  # 60秒间隔
            max_cycle_time=3000,  # 50分钟最大周期
            display_update_interval=30  # 30秒更新
        )


# 默认配置实例
DEFAULT_CONFIG = BatchConfig()

if __name__ == "__main__":
    # 测试配置
    config = BatchConfig()
    
    print("默认配置:")
    config.print_summary()
    
    print(f"\n配置验证: {'✅ 通过' if config.validate() else '❌ 失败'}")
    
    # 保存和加载测试
    config_file = "batch_config.json"
    config.save_to_file(config_file)
    loaded_config = BatchConfig.load_from_file(config_file)
    
    print(f"\n配置保存和加载: {'✅ 成功' if config.to_dict() == loaded_config.to_dict() else '❌ 失败'}")
    
    # 清理测试文件
    if os.path.exists(config_file):
        os.remove(config_file)