#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml
import time
from strategy.maker_channel import MakerChannelStrategy
from binance_trader import BinanceTrader

def manual_clean_aweusdt_orphan():
    """手动清理AWEUSDT的孤儿订单"""
    try:
        # 读取配置
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 初始化交易器
        trader = BinanceTrader(config)
        
        # 初始化策略（用于访问清理方法）
        strategy = MakerChannelStrategy(trader, config)
        
        print("=== 开始手动清理AWEUSDT孤儿订单 ===")
        
        # 1. 检查当前AWEUSDT持仓
        position = trader.get_position('AWEUSDT')
        position_amt = float(position['positionAmt']) if position else 0
        print(f"AWEUSDT当前持仓: {position_amt}")
        
        # 2. 检查开放订单
        open_orders = trader.http.get('/fapi/v1/openOrders', {'symbol': 'AWEUSDT'})
        print(f"AWEUSDT开放订单数量: {len(open_orders) if open_orders else 0}")
        
        if open_orders:
            for order in open_orders:
                print(f"  订单ID: {order['orderId']}, 类型: {order['type']}, 方向: {order['side']}, 状态: {order['status']}")
        
        # 3. 检查孤儿止损/止盈单
        orphan_orders = strategy.check_orphan_stop_orders('AWEUSDT')
        print(f"发现孤儿止损/止盈单数量: {len(orphan_orders)}")
        
        if orphan_orders:
            for orphan in orphan_orders:
                print(f"  孤儿订单: {orphan['orderId']} ({orphan['type']})")
        
        # 4. 执行清理
        if abs(position_amt) < 0.001 and open_orders:
            print("\n=== 开始清理孤儿订单 ===")
            
            for order in open_orders:
                order_id = order['orderId']
                order_type = order['type']
                
                if order_type in ['STOP_MARKET', 'TAKE_PROFIT_MARKET']:
                    print(f"正在取消孤儿{order_type}单: {order_id}")
                    
                    # 使用策略的确认取消方法
                    success = strategy.cancel_order_with_confirmation('AWEUSDT', order_id)
                    
                    if success:
                        print(f"✅ 成功取消订单: {order_id}")
                    else:
                        print(f"❌ 取消订单失败: {order_id}")
                        
                        # 尝试直接取消
                        print(f"尝试直接取消订单: {order_id}")
                        try:
                            cancel_result = trader.cancel_order('AWEUSDT', order_id)
                            if cancel_result:
                                print(f"✅ 直接取消成功: {order_id}")
                            else:
                                print(f"❌ 直接取消也失败: {order_id}")
                        except Exception as e:
                            print(f"❌ 直接取消异常: {e}")
                    
                    time.sleep(1)  # 避免频繁操作
        
        # 5. 验证清理结果
        print("\n=== 验证清理结果 ===")
        time.sleep(2)
        
        final_orders = trader.http.get('/fapi/v1/openOrders', {'symbol': 'AWEUSDT'})
        print(f"清理后AWEUSDT开放订单数量: {len(final_orders) if final_orders else 0}")
        
        if final_orders:
            print("⚠️ 仍有未清理的订单:")
            for order in final_orders:
                print(f"  订单ID: {order['orderId']}, 类型: {order['type']}, 状态: {order['status']}")
        else:
            print("✅ 所有孤儿订单已清理完成")
            
    except Exception as e:
        print(f"清理过程发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    manual_clean_aweusdt_orphan()