import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class PositionManager:
    def __init__(self, config: dict, cache_manager):
        self.config = config
        self.cache_manager = cache_manager
        self.max_positions = config['strategy']['max_positions']
        self.max_additions = config['strategy']['max_additions']
        self.position_size_usd = config['strategy']['position_size_usd']
        self.daily_close_time = datetime.strptime(config['strategy']['daily_close_time'], "%H:%M").time()
    
    def get_positions(self) -> Dict[str, Any]:
        """获取当前持仓"""
        return self.cache_manager.load_positions()
    
    def save_positions(self, positions: Dict[str, Any]):
        """保存持仓信息"""
        self.cache_manager.save_positions(positions)
    
    def update_position(self, symbol: str, position_data: Dict[str, Any]):
        """更新持仓信息"""
        positions = self.get_positions()
        positions[symbol] = position_data
        self.save_positions(positions)
    
    def remove_position(self, symbol: str):
        """移除持仓"""
        positions = self.get_positions()
        if symbol in positions:
            del positions[symbol]
            self.save_positions(positions)
    
    def can_open_new_position(self) -> bool:
        """检查是否可以开新仓"""
        positions = self.get_positions()
        return len(positions) < self.max_positions
    
    def can_add_to_position(self, symbol: str) -> bool:
        """检查是否可以加仓"""
        positions = self.get_positions()
        if symbol not in positions:
            return False
        
        position = positions[symbol]
        return position.get('addition_count', 0) < self.max_additions
    
    def calculate_position_size(self, symbol: str, price: float) -> float:
        """计算仓位大小"""
        try:
            # 固定名义金额/价格
            size = self.position_size_usd / price
            logger.info(f"计算仓位: {symbol}, 价格={price}, 仓位={size}")
            return size
        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            return 0.0
    
    def should_close_all_positions(self) -> bool:
        """检查是否应该平仓所有头寸"""
        now = datetime.now().time()
        # 简单实现：检查是否到了每日平仓时间
        # 在实际应用中，可能需要更复杂的逻辑
        return now >= self.daily_close_time and (datetime.now() - timedelta(minutes=1)).time() < self.daily_close_time
    
    def update_stop_loss(self, symbol: str, current_price: float, entry_price: float):
        """更新止损位"""
        positions = self.get_positions()
        if symbol not in positions:
            return
        
        position = positions[symbol]
        stop_loss_pct = self.config['strategy']['stop_loss_pct']
        
        # 移动止损逻辑
        if position.get('has_moved_stop', False):
            # 如果已经移动过止损，继续跟踪
            new_stop = current_price * (1 - self.config['strategy']['trailing_stop_pct'])
            if new_stop > position.get('stop_loss', 0):
                position['stop_loss'] = new_stop
                logger.info(f"更新移动止损: {symbol}, 新止损={new_stop}")
        elif (current_price / entry_price) - 1 > 0.02:  # 2%盈利后移动止损
            position['has_moved_stop'] = True
            position['stop_loss'] = entry_price * 1.004  # 0.4%的保护
            logger.info(f"激活移动止损: {symbol}, 止损={position['stop_loss']}")
        
        positions[symbol] = position
        self.save_positions(positions)