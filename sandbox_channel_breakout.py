#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通道突破 + Freshness 过滤 - 完整可落地代码
独立运行，不依赖现有策略框架
只需要 pandas 和 ta 库
"""

import pandas as pd
import numpy as np
try:
    import talib as ta
except ImportError:
    print("警告: talib未安装，使用简化版ATR计算")
    ta = None

def simple_atr(high, low, close, period=14):
    """简化版ATR计算（当talib不可用时）"""
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    return tr.rolling(window=period).mean()

def calculate_channel(df, period=24, atr_mult=1.2):
    """
    计算均线通道
    
    参数:
        df: DataFrame，包含 'h', 'l', 'c', 'v' 列
        period: 通道周期（根数）
        atr_mult: ATR倍数
    
    返回:
        mid, upper, lower: 中轨、上轨、下轨
    """
    # 中轨 = EMA
    mid = df['c'].ewm(span=period).mean()
    
    # ATR计算
    if ta is not None:
        atr = ta.ATR(df['h'].values, df['l'].values, df['c'].values, timeperiod=14)
        atr = pd.Series(atr, index=df.index)
    else:
        atr = simple_atr(df['h'], df['l'], df['c'], 14)
    
    # 上下轨
    upper = mid + atr * atr_mult
    lower = mid - atr * atr_mult
    
    return mid, upper, lower

def check_breakout_with_freshness(df, max_gap_pct=5.0, max_bars=3):
    """
    检查通道突破 + Freshness过滤
    
    参数:
        df: K线数据
        max_gap_pct: 最大突破幅度（%）
        max_bars: 最大突破时间（根数）
    
    返回:
        dict: 包含突破状态、突破幅度、突破时间等信息
    """
    if len(df) < 50:
        return {
            'breakout': False,
            'reason': 'K线数据不足',
            'gap_pct': 0,
            'bars_since': 0
        }
    
    # 计算通道
    mid, upper, lower = calculate_channel(df)
    
    # 当前价格
    current_price = df['c'].iloc[-1]
    current_upper = upper.iloc[-1]
    
    # 检查是否突破上轨
    if current_price <= current_upper:
        return {
            'breakout': False,
            'reason': '未突破上轨',
            'gap_pct': (current_price - current_upper) / current_upper * 100,
            'bars_since': 0,
            'current_price': current_price,
            'upper_band': current_upper
        }
    
    # 计算突破幅度
    gap_pct = (current_price - current_upper) / current_upper * 100
    
    # 检查突破幅度是否过大（防止追高）
    if gap_pct > max_gap_pct:
        return {
            'breakout': False,
            'reason': f'突破幅度过大 ({gap_pct:.2f}% > {max_gap_pct}%)',
            'gap_pct': gap_pct,
            'bars_since': 0,
            'current_price': current_price,
            'upper_band': current_upper
        }
    
    # 查找首次突破的时间点
    bars_since_breakout = 0
    for i in range(len(df) - 1, max(0, len(df) - max_bars - 1), -1):
        if df['c'].iloc[i] > upper.iloc[i]:
            bars_since_breakout += 1
        else:
            break
    
    # 检查突破时间是否过久（防止追高）
    if bars_since_breakout > max_bars:
        return {
            'breakout': False,
            'reason': f'突破时间过久 ({bars_since_breakout}根 > {max_bars}根)',
            'gap_pct': gap_pct,
            'bars_since': bars_since_breakout,
            'current_price': current_price,
            'upper_band': current_upper
        }
    
    # 检查成交量确认
    vol_avg = df['v'].rolling(20).mean().iloc[-1]
    current_vol = df['v'].iloc[-1]
    vol_ratio = current_vol / vol_avg if vol_avg > 0 else 0
    
    if vol_ratio < 1.5:  # 成交量不足1.5倍
        return {
            'breakout': False,
            'reason': f'成交量不足 ({vol_ratio:.2f}x < 1.5x)',
            'gap_pct': gap_pct,
            'bars_since': bars_since_breakout,
            'vol_ratio': vol_ratio,
            'current_price': current_price,
            'upper_band': current_upper
        }
    
    # 通过所有检查
    return {
        'breakout': True,
        'reason': '通过所有检查',
        'gap_pct': gap_pct,
        'bars_since': bars_since_breakout,
        'vol_ratio': vol_ratio,
        'current_price': current_price,
        'upper_band': current_upper
    }

def generate_sample_data(symbol="TESTUSDT", bars=100):
    """
    生成示例K线数据（用于测试）
    """
    np.random.seed(42)
    
    # 生成基础价格走势
    base_price = 100
    price_changes = np.random.normal(0, 0.02, bars)  # 2%波动
    
    # 添加趋势（最后20根K线上涨）
    trend = np.zeros(bars)
    trend[-20:] = np.linspace(0, 0.1, 20)  # 最后20根K线10%上涨趋势
    
    prices = [base_price]
    for i in range(1, bars):
        change = price_changes[i] + trend[i]
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1))  # 价格不能为负
    
    # 生成OHLCV数据
    data = []
    for i, close in enumerate(prices):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = (high + low) / 2 + np.random.normal(0, (high - low) * 0.1)
        volume = np.random.uniform(1000, 10000)
        
        # 最后几根K线放量
        if i >= bars - 5:
            volume *= np.random.uniform(2, 4)
        
        data.append({
            'timestamp': pd.Timestamp.now() - pd.Timedelta(minutes=(bars-i)*15),
            'o': open_price,
            'h': high,
            'l': low,
            'c': close,
            'v': volume
        })
    
    return pd.DataFrame(data)

def test_breakout_logic():
    """
    测试突破逻辑
    """
    print("=== 通道突破 + Freshness 过滤测试 ===")
    
    # 生成测试数据
    df = generate_sample_data("TESTUSDT", 100)
    
    print(f"\n生成了 {len(df)} 根K线数据")
    print(f"价格范围: {df['c'].min():.4f} - {df['c'].max():.4f}")
    print(f"当前价格: {df['c'].iloc[-1]:.4f}")
    
    # 计算通道
    mid, upper, lower = calculate_channel(df)
    
    print(f"\n通道信息:")
    print(f"  中轨: {mid.iloc[-1]:.4f}")
    print(f"  上轨: {upper.iloc[-1]:.4f}")
    print(f"  下轨: {lower.iloc[-1]:.4f}")
    
    # 测试不同参数的突破检查
    test_configs = [
        {'max_gap_pct': 5.0, 'max_bars': 3, 'name': '标准配置'},
        {'max_gap_pct': 2.0, 'max_bars': 2, 'name': '严格配置'},
        {'max_gap_pct': 10.0, 'max_bars': 5, 'name': '宽松配置'}
    ]
    
    for config in test_configs:
        print(f"\n=== {config['name']} ===")
        result = check_breakout_with_freshness(
            df, 
            max_gap_pct=config['max_gap_pct'],
            max_bars=config['max_bars']
        )
        
        print(f"突破状态: {result['breakout']}")
        print(f"原因: {result['reason']}")
        if 'gap_pct' in result:
            print(f"突破幅度: {result['gap_pct']:.2f}%")
        if 'bars_since' in result:
            print(f"突破时间: {result['bars_since']}根K线")
        if 'vol_ratio' in result:
            print(f"成交量倍数: {result['vol_ratio']:.2f}x")

def create_config_template():
    """
    创建配置模板
    """
    config = {
        'channel': {
            'period': 24,        # 通道周期（根数）
            'atr_mult': 1.2,     # ATR倍数
        },
        'breakout_filter': {
            'max_gap_pct': 5.0,  # 最大突破幅度（%）
            'max_bars': 3,       # 最大突破时间（根数）
            'vol_mult': 1.5,     # 成交量倍数要求
        }
    }
    
    print("\n=== 配置模板 ===")
    print("# 通道突破配置")
    print(f"channel_period: {config['channel']['period']}")
    print(f"atr_multiplier: {config['channel']['atr_mult']}")
    print(f"max_breakout_gap: {config['breakout_filter']['max_gap_pct']}%")
    print(f"max_breakout_bars: {config['breakout_filter']['max_bars']}")
    print(f"volume_multiplier: {config['breakout_filter']['vol_mult']}x")
    
    return config

if __name__ == '__main__':
    # 运行测试
    test_breakout_logic()
    
    # 显示配置模板
    create_config_template()
    
    print("\n=== 使用说明 ===")
    print("1. 复制 check_breakout_with_freshness() 函数到你的策略中")
    print("2. 在 score_symbol() 开头调用该函数")
    print("3. 只有返回 breakout=True 的币种才进入评分")
    print("4. 调整配置参数以适应不同市场环境")
    print("\n参数调整建议:")
    print("- 牛市: max_gap_pct=3%, max_bars=2 (更严格)")
    print("- 熊市: max_gap_pct=8%, max_bars=5 (更宽松)")
    print("- 震荡: max_gap_pct=5%, max_bars=3 (标准)")