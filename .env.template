# 环境变量配置文件
# 请复制此文件为 .env 并填入实际值

# Binance API 配置
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# 数据库配置（如果使用）
DATABASE_URL=sqlite:///strategy.db
DATABASE_PASSWORD=your_database_password

# Redis配置（如果使用）
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password

# 邮件通知配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
NOTIFICATION_EMAIL=<EMAIL>

# 钉钉/企业微信通知配置
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=your_token
WECHAT_WEBHOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key

# 系统配置
ENVIRONMENT=production  # development, testing, production
LOG_LEVEL=INFO         # DEBUG, INFO, WARNING, ERROR
DEBUG_MODE=false

# 安全配置
SECRET_KEY=your_secret_key_for_encryption
JWT_SECRET=your_jwt_secret_key

# 第三方服务配置
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_URL=http://localhost:3000

# 备份配置
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key
BACKUP_REMOTE_PATH=s3://your-bucket/backups/

# 注意事项：
# 1. 请勿将此文件提交到版本控制系统
# 2. 确保 .env 文件权限设置为 600 (仅所有者可读写)
# 3. 定期更换API密钥和密码
# 4. 在生产环境中使用强密码