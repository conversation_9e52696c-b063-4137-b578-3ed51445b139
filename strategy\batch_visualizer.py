"""
批次处理进度可视化组件
实时显示批次处理状态和异常情况
"""

import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

class BatchVisualizer:
    """批次处理可视化器"""
    
    def __init__(self, batch_monitor, update_interval: int = 10):
        self.batch_monitor = batch_monitor
        self.update_interval = update_interval
        self.log = logging.getLogger("BatchVisualizer")
        
        # 可视化状态
        self.display_active = False
        self.display_thread = None
        
        # 显示配置
        self.console_width = 80
        self.show_details = True
        
    def start_display(self):
        """开始显示进度"""
        if self.display_active:
            return
        
        self.display_active = True
        self.display_thread = threading.Thread(target=self._display_loop, daemon=True)
        self.display_thread.start()
        self.log.info("批次进度可视化已启动")
    
    def stop_display(self):
        """停止显示进度"""
        self.display_active = False
        if self.display_thread and self.display_thread.is_alive():
            self.display_thread.join(timeout=2)
        self.log.info("批次进度可视化已停止")
    
    def _display_loop(self):
        """显示循环"""
        while self.display_active:
            try:
                self._update_display()
                time.sleep(self.update_interval)
            except Exception as e:
                self.log.error(f"显示更新异常: {e}")
                time.sleep(5)
    
    def _update_display(self):
        """更新显示内容"""
        status = self.batch_monitor.get_progress_status()
        
        if status.get("status") == "no_active_cycle":
            self._display_idle_status()
        else:
            self._display_active_status(status)
    
    def _display_idle_status(self):
        """显示空闲状态"""
        stats = self.batch_monitor.get_scan_statistics()
        
        print("\n" + "="*self.console_width)
        print("📊 批次处理监控系统 - 空闲状态")
        print("="*self.console_width)
        print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📈 历史统计:")
        print(f"   • 总扫描周期: {stats['total_cycles']}")
        print(f"   • 完成周期: {stats['completed_cycles']}")
        print(f"   • 平均周期时间: {stats['average_cycle_time']:.1f}秒")
        print(f"   • 平均处理币种: {stats['average_symbols_per_cycle']:.0f}个")
        if stats['total_duplicate_scans'] > 0:
            print(f"⚠️  重复扫描: {stats['total_duplicate_scans']}次 ({stats['unique_duplicated_symbols']}个币种)")
        print("="*self.console_width)
    
    def _display_active_status(self, status: Dict):
        """显示活跃状态"""
        print("\n" + "="*self.console_width)
        print("🚀 批次处理监控系统 - 运行中")
        print("="*self.console_width)
        
        # 基本信息
        print(f"🔄 扫描周期: {status['cycle_id']}")
        print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  运行时间: {self._format_duration(status['elapsed_time'])}")
        
        # 进度条
        progress = status['progress_percentage']
        self._display_progress_bar(progress)
        
        # 统计信息
        print(f"\n📊 处理统计:")
        print(f"   • 总币种数: {status['total_symbols']}")
        print(f"   • 已处理: {status['processed_symbols']}")
        print(f"   • 达标币种: {status['qualified_symbols']}")
        print(f"   • 总批次数: {status['total_batches']}")
        
        # 批次状态
        print(f"\n🔢 批次状态:")
        print(f"   • ✅ 已完成: {status['completed_batches']}")
        print(f"   • 🔄 处理中: {status['processing_batches']}")
        print(f"   • ❌ 失败: {status['failed_batches']}")
        print(f"   • ⏳ 待处理: {status['total_batches'] - status['completed_batches'] - status['processing_batches'] - status['failed_batches']}")
        
        # 预估时间
        if status['estimated_remaining'] > 0:
            print(f"⏰ 预计剩余时间: {self._format_duration(status['estimated_remaining'])}")
        
        # 异常情况
        if status['duplicate_scans'] > 0:
            print(f"⚠️  重复扫描检测: {status['duplicate_scans']}个币种")
        
        # 详细批次信息
        if self.show_details:
            self._display_batch_details(status['current_batch_details'])
        
        print("="*self.console_width)
    
    def _display_progress_bar(self, percentage: float):
        """显示进度条"""
        bar_length = 50
        filled_length = int(bar_length * percentage / 100)
        
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        print(f"\n📈 总体进度: [{bar}] {percentage:.1f}%")
    
    def _display_batch_details(self, batch_details: List[Dict]):
        """显示批次详情"""
        if not batch_details:
            return
        
        print(f"\n🔍 批次详情:")
        
        # 显示最近的几个批次
        recent_batches = sorted(batch_details, key=lambda x: x['batch_index'])[-5:]
        
        for batch in recent_batches:
            status_icon = self._get_status_icon(batch['status'])
            processing_time = batch['processing_time']
            
            print(f"   {status_icon} 批次 {batch['batch_index']}: "
                  f"{batch['processed_count']}/{batch['symbols_count']} 币种 "
                  f"({processing_time:.1f}s)")
            
            if batch['failed_count'] > 0:
                print(f"      ❌ 失败: {batch['failed_count']} 个币种")
    
    def _get_status_icon(self, status: str) -> str:
        """获取状态图标"""
        icons = {
            "pending": "⏳",
            "processing": "🔄",
            "completed": "✅",
            "failed": "❌"
        }
        return icons.get(status, "❓")
    
    def _format_duration(self, seconds: float) -> str:
        """格式化时间长度"""
        if seconds < 60:
            return f"{seconds:.0f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"
    
    def generate_html_report(self) -> str:
        """生成HTML进度报告"""
        status = self.batch_monitor.get_progress_status()
        stats = self.batch_monitor.get_scan_statistics()
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次处理进度报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }}
        .status-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }}
        .status-card {{ background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }}
        .progress-bar {{ width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }}
        .progress-fill {{ height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }}
        .batch-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .batch-table th, .batch-table td {{ padding: 10px; text-align: left; border-bottom: 1px solid #dee2e6; }}
        .batch-table th {{ background-color: #007bff; color: white; }}
        .status-badge {{ padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; }}
        .status-completed {{ background-color: #28a745; }}
        .status-processing {{ background-color: #ffc107; color: #212529; }}
        .status-failed {{ background-color: #dc3545; }}
        .status-pending {{ background-color: #6c757d; }}
        .alert {{ padding: 15px; margin: 10px 0; border-radius: 4px; }}
        .alert-warning {{ background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }}
        .timestamp {{ text-align: center; color: #6c757d; margin-top: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 批次处理进度监控</h1>
            <p>实时监控批次扫描状态和处理进度</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>📊 当前状态</h3>
                <p><strong>扫描周期:</strong> {status.get('cycle_id', '无活跃周期')}</p>
                <p><strong>运行状态:</strong> {status.get('status', '空闲')}</p>
                <p><strong>运行时间:</strong> {self._format_duration(status.get('elapsed_time', 0))}</p>
            </div>
            
            <div class="status-card">
                <h3>🔢 处理统计</h3>
                <p><strong>总币种:</strong> {status.get('total_symbols', 0)}</p>
                <p><strong>已处理:</strong> {status.get('processed_symbols', 0)}</p>
                <p><strong>达标币种:</strong> {status.get('qualified_symbols', 0)}</p>
            </div>
            
            <div class="status-card">
                <h3>📈 批次进度</h3>
                <p><strong>总批次:</strong> {status.get('total_batches', 0)}</p>
                <p><strong>已完成:</strong> {status.get('completed_batches', 0)}</p>
                <p><strong>处理中:</strong> {status.get('processing_batches', 0)}</p>
                <p><strong>失败:</strong> {status.get('failed_batches', 0)}</p>
            </div>
            
            <div class="status-card">
                <h3>📊 历史统计</h3>
                <p><strong>总周期:</strong> {stats.get('total_cycles', 0)}</p>
                <p><strong>完成周期:</strong> {stats.get('completed_cycles', 0)}</p>
                <p><strong>平均时间:</strong> {self._format_duration(stats.get('average_cycle_time', 0))}</p>
            </div>
        </div>
        
        <div class="progress-section">
            <h3>📈 总体进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {status.get('progress_percentage', 0):.1f}%"></div>
            </div>
            <p style="text-align: center;">{status.get('progress_percentage', 0):.1f}% 完成</p>
        </div>
        
        {self._generate_batch_table_html(status.get('current_batch_details', []))}
        
        {self._generate_alerts_html(status)}
        
        <div class="timestamp">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>自动刷新间隔: {self.update_interval}秒</p>
        </div>
    </div>
    
    <script>
        // 自动刷新页面
        setTimeout(function() {{
            location.reload();
        }}, {self.update_interval * 1000});
    </script>
</body>
</html>
        """
        
        # 保存HTML文件
        html_file = "batch_progress.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_file
    
    def _generate_batch_table_html(self, batch_details: List[Dict]) -> str:
        """生成批次表格HTML"""
        if not batch_details:
            return ""
        
        table_html = """
        <div class="batch-section">
            <h3>🔍 批次详情</h3>
            <table class="batch-table">
                <thead>
                    <tr>
                        <th>批次</th>
                        <th>状态</th>
                        <th>币种数量</th>
                        <th>已处理</th>
                        <th>失败</th>
                        <th>处理时间</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for batch in sorted(batch_details, key=lambda x: x['batch_index']):
            status_class = f"status-{batch['status']}"
            status_text = {
                'pending': '待处理',
                'processing': '处理中',
                'completed': '已完成',
                'failed': '失败'
            }.get(batch['status'], batch['status'])
            
            table_html += f"""
                    <tr>
                        <td>批次 {batch['batch_index']}</td>
                        <td><span class="status-badge {status_class}">{status_text}</span></td>
                        <td>{batch['symbols_count']}</td>
                        <td>{batch['processed_count']}</td>
                        <td>{batch['failed_count']}</td>
                        <td>{batch['processing_time']:.1f}秒</td>
                    </tr>
            """
        
        table_html += """
                </tbody>
            </table>
        </div>
        """
        
        return table_html
    
    def _generate_alerts_html(self, status: Dict) -> str:
        """生成警告信息HTML"""
        alerts = []
        
        # 检查重复扫描
        if status.get('duplicate_scans', 0) > 0:
            alerts.append(f"⚠️ 检测到 {status['duplicate_scans']} 个币种重复扫描")
        
        # 检查失败率
        total_batches = status.get('total_batches', 0)
        failed_batches = status.get('failed_batches', 0)
        if total_batches > 0 and failed_batches / total_batches > 0.1:
            alerts.append(f"⚠️ 批次失败率较高: {failed_batches}/{total_batches} ({failed_batches/total_batches*100:.1f}%)")
        
        # 检查处理时间
        if status.get('elapsed_time', 0) > 1800:  # 30分钟
            alerts.append("⚠️ 扫描周期运行时间过长，可能存在性能问题")
        
        if not alerts:
            return ""
        
        alerts_html = '<div class="alerts-section"><h3>⚠️ 异常提醒</h3>'
        for alert in alerts:
            alerts_html += f'<div class="alert alert-warning">{alert}</div>'
        alerts_html += '</div>'
        
        return alerts_html