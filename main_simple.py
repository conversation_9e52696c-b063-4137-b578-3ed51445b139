import yaml
import logging
import warnings
import urllib3
import time
import asyncio
import pickle
import os
from pathlib import Path
from binance_trader import BinanceTrader
from strategy.maker_channel import MakerChannelStrategy
from cache_manager import CacheManager

# 完全禁用所有警告
warnings.filterwarnings("ignore")
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置控制台编码为UTF-8
import sys
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 配置详细日志格式（控制台+文件）
console_handler = logging.StreamHandler()
console_handler.setStream(sys.stdout)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler('strategy.log', encoding='utf-8'),
        console_handler
    ]
)

# 设置ReferenceFolder相关日志器的级别为DEBUG以显示评分详情
logging.getLogger('ReferenceFolder.symbol_scorer').setLevel(logging.DEBUG)

def _initialize_cand_cache(strategy, cache_manager):
    """初始化候选池缓存"""
    cand_cache_file = Path("cache/cand_cache.pkl")
    
    if cand_cache_file.exists():
        try:
            with open(cand_cache_file, 'rb') as f:
                cand_cache = pickle.load(f)
            
            # 验证缓存数据结构
            if isinstance(cand_cache, dict) and 'candidates' in cand_cache:
                strategy.cand_cache = cand_cache
                logging.info(f"成功加载候选池缓存，包含 {len(cand_cache['candidates'])} 个候选")
                
                # 显示缓存中的候选池信息
                for symbol, data in cand_cache['candidates'].items():
                    logging.info(f"缓存候选: {symbol} - 评分: {data.get('score', 'N/A')}")
                
                return True
            else:
                logging.warning("候选池缓存格式无效，将重新初始化")
                
        except Exception as e:
            logging.error(f"加载候选池缓存失败: {e}")
    
    # 初始化空的候选池缓存
    strategy.cand_cache = {
        'candidates': {},
        'last_update': time.time(),
        'metadata': {
            'version': '1.0',
            'created_at': time.time()
        }
    }
    logging.info("初始化空的候选池缓存")
    return False

def _save_cand_cache_to_file(strategy):
    """保存候选池缓存到文件"""
    try:
        cand_cache_file = Path("cache/cand_cache.pkl")
        cand_cache_file.parent.mkdir(exist_ok=True)
        
        with open(cand_cache_file, 'wb') as f:
            pickle.dump(strategy.cand_cache, f)
        
        logging.info(f"候选池缓存已保存到 {cand_cache_file}")
        
    except Exception as e:
        logging.error(f"保存候选池缓存失败: {e}")

def _sync_candidate_data(strategy, cache_manager):
    """同步候选池数据，确保 candidates.pkl 和 cand_cache.pkl 一致"""
    try:
        # 从 cand_cache 提取候选池数据
        candidates = strategy.cand_cache.get('candidates', {})
        
        # 保存到 candidates.pkl
        cache_manager.save_candidates(candidates)
        
        logging.info(f"候选池数据同步完成，共 {len(candidates)} 个候选")
        
    except Exception as e:
        logging.error(f"候选池数据同步失败: {e}")

def main():
    """主函数 - 简化版本，跳过网络环境检测"""
    # 加载基础配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    # 跳过网络环境检测，使用配置文件中的静态代理设置
    logging.info("使用简化启动模式，跳过网络环境检测")
    
    # 初始化缓存管理器
    cache_manager = CacheManager()
    
    # 初始化交易器
    trader = BinanceTrader(cfg)
    
    # 执行冷启动：加载全币种基础信息
    logging.info("执行冷启动：加载全币种基础信息...")
    symbols_info = trader.get_all_futures()
    logging.info(f"冷启动完成，加载 {len(symbols_info)} 个交易对信息")
    
    # 初始化策略
    strategy = MakerChannelStrategy(trader, cfg)
    
    # 检查并初始化 cand_cache
    _initialize_cand_cache(strategy, cache_manager)
    
    # 执行启动预热扫描
    logging.info("执行启动预热扫描（涨幅榜前25 + 成交量榜前25）...")
    strategy.warmup()
    
    # 保存预热后的候选池到 cand_cache.pkl
    _save_cand_cache_to_file(strategy)
    
    # 同步候选池数据，确保 candidates.pkl 和 cand_cache.pkl 一致
    _sync_candidate_data(strategy, cache_manager)
    
    # 主循环使用完整的策略循环
    logging.info("启动策略主循环...")
    asyncio.run(strategy.loop())

if __name__ == '__main__':
    main()