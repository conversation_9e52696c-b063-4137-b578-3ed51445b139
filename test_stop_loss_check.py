#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的止损单检查测试
"""

import os
import sys
import logging
from datetime import datetime
import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def test_stop_loss_check():
    """简化的止损单检查测试"""
    log = logging.getLogger('test')
    
    try:
        log.info("开始简化的止损单检查测试...")
        
        # 直接使用环境变量中的API密钥进行简单测试
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        if not api_key or not api_secret:
            log.error("缺少API密钥环境变量")
            return 1
            
        log.info(f"API Key长度: {len(api_key)}")
        log.info(f"API Secret长度: {len(api_secret)}")
        
        # 简单的HTTP请求测试
        import hmac
        import hashlib
        import time
        from urllib.parse import urlencode
        
        base_url = 'https://fapi.binance.com'
        timestamp = int(time.time() * 1000)
        
        # 测试获取持仓
        params = {'timestamp': timestamp}
        query_string = urlencode(params)
        signature = hmac.new(api_secret.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()
        params['signature'] = signature
        
        headers = {
            'X-MBX-APIKEY': api_key
        }
        
        url = f"{base_url}/fapi/v2/positionRisk"
        response = requests.get(url, params=params, headers=headers)
        
        log.info(f"API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            positions = response.json()
            log.info(f"获取到 {len(positions)} 个持仓记录")
            
            active_positions = []
            for pos in positions:
                qty = float(pos.get('positionAmt', 0))
                if abs(qty) > 0.001:
                    active_positions.append(pos)
                    symbol = pos.get('symbol')
                    log.info(f"活跃持仓: {symbol}, 数量: {qty}")
            
            log.info(f"共有 {len(active_positions)} 个活跃持仓")
            return 0
        else:
            log.error(f"API请求失败: {response.text}")
            return 1
            
    except Exception as e:
        log.error(f"测试失败: {e}")
        import traceback
        log.error(traceback.format_exc())
        return 1

if __name__ == '__main__':
    exit_code = test_stop_loss_check()
    sys.exit(exit_code)