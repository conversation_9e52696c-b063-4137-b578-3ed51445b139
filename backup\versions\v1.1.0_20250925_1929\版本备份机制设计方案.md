# 版本备份机制设计方案

## 📋 备份机制总体设计

### 🎯 设计目标
1. **快速回滚**：5分钟内完成版本回滚
2. **数据完整性**：确保所有关键文件和配置的完整备份
3. **自动化执行**：最小化人工干预，支持脚本化操作
4. **版本管理**：支持多版本备份和选择性回滚
5. **风险控制**：备份前验证，回滚后确认

### 🏗️ 备份架构设计

```
e:\allmace\
├── backup\                    # 备份根目录
│   ├── versions\              # 版本备份目录
│   │   ├── v1.0_20250125_1430\    # 版本备份（格式：版本号_日期_时间）
│   │   │   ├── strategy\          # 策略代码备份
│   │   │   ├── config\            # 配置文件备份
│   │   │   ├── cache\             # 缓存数据备份
│   │   │   ├── logs\              # 日志文件备份
│   │   │   └── metadata.json     # 版本元数据
│   │   └── v1.1_20250125_1630\    # 新版本备份
│   ├── scripts\               # 备份和回滚脚本
│   │   ├── backup.py          # 备份执行脚本
│   │   ├── rollback.py        # 回滚执行脚本
│   │   └── verify.py          # 验证脚本
│   └── logs\                  # 备份操作日志
│       ├── backup.log         # 备份操作记录
│       └── rollback.log       # 回滚操作记录
```

## 📂 关键文件备份清单

### 🔧 核心策略文件（必备）
```
strategy/
├── maker_channel.py           # 主策略文件
├── maker_channel_backup.py    # 备份策略文件
└── __init__.py               # 模块初始化文件
```

### ⚙️ 系统配置文件（必备）
```
config/
├── config.json               # 主配置文件
├── trading_config.json       # 交易配置
├── risk_config.json          # 风险控制配置
└── api_config.json           # API配置（敏感信息需加密）
```

### 🔄 核心业务文件（必备）
```
根目录文件：
├── binance_trader.py         # 交易接口
├── cache_manager.py          # 缓存管理
├── http_client.py            # HTTP客户端
├── main.py                   # 主程序入口
└── requirements.txt          # 依赖清单
```

### 📊 数据和日志文件（重要）
```
cache/
├── candidates.pkl            # 候选池数据
├── symbols.json              # 交易对信息
└── depth_*.json             # 深度数据（选择性备份）

日志文件：
├── strategy.log              # 策略运行日志
└── 其他*.log文件             # 系统日志
```

### 📋 文档和脚本文件（可选）
```
文档：
├── *.md                      # 文档文件
└── doc/                      # 文档目录

脚本：
├── *.py                      # 工具脚本
└── test_*.py                 # 测试脚本
```

## 🔄 备份执行流程

### 阶段1：备份前准备
1. **创建备份目录结构**
2. **生成版本标识**：`v{版本号}_{YYYYMMDD}_{HHMM}`
3. **检查磁盘空间**：确保至少有1GB可用空间
4. **记录当前系统状态**：运行状态、持仓信息、活跃订单

### 阶段2：文件备份执行
1. **停止策略运行**（可选，根据备份类型）
2. **复制核心文件**：按优先级顺序备份
3. **压缩大文件**：缓存数据和日志文件
4. **生成校验和**：确保文件完整性
5. **创建元数据文件**：记录备份信息

### 阶段3：备份后验证
1. **文件完整性检查**：校验和验证
2. **配置文件语法检查**：JSON格式验证
3. **依赖关系检查**：import语句验证
4. **生成备份报告**：记录备份结果

## 📝 元数据文件格式

### metadata.json 结构
```json
{
  "backup_info": {
    "version": "v1.0",
    "timestamp": "2025-01-25T14:30:00Z",
    "backup_type": "full",
    "created_by": "auto_backup_system",
    "description": "策略优化前的完整备份"
  },
  "system_state": {
    "strategy_running": true,
    "active_positions": [
      {
        "symbol": "1000PEPEUSDT",
        "side": "LONG",
        "size": 1000,
        "entry_price": 0.********
      }
    ],
    "active_orders": 3,
    "account_balance": 1000.50
  },
  "files_backed_up": {
    "strategy_files": [
      "strategy/maker_channel.py",
      "strategy/maker_channel_backup.py"
    ],
    "config_files": [
      "config/config.json",
      "config/trading_config.json"
    ],
    "core_files": [
      "binance_trader.py",
      "cache_manager.py",
      "http_client.py",
      "main.py"
    ],
    "data_files": [
      "cache/candidates.pkl",
      "cache/symbols.json"
    ]
  },
  "checksums": {
    "strategy/maker_channel.py": "sha256:abc123...",
    "config/config.json": "sha256:def456...",
    "binance_trader.py": "sha256:ghi789..."
  },
  "backup_size": {
    "total_size_mb": 45.6,
    "compressed_size_mb": 12.3
  }
}
```

## 🔙 回滚执行流程

### 阶段1：回滚前准备
1. **选择回滚版本**：列出可用备份版本
2. **验证备份完整性**：校验和检查
3. **记录当前状态**：创建回滚前快照
4. **停止当前策略**：安全停止运行中的程序

### 阶段2：文件回滚执行
1. **备份当前版本**：创建回滚前备份
2. **恢复文件**：按优先级顺序恢复
3. **解压缩文件**：恢复压缩的数据文件
4. **更新配置**：恢复配置文件
5. **重建缓存**：清理并重建缓存数据

### 阶段3：回滚后验证
1. **文件完整性检查**：确认文件正确恢复
2. **配置有效性验证**：测试配置文件
3. **依赖关系检查**：验证模块导入
4. **功能测试**：基础功能验证
5. **生成回滚报告**：记录回滚结果

## 🛠️ 备份脚本设计

### backup.py 主要功能
```python
class BackupManager:
    def __init__(self, source_dir, backup_dir):
        self.source_dir = source_dir
        self.backup_dir = backup_dir
        
    def create_backup(self, version, backup_type="full"):
        """创建备份"""
        # 1. 创建备份目录
        # 2. 复制文件
        # 3. 生成元数据
        # 4. 验证备份
        
    def verify_backup(self, backup_path):
        """验证备份完整性"""
        # 1. 校验和检查
        # 2. 文件完整性
        # 3. 配置语法检查
        
    def list_backups(self):
        """列出所有备份版本"""
        
    def cleanup_old_backups(self, keep_count=5):
        """清理旧备份（保留最新5个）"""
```

### rollback.py 主要功能
```python
class RollbackManager:
    def __init__(self, source_dir, backup_dir):
        self.source_dir = source_dir
        self.backup_dir = backup_dir
        
    def rollback_to_version(self, version):
        """回滚到指定版本"""
        # 1. 验证目标版本
        # 2. 创建当前版本快照
        # 3. 执行文件恢复
        # 4. 验证回滚结果
        
    def list_available_versions(self):
        """列出可回滚的版本"""
        
    def verify_rollback(self):
        """验证回滚后的系统状态"""
```

## 🚨 风险控制措施

### 备份风险控制
1. **磁盘空间检查**：备份前确保足够空间
2. **文件锁定检查**：避免备份正在使用的文件
3. **权限验证**：确保有足够的文件操作权限
4. **网络状态检查**：避免在网络不稳定时备份

### 回滚风险控制
1. **版本兼容性检查**：确保回滚版本与当前环境兼容
2. **数据一致性保护**：回滚前备份当前数据
3. **渐进式回滚**：先回滚配置，再回滚代码
4. **回滚验证**：每步回滚后进行验证

### 应急处理方案
1. **回滚失败处理**：保留多个回滚点
2. **数据恢复方案**：独立的数据备份机制
3. **手动干预流程**：自动化失败时的手动操作指南
4. **联系机制**：关键问题的通知和升级机制

## 📊 监控和告警

### 备份监控指标
1. **备份成功率**：>99%
2. **备份完成时间**：<10分钟
3. **备份文件大小**：监控异常增长
4. **磁盘使用率**：<80%

### 回滚监控指标
1. **回滚成功率**：>95%
2. **回滚完成时间**：<5分钟
3. **系统恢复时间**：<15分钟
4. **功能验证通过率**：100%

### 告警机制
1. **备份失败告警**：立即通知
2. **磁盘空间告警**：使用率>90%
3. **回滚异常告警**：回滚过程中的任何错误
4. **验证失败告警**：备份或回滚后的验证失败

## 🔧 实施计划

### 第一阶段：基础设施搭建（1小时）
1. 创建备份目录结构
2. 编写基础备份脚本
3. 实现文件复制和压缩功能
4. 添加基本的日志记录

### 第二阶段：核心功能实现（2小时）
1. 实现完整的备份流程
2. 开发回滚功能
3. 添加文件完整性验证
4. 实现元数据管理

### 第三阶段：验证和优化（1小时）
1. 全面测试备份和回滚功能
2. 优化性能和错误处理
3. 完善监控和告警机制
4. 编写操作文档

### 第四阶段：集成和部署（30分钟）
1. 集成到主系统中
2. 配置自动化任务
3. 进行端到端测试
4. 培训和文档交付

## 📋 操作检查清单

### 备份操作检查清单
- [ ] 确认磁盘空间充足（>1GB）
- [ ] 检查系统运行状态
- [ ] 记录当前持仓和订单
- [ ] 执行备份脚本
- [ ] 验证备份完整性
- [ ] 更新备份记录
- [ ] 清理旧备份文件

### 回滚操作检查清单
- [ ] 选择正确的回滚版本
- [ ] 验证备份文件完整性
- [ ] 停止当前策略运行
- [ ] 创建回滚前快照
- [ ] 执行文件回滚
- [ ] 验证配置文件
- [ ] 测试基础功能
- [ ] 重启策略系统
- [ ] 监控系统运行状态
- [ ] 记录回滚结果

## 🎯 成功标准

### 备份成功标准
1. **文件完整性**：所有关键文件成功备份，校验和正确
2. **元数据准确**：版本信息、系统状态记录完整
3. **执行效率**：备份时间<10分钟
4. **存储优化**：压缩率>70%

### 回滚成功标准
1. **功能恢复**：系统功能完全恢复到目标版本状态
2. **数据一致性**：配置和数据状态正确
3. **性能正常**：系统性能无明显下降
4. **稳定运行**：回滚后24小时无异常

通过以上完整的版本备份机制设计，我们可以确保策略优化过程中的安全性和可回滚性，最大程度降低优化风险，保障系统稳定运行。