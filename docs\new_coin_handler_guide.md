# 新币种处理器使用指南

## 概述

新币种处理器（NewCoinHandler）是专门为处理历史数据不足的新上市币种而设计的工具。它解决了像AKEUSDT等新币种在获取K线数据时遇到的异常问题，提供了智能的参数优化、错误处理和配置管理功能。

## 主要功能

### 1. 新币种识别
- 自动识别新上市币种（基于上市天数阈值）
- 支持白名单管理，可手动添加特殊币种

### 2. 智能参数优化
- 根据币种历史数据长度自动调整K线请求参数
- 优化时间间隔和数据量限制
- 减少API请求失败率

### 3. 增强错误处理
- 分类错误统计（网络、数据、API、验证等）
- 重试机制和错误恢复
- 详细的错误日志和统计报告

### 4. 配置文件支持
- YAML格式配置文件
- 动态配置更新
- 参数热重载

## 快速开始

### 1. 基本使用

```python
from new_coin_handler import NewCoinHandler

# 初始化处理器
handler = NewCoinHandler(trader, logger)

# 检查是否为新币种
if handler.is_new_coin('AKEUSDT'):
    # 获取优化后的K线数据
    df = handler.get_klines_for_new_coin('AKEUSDT', '15m', 100)
    print(f"获取到 {len(df)} 条K线数据")
```

### 2. 使用自定义配置

```python
# 指定配置文件路径
handler = NewCoinHandler(trader, logger, config_path='custom_config.yaml')

# 动态更新配置
handler.update_config_value('basic.new_coin_threshold_days', 10)
handler.reload_config()
```

## 配置文件说明

配置文件采用YAML格式，包含以下主要部分：

### 基本参数 (basic)
```yaml
basic:
  new_coin_threshold_days: 7  # 新币种判定阈值（天）
  default_config:             # 默认配置
    max_available_klines: 50
    recommended_intervals: ['1m', '5m', '15m', '1h']
    min_klines_required: 5
    max_limit: 30
    retry_count: 3
    retry_delay: 1.5
```

### 白名单 (whitelist)
```yaml
whitelist:
  AKEUSDT:
    onboard_date: '2025-09-26'
    max_available_klines: 82
    recommended_intervals: ['1m', '5m', '15m', '1h']
    min_klines_required: 5
    max_limit: 50
    description: 'Akash Network token - 新上市币种'
```

### 错误处理 (error_handling)
```yaml
error_handling:
  max_consecutive_errors: 5
  error_log_level: 'WARNING'
  retry_on_error: true
  backoff_multiplier: 2.0
```

### 性能优化 (performance)
```yaml
performance:
  enable_cache: true
  max_cache_size: 1000
  cache_ttl_seconds: 300
  batch_size: 50
```

### 数据验证 (validation)
```yaml
validation:
  strict_validation: true
  min_data_points: 5
  max_price_gap_percent: 50.0
  check_continuity: true
  time_std_threshold: 30  # 分钟
  price_volatility_threshold: 10.0
```

## API 参考

### 核心方法

#### `is_new_coin(symbol, age_days=None)`
检查指定币种是否为新币种。

**参数：**
- `symbol`: 币种符号（如'AKEUSDT'）
- `age_days`: 可选，覆盖默认的天数阈值

**返回：** `bool` - 是否为新币种

#### `get_klines_for_new_coin(symbol, interval='15m', limit=200, age_days=None)`
获取新币种的K线数据，自动优化请求参数。

**参数：**
- `symbol`: 币种符号
- `interval`: 时间间隔
- `limit`: 数据量限制
- `age_days`: 可选，币种年龄

**返回：** `DataFrame` - K线数据

#### `optimize_kline_request(symbol, interval, limit, age_days=None)`
优化K线请求参数。

**返回：** `tuple` - (优化后的间隔, 优化后的限制)

### 配置管理方法

#### `get_config_value(key_path, default=None)`
获取配置值，支持点号分隔的路径。

**示例：**
```python
threshold = handler.get_config_value('basic.new_coin_threshold_days', 7)
```

#### `update_config_value(key_path, new_value)`
更新配置值。

#### `reload_config()`
重新加载配置文件。

#### `save_config()`
保存当前配置到文件。

### 错误处理方法

#### `get_error_summary()`
获取错误统计摘要。

#### `reset_error_stats()`
重置错误统计。

#### `get_new_coin_stats()`
获取新币种处理统计信息。

## 最佳实践

### 1. 配置优化
- 根据交易所API限制调整`retry_delay`和`max_consecutive_errors`
- 为不同类型的新币种设置合适的`max_available_klines`
- 定期检查和更新白名单

### 2. 错误处理
- 监控错误统计，及时发现问题
- 根据错误类型调整重试策略
- 保持日志级别适中，避免过多噪音

### 3. 性能优化
- 启用缓存以减少重复请求
- 合理设置批处理大小
- 定期清理过期的缓存数据

### 4. 数据验证
- 启用严格验证以确保数据质量
- 根据市场特点调整价格波动阈值
- 检查数据连续性以发现异常

## 故障排除

### 常见问题

1. **配置文件加载失败**
   - 检查文件路径是否正确
   - 验证YAML格式是否有效
   - 确保文件权限允许读取

2. **K线数据获取失败**
   - 检查网络连接
   - 验证API密钥和权限
   - 查看错误日志确定具体原因

3. **参数优化不生效**
   - 确认币种在白名单中或满足新币种条件
   - 检查配置文件中的参数设置
   - 验证币种上市日期是否正确

### 调试技巧

1. **启用详细日志**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **查看错误统计**
```python
print(handler.get_error_summary())
```

3. **检查配置状态**
```python
print(handler.get_config_summary())
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持新币种识别和参数优化
- 基本错误处理和重试机制

### v1.1.0
- 添加配置文件支持
- 增强错误分类和统计
- 改进数据验证机制

### v1.2.0
- 优化内存使用
- 添加性能监控
- 支持动态配置更新

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。