import os
import json
import pickle
import time
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class CacheManager:
    def __init__(self, cache_dir="data"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
    def load_symbols(self):
        """加载交易对信息"""
        path = self.cache_dir / "symbols.pkl"
        if path.exists():
            try:
                return pickle.load(open(path, 'rb'))
            except Exception as e:
                logger.error(f"加载交易对信息失败: {e}")
        return []
    
    def save_symbols(self, symbols):
        """保存交易对信息"""
        path = self.cache_dir / "symbols.pkl"
        try:
            pickle.dump(symbols, open(path, 'wb'))
        except Exception as e:
            logger.error(f"保存交易对信息失败: {e}")
    
    def load_klines(self, symbol, timeframe, limit):
        """加载K线数据缓存"""
        cache_key = f"klines_{symbol.replace('/', '_')}_{timeframe}_{limit}"
        path = self.cache_dir / f"{cache_key}.pkl"
        if path.exists() and time.time() - path.stat().st_mtime < 300:  # 5分钟缓存
            try:
                return pickle.load(open(path, 'rb'))
            except Exception as e:
                logger.error(f"加载K线缓存失败: {e}")
        return None
    
    def save_klines(self, symbol, timeframe, limit, data):
        """保存K线数据缓存"""
        cache_key = f"klines_{symbol.replace('/', '_')}_{timeframe}_{limit}"
        path = self.cache_dir / f"{cache_key}.pkl"
        try:
            pickle.dump(data, open(path, 'wb'))
        except Exception as e:
            logger.error(f"保存K线缓存失败: {e}")
    
    def load_candidates(self):
        """加载候选交易对"""
        path = self.cache_dir / "candidates.pkl"
        if path.exists():
            try:
                return pickle.load(open(path, 'rb'))
            except Exception as e:
                logger.error(f"加载候选交易对失败: {e}")
        return []
    
    def save_candidates(self, candidates):
        """保存候选交易对"""
        path = self.cache_dir / "candidates.pkl"
        try:
            pickle.dump(candidates, open(path, 'wb'))
        except Exception as e:
            logger.error(f"保存候选交易对失败: {e}")

    def load_positions(self):
        """加载持仓信息"""
        path = self.cache_dir / "positions.pkl"
        if path.exists():
            try:
                return pickle.load(open(path, 'rb'))
            except Exception as e:
                logger.error(f"加载持仓信息失败: {e}")
        return {}

    def save_positions(self, positions):
        """保存持仓信息"""
        path = self.cache_dir / "positions.pkl"
        try:
            pickle.dump(positions, open(path, 'wb'))
        except Exception as e:
            logger.error(f"保存持仓信息失败: {e}")