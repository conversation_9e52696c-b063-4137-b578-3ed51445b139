{"system": {"name": "全币种均线通道量化策略", "version": "1.0.0", "environment": "production", "debug": false, "log_level": "INFO"}, "exchange": {"name": "binance", "api_key": "${BINANCE_API_KEY}", "api_secret": "${BINANCE_API_SECRET}", "testnet": false, "timeout": 30, "retry_count": 3, "retry_delay": 1}, "strategy": {"name": "maker_channel", "enabled": true, "max_positions": 3, "position_size_usd": 100, "leverage": 1, "scan_interval": 300, "update_interval": 60}, "cache": {"enabled": true, "ttl": 3600, "max_size": 1000, "cleanup_interval": 3600, "storage_path": "./cache"}, "logging": {"level": "INFO", "file": "./strategy.log", "max_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "monitoring": {"enabled": true, "metrics_interval": 60, "alert_threshold": {"order_failure_rate": 0.1, "api_error_rate": 0.05, "position_loss_rate": 0.2}}, "backup": {"enabled": true, "interval": 86400, "keep_count": 10, "backup_path": "./backup"}}