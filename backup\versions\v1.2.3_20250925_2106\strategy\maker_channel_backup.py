import pandas as pd
import numpy as np
import datetime
import logging
import time
# ↓↓↓ 复用你的原模块 ↓↓↓
from binance_trader import BinanceTrader
from binance_trader import ApiRateLimiter, OrderOperationQueue
# ↓↓↓ 缓存管理器 ↓↓↓
from cache_manager import CacheManager

class MakerChannelStrategy:
    """依赖 BinanceTrader 完成所有下单/查询"""
    def __init__(self, trader: BinanceTrader, config: dict):
        self.trader = trader
        self.cfg = config
        self.log = logging.getLogger('MakerChannel')
        self.symbol_selector_log = logging.getLogger('ReferenceFolder.symbol_selector')
        self.symbol_scorer_log = logging.getLogger('ReferenceFolder.symbol_scorer')
        self.symbol = None
        self.pos = None
        self.add_count = 0
        self.original_nominal = config['first_nominal']
        self.cache = CacheManager()
        self.cand_cache = {}  # 内存缓存候选池
        self.last_full_scan = 0  # 上次全量扫描时间
        self.last_candidate_scan = 0  # 上次候选池扫描时间
        self.margin_alert_symbols = set()  # 保证金警报币种集合
        self.active_orders = {}  # 活跃订单跟踪 {order_id: {symbol, type, create_time, ttl}}
        self.order_ttl = config.get('order_ttl', 300)  # 限价单TTL，默认5分钟
        self.network_check_interval = config.get('network_check_interval', 60)  # 网络检测间隔，默认60秒
        self.last_network_check = 0  # 上次网络检测时间
        self.network_status = {'connected': True, 'latency': 0, 'last_error': None}  # 网络状态
        
        # 异步首次打分相关
        self.first_scoring_completed = False
        self.scoring_batch_index = 0
        self.last_scoring_time = 0
        self.scoring_batch_size = 10  # 每批处理10个币种
        self.scoring_interval = 30  # 每30秒处理一批
        
        self.symbol_selector_log.info(f"币种选择器初始化完成，选择前{config.get('symbol_limit', 50)}个币种，最低评分要求大于:{config.get('min_score', 7)}")
    # ---------- 新增：启动预热 ----------
    def warmup(self):
        """程序启动时轻量预热：仅获取基础信息，不拉取K线"""
        try:
            self.log.info("执行轻量启动预热（仅基础信息）...")

            # 首先检查并恢复实际持仓状态
            self._recover_positions_on_startup()

            # 获取所有交易对24小时行情（使用HttpClient的全局超时设置）
            info = self.trader.http.get('/fapi/v1/ticker/24hr')
            if not info or not isinstance(info, list):
                self.log.warning(f"启动预热失败：未获取到24hr行情数据 ({type(info)})")
                return

            # 获取交易所信息来过滤无效交易对
            exchange_info = self.trader.http.get('/fapi/v1/exchangeInfo')
            valid_symbols = set()
            if exchange_info and 'symbols' in exchange_info:
                for s in exchange_info['symbols']:
                    # 只选择状态为TRADING的USDT永续合约
                    if (isinstance(s, dict) and 
                        s.get('symbol', '').endswith('USDT') and
                        s.get('contractType') == 'PERPETUAL' and
                        s.get('status') == 'TRADING'):
                        valid_symbols.add(s['symbol'])

            # 过滤有效交易对
            usdt_pairs = []
            for s in info:
                symbol = s.get('symbol', '')
                if symbol in valid_symbols:
                    # 额外过滤条件：成交量大于1000 USDT，价格大于0.001
                    quote_volume = float(s.get('quoteVolume', 0.0))
                    last_price = float(s.get('lastPrice', 0.0))
                    price_change_percent = float(s.get('priceChangePercent', 0.0))
                    
                    # 过滤掉成交量过低、价格异常或涨幅异常的无效交易对
                    if (quote_volume > 1000 and 
                        last_price > 0.001 and 
                        abs(price_change_percent) < 10000):  # 过滤异常涨幅
                        usdt_pairs.append(s)
            
            self.log.info(f"交易所有效交易对数量: {len(valid_symbols)}, 过滤后有效交易对数量: {len(usdt_pairs)}")

            # 按涨幅排序，取前25
            top_gainers = sorted(
                usdt_pairs,
                key=lambda x: float(x.get('priceChangePercent', 0.0)),
                reverse=True
            )[:25]

            # 按成交额排序（quoteVolume = USDT成交额），取前25
            top_volume = sorted(
                usdt_pairs,
                key=lambda x: float(x.get('quoteVolume', 0.0)),
                reverse=True
            )[:25]

            # 合并去重，仅保存基础信息到候选池
            selected_symbols = list({s['symbol'] for s in (top_gainers + top_volume)})

            if not selected_symbols:
                self.log.warning("预热未选出任何币种")
                return

            # 仅保存候选池列表，不进行K线拉取和评分
            self.cache.save_candidates(selected_symbols)
            
            # 初始化异步打分状态
            self.first_scoring_completed = False
            self.scoring_batch_index = 0
            self.last_scoring_time = 0

            self.log.info(f"轻量预热完成，候选池数量: {len(selected_symbols)} 个币种，将在主循环中异步打分")

        except Exception as e:
            self.log.error(f"warmup 初始化候选池失败: {e}")
            # 初始化默认状态
            self.first_scoring_completed = False
            self.scoring_batch_index = 0
            self.last_scoring_time = 0
    
    def _recover_positions_on_startup(self):
        """程序启动时从实际持仓中恢复状态"""
        try:
            self.log.info("检查并恢复实际持仓状态...")
            
            # 获取所有实际持仓
            positions = self.trader.get_positions()
            active_positions = []
            
            for pos in positions:
                qty = float(pos.get('positionAmt', 0))
                if abs(qty) > 0.001:  # 过滤掉数量极小的持仓
                    symbol = pos.get('symbol')
                    entry_price = float(pos.get('entryPrice', 0))
                    unrealized_pnl = float(pos.get('unRealizedProfit', 0))
                    
                    active_positions.append({
                        'symbol': symbol,
                        'qty': qty,
                        'entry_price': entry_price,
                        'unrealized_pnl': unrealized_pnl
                    })
                    
                    self.log.info(f"发现持仓: {symbol}, 数量: {qty}, 入场价: {entry_price}, 浮盈: {unrealized_pnl:.4f}")
            
            if active_positions:
                # 如果有多个持仓，选择最新的一个作为主持仓（按绝对浮盈排序）
                main_position = max(active_positions, key=lambda x: abs(x['unrealized_pnl']))
                
                # 恢复self.pos状态
                self.pos = {
                    'symbol': main_position['symbol'],
                    'entry': main_position['entry_price'],  # 使用'entry'字段名
                    'qty': main_position['qty'],
                    'stop': main_position['entry_price'] * (0.97 if main_position['qty'] > 0 else 1.03),  # 默认3%止损
                    'add_count': 0  # 重启后重置加仓次数
                }
                
                self.log.info(f"恢复主持仓状态: {main_position['symbol']}, 数量: {main_position['qty']}, 入场价: {main_position['entry_price']}")
                
                # 检查所有持仓的止损单状态
                self._check_all_positions_stop_orders(active_positions)
            else:
                self.log.info("未发现活跃持仓，保持空仓状态")
                self.pos = None
                
        except Exception as e:
            self.log.error(f"恢复持仓状态失败: {e}")
            self.pos = None
    
    def _check_all_positions_stop_orders(self, active_positions):
        """检查所有持仓的止损单状态并补挂缺失的止损单"""
        try:
            for pos in active_positions:
                symbol = pos['symbol']
                qty = pos['qty']
                entry_price = pos['entry_price']
                
                # 获取该币种的开放订单
                try:
                    open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': symbol})
                    
                    # 检查是否存在止损单
                    has_stop_order = False
                    for order in open_orders:
                        if order.get('type') in ['STOP_MARKET', 'STOP']:
                            has_stop_order = True
                            break
                    
                    if not has_stop_order:
                        # 缺少止损单，立即补挂
                        self.log.warning(f"持仓 {symbol} 缺少止损单，立即补挂")
                        
                        # 获取当前价格
                        current_price = float(self.trader.get_symbol_ticker(symbol)['price'])
                        
                        # 计算止损价格（基于当前价格的3%止损）
                        if qty > 0:  # 多头持仓
                            stop_price = current_price * 0.97
                        else:  # 空头持仓
                            stop_price = current_price * 1.03
                        
                        # 补挂止损单
                        try:
                            result = self.trader.place_stop_loss_order(symbol, abs(qty), stop_price)
                            if result and result.get('orderId'):
                                self.log.info(f"成功为 {symbol} 补挂止损单，止损价: {stop_price}")
                            else:
                                self.log.error(f"为 {symbol} 补挂止损单失败: {result}")
                        except Exception as e:
                            self.log.error(f"为 {symbol} 补挂止损单异常: {e}")
                    else:
                        self.log.info(f"持仓 {symbol} 已有止损单")
                        
                except Exception as e:
                    self.log.error(f"检查 {symbol} 开放订单失败: {e}")
                    
        except Exception as e:
            self.log.error(f"检查所有持仓止损单失败: {e}")
    
    def _async_first_score_batch(self):
        """异步首次打分：分批处理候选池，避免阻塞主循环"""
        try:
            # 如果已完成首次打分，直接返回
            if self.first_scoring_completed:
                return
            
            # 检查是否到了处理时间
            current_time = time.time()
            if current_time - self.last_scoring_time < self.scoring_interval:
                return
            
            # 获取候选池列表
            candidates = self.cache.load_candidates()
            if not candidates:
                self.log.warning("候选池为空，跳过异步打分")
                self.first_scoring_completed = True
                return
            
            # 计算当前批次的范围
            start_idx = self.scoring_batch_index * self.scoring_batch_size
            end_idx = min(start_idx + self.scoring_batch_size, len(candidates))
            
            if start_idx >= len(candidates):
                # 所有批次处理完成
                self.first_scoring_completed = True
                self.log.info(f"异步首次打分完成，共处理 {len(candidates)} 个币种")
                return
            
            # 处理当前批次
            batch_symbols = candidates[start_idx:end_idx]
            self.log.info(f"异步打分批次 {self.scoring_batch_index + 1}：处理 {len(batch_symbols)} 个币种 ({start_idx+1}-{end_idx})")
            
            # 并行更新当前批次
            self.parallel_update(batch_symbols, '15m', 200)
            
            # 更新状态
            self.scoring_batch_index += 1
            self.last_scoring_time = current_time
            
            # 检查是否完成所有批次
            if end_idx >= len(candidates):
                self.first_scoring_completed = True
                self.log.info(f"异步首次打分完成，共处理 {len(candidates)} 个币种，候选池数量: {len(self.cand_cache)}")
            
        except Exception as e:
            self.log.error(f"异步首次打分失败: {e}")
            # 发生错误时标记为完成，避免无限重试
            self.first_scoring_completed = True
    # ---------- 工具 ----------
    def calculate_atr(self, high, low, close, length=14):
        """计算平均真实波幅(ATR)"""
        # 计算真实波幅(TR)
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # 计算ATR
        atr = tr.rolling(window=length).mean()
        return atr

    def get_klines(self, symbol, interval='15m', limit=200):
        # 先检查缓存
        cached_data = self.cache.load_klines(symbol, interval, limit)
        if cached_data is not None:
            self.log.debug(f"使用缓存K线数据: {symbol} {interval}")
            # 确保缓存数据的时间戳索引有UTC时区
            if not cached_data.empty:
                try:
                    # 如果时间戳没有时区，添加UTC时区
                    if cached_data.index.tz is None:
                        cached_data.index = cached_data.index.tz_localize('UTC')
                    # 如果已经有时区，不进行任何操作，避免"Cannot localize tz-aware Timestamp"错误
                except Exception as e:
                    self.log.warning(f"时区处理失败 {symbol}: {e}")
            return cached_data
        
        # 缓存未命中，从API获取
        k = self.trader.get_klines(symbol, interval, limit)
        if isinstance(k, list) and len(k) > 0:
            df = pd.DataFrame(k, columns=['t', 'o', 'h', 'l', 'c', 'v', 'ct', 'q', 'n', 'V', 'qV', 'ignore'])
            df[['o', 'h', 'l', 'c', 'v']] = df[['o', 'h', 'l', 'c', 'v']].astype(float)
            df['t'] = pd.to_datetime(df['t'], unit='ms', utc=True)
            df_result = df.set_index('t')
            
            # 保存到缓存
            self.cache.save_klines(symbol, interval, limit, df_result)
            return df_result
        return pd.DataFrame()

    def get_depth01pct(self, symbol):
        # 先检查缓存
        cached_depth = self.cache.load_depth(symbol)
        if cached_depth is not None:
            self.log.debug(f"使用缓存深度数据: {symbol}")
            return cached_depth
        
        import time
        # 重试机制，最多重试3次
        for attempt in range(3):
            try:
                book = self.trader.http.get('/fapi/v1/depth', {'symbol': symbol, 'limit': 100})
                if not book or 'bids' not in book or 'asks' not in book:
                    self.log.warning(f"获取{symbol}深度数据失败，第{attempt+1}次重试")
                    time.sleep(0.5)
                    continue
                
                bids, asks = book['bids'], book['asks']
                if not bids or not asks:
                    self.log.warning(f"{symbol}深度数据为空，第{attempt+1}次重试")
                    time.sleep(0.5)
                    continue
                
                mid = (float(bids[0][0]) + float(asks[0][0])) / 2
                bid_depth = sum([float(v) for p, v in bids if float(p) >= mid * 0.999])
                ask_depth = sum([float(v) for p, v in asks if float(p) <= mid * 1.001])
                
                depth = min(bid_depth, ask_depth)
                if depth <= 0:
                    self.log.warning(f"{symbol}深度计算异常: {depth}")
                    time.sleep(0.5)
                    continue
                    
                # 记录深度信息用于调试
                self.log.debug(f"{symbol}深度数据: 中间价={mid:.2f}, 买盘深度={bid_depth:.2f}, 卖盘深度={ask_depth:.2f}, 最终深度={depth:.2f}")
                
                # 保存到缓存
                self.cache.save_depth(symbol, depth)
                return depth
            except Exception as e:
                self.log.warning(f"获取{symbol}深度数据异常: {e}，第{attempt+1}次重试")
                time.sleep(0.5)
                continue
        
        # 所有重试都失败，返回默认值
        self.log.error(f"获取{symbol}深度数据失败，使用默认值100000")
        return 100000  # 返回一个合理的默认值，避免评分为0

    # ---------- 策略核心 ----------
    def check_channel_breakthrough(self, df_d):
        """检查通道突破条件 - 作为进入候选池的硬开关（放宽条件）"""
        n = max(2, int(1440 / 60))  # 1h≈24根
        upper_band = df_d['h'].rolling(n).max().iloc[-1]
        current_price = df_d['c'].iloc[-1]
        high_price = df_d['h'].iloc[-1]  # 当前K线最高价
        
        # 放宽条件1：收盘价接近上轨（95%以上）或最高价突破上轨即可
        close_ratio = current_price / upper_band
        high_breakthrough = high_price >= upper_band
        
        if close_ratio < 0.95 and not high_breakthrough:
            self.log.debug(f"{df_d.name} 未接近/突破上轨: 收盘比例={close_ratio:.3f}, 最高价突破={high_breakthrough}")
            return False
        
        # 放宽条件2：突破幅度限制提高到10%
        if high_breakthrough:
            breakthrough_pct = (high_price - upper_band) / upper_band * 100
            if breakthrough_pct > 10.0:
                self.log.debug(f"{df_d.name} 突破幅度过大: {breakthrough_pct:.2f}% > 10%")
                return False
        
        # 放宽条件3：允许更长的突破时间（5根K线）
        recent_above_count = sum(1 for i in range(-5, 0) if df_d['h'].iloc[i] >= df_d['h'].rolling(n).max().iloc[i])
        if recent_above_count >= 5:
            self.log.debug(f"{df_d.name} 突破时间过长: 连续{recent_above_count}根K线突破")
            return False
        
        breakthrough_info = f"收盘比例={close_ratio:.3f}, 最高价突破={high_breakthrough}"
        if high_breakthrough:
            breakthrough_pct = (high_price - upper_band) / upper_band * 100
            breakthrough_info += f", 突破幅度={breakthrough_pct:.2f}%"
        
        self.log.debug(f"{df_d.name} 通过通道突破检查: {breakthrough_info}")
        return True
    
    def score_symbol(self, df_d, age_days=None):
        """计算币种评分 - 不再包含通道突破检查"""
        
        # 1. 深度 score_l（门槛降低 → 200 k$ 即及格）
        depth = self.get_depth01pct(df_d.name)
        score_l = 2 if depth >= 2e6 else 1 if depth >= 2e5 else 0
        self.log.debug(f"{df_d.name} 深度评分: depth={depth:.0f}, score_l={score_l}")

        # 2. 成交量变化 score_v（≥ 0 % 即及格）
        if len(df_d) >= 50:
            vol24_chg = df_d['v'].iloc[-25:].mean() / df_d['v'].iloc[-50:-25].mean() - 1
            score_v = 1 if vol24_chg >= 0.0 else 0
            self.log.debug(f"{df_d.name} 成交量评分: vol24_chg={vol24_chg:.2f}, score_v={score_v}")
        else:
            score_v = 0
            self.log.debug(f"{df_d.name} 成交量评分: 数据长度不足 ({len(df_d)} < 50)")

        # 3. 币龄 score_a（使用分钟级年龄，新币也能满分）
        current_time = pd.Timestamp.utcnow()
        # 确保current_time有时区信息
        if current_time.tz is None:
            current_time = current_time.tz_localize('UTC')
        first_time = df_d.index[0]
        # 确保first_time有时区信息，使用安全的处理方式
        try:
            if hasattr(first_time, 'tz') and first_time.tz is None:
                first_time = first_time.tz_localize('UTC')
            elif not hasattr(first_time, 'tz'):
                # 如果没有tz属性，转换为带时区的Timestamp
                first_time = pd.Timestamp(first_time).tz_localize('UTC')
            age_min = int((current_time - first_time).total_seconds() // 60)
        except Exception as e:
            # 如果时区处理失败，使用默认值
            self.log.debug(f"{df_d.name} 时区处理失败，使用默认币龄: {e}")
            age_min = 60*24*30  # 默认30天
            
        # 新的币龄评分逻辑：对新币更友好
        if age_min <= 60*24*2:                # ≤2天（新币黄金期）
            score_a = 2
        elif 60*24*2 < age_min <= 60*24*7:    # 2-7天
            score_a = 2  
        elif 60*24*7 < age_min <= 60*24*30:   # 7-30天
            score_a = 1
        elif 60*24*30 < age_min <= 60*24*365: # 30天-1年
            score_a = 1
        else:                                  # >1年
            score_a = 0
        self.log.debug(f"{df_d.name} 币龄评分: age_min={age_min}分钟({age_min/(60*24):.1f}天), score_a={score_a}")

        # 4. 通道评分已移至前置条件，这里直接给满分
        score_t = 2  # 能到这里说明已经突破上轨
        self.log.debug(f"{df_d.name} 通道评分: 已突破上轨，score_t={score_t}")

        # 5. 动量 score_m（优化对新币的评分）
        # 根据数据长度灵活计算动量
        if len(df_d) < 5:
            score_m = 0
            self.log.debug(f"{df_d.name} 动量评分: 数据长度过少 ({len(df_d)} < 5)")
        elif len(df_d) < 25:
            # 新币数据不足25根，使用现有数据计算短期动量
            available_periods = len(df_d) - 1
            chg_short = (df_d['c'].iloc[-1] / df_d['c'].iloc[-available_periods] - 1) * 100
            high_short = df_d['h'].iloc[-available_periods:].max()
            low_short = df_d['l'].iloc[-available_periods:].min()
            retrace = (high_short - low_short) / df_d['c'].iloc[-available_periods] * 100
            
            if retrace < 0.1:  # 避免除零
                score_m = 1 if chg_short > 0 else 0  # 新币给予基础分
                self.log.debug(f"{df_d.name} 动量评分: 新币回撤过小，涨幅={chg_short:.2f}%, score_m={score_m}")
            else:
                ratio = chg_short / retrace
                if chg_short > 0:
                    # 新币评分标准更宽松
                    score_m = 2 if ratio >= 1.0 else 1 if ratio >= 0.3 else 0
                    self.log.debug(f"{df_d.name} 动量评分(新币): 涨幅={chg_short:.2f}%, 回撤={retrace:.2f}%, 比率={ratio:.2f}, score_m={score_m}")
                else:
                    score_m = 0
                    self.log.debug(f"{df_d.name} 动量评分: 短期涨幅为负 ({chg_short:.2f}%)")
        else:
            # 正常24小时动量计算
            chg24 = (df_d['c'].iloc[-1] / df_d['c'].iloc[-25] - 1) * 100
            high_24h = df_d['h'].iloc[-25:].max()
            low_24h = df_d['l'].iloc[-25:].min()
            retrace = (high_24h - low_24h) / df_d['c'].iloc[-25] * 100
            
            if retrace < 0.1:  # 避免除零
                score_m = 0
                self.log.debug(f"{df_d.name} 动量评分: 回撤幅度过小 ({retrace:.2f}%)")
            else:
                ratio = chg24 / retrace
                if chg24 > 0:
                    score_m = 2 if ratio >= 1.5 else 1 if ratio >= 0.5 else 0
                    self.log.debug(f"{df_d.name} 动量评分: chg24={chg24:.2f}%, retrace={retrace:.2f}%, ratio={ratio:.2f}, score_m={score_m}")
                else:
                    score_m = 0
                    self.log.debug(f"{df_d.name} 动量评分: 24小时涨幅为负 ({chg24:.2f}%)")

        # 6. 基础保护：只要深度 ≥ 200 k$ 且总分为 0 → 保底 1 分
        total_score = score_m * 3 + score_l * 2 + score_v * 1 + score_a * 2 + score_t * 2
        if total_score == 0 and depth >= 2e5:
            total_score = 1
            score_details = f"M({score_m}*3={score_m*3}),L({score_l}*2={score_l*2}),V({score_v}*1={score_v*1}),A({score_a}*2={score_a*2}),T({score_t}*2={score_t*2}),基础分(+1)"
        else:
            score_details = f"M({score_m}*3={score_m*3}),L({score_l}*2={score_l*2}),V({score_v}*1={score_v*1}),A({score_a}*2={score_a*2}),T({score_t}*2={score_t*2})"
        
        self.symbol_scorer_log.debug(f"{df_d.name}评分:{{{score_details}}},总分:{total_score:.2f}")
        
        return total_score, score_m

    def dynamic_tf(self, age_days):
        if age_days <= 1:
            return '15m', '5m', '3m'
        elif age_days <= 3:
            return '1h', '15m', '5m'
        elif age_days <= 7:
            return '3h', '1h', '15m'
        elif age_days <= 30:
            return '4h', '3h', '1h'
        elif age_days <= 60:
            return '6h', '4h', '3h'
        else:
            return '1d', '6h', '4h'

    def watch_fill_then_stop(self, symbol, order_id, qty, stop_price):
        """异步盯单线程"""
        for _ in range(30):  # 30秒超时
            o = self.trader.get_order(symbol, order_id)
            if o and o.get('status') == 'FILLED':
                # 真正写入仓位
                self.pos = {
                    'symbol': symbol,
                    'entry': float(o['price']),
                    'qty': qty,
                    'stop': stop_price,
                    'add_count': 0
                }
                # 挂止损单
                self.trader.place_stop_loss_order(symbol, qty, stop_price)
                return
            if o and o.get('status') in ['CANCELED', 'EXPIRED', 'REJECTED']:
                return  # 直接放弃
            time.sleep(1)
        # 超时未成交→撤单
        self.trader.cancel_order(symbol, order_id)

    def run_symbol(self, symbol):
        info = self.trader.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
        if not info or 'symbols' not in info: return
        # 安全地获取过滤器信息
        tick = 0.01  # 默认值
        step = 0.001  # 默认值
        min_q = 0.001  # 默认值
        if isinstance(info, dict) and info.get('symbols') and len(info['symbols']) > 0:
            symbol_info = info['symbols'][0]
            if isinstance(symbol_info, dict):
                filters = symbol_info.get('filters', [])
                if isinstance(filters, list):
                    # 获取价格精度过滤器
                    price_filters = [f for f in filters if isinstance(f, dict) and f.get('filterType') == 'PRICE_FILTER']
                    if price_filters:
                        tick = float(price_filters[0].get('tickSize', 0.01))
                    
                    # 获取数量精度过滤器
                    lot_filters = [f for f in filters if isinstance(f, dict) and f.get('filterType') == 'LOT_SIZE']
                    if lot_filters:
                        step = float(lot_filters[0].get('stepSize', 0.001))
                        min_q = float(lot_filters[0].get('minQty', 0.001))

        # ===== ① 使用交易对信息中的onboardDate字段计算币龄 =====
        age_days = 500  # 默认值
        
        try:
            # 获取交易对详细信息，包含上线时间
            symbol_info = self.trader.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
            if isinstance(symbol_info, dict) and symbol_info.get('symbols') and len(symbol_info['symbols']) > 0:
                symbol_data = symbol_info['symbols'][0]
                if isinstance(symbol_data, dict):
                    # 检查是否有onboardDate字段
                    onboard_date = symbol_data.get('onboardDate')
                    if onboard_date is not None:
                        # 将时间戳转换为datetime对象
                        launch_ts = pd.to_datetime(onboard_date, unit='ms')
                        # 确保时区为UTC
                        if launch_ts.tz is None:
                            launch_ts = launch_ts.tz_localize('UTC')
                        current_time = pd.Timestamp.utcnow()
                        if current_time.tz is None:
                            current_time = current_time.tz_localize('UTC')
                        age_days = (current_time - launch_ts).days
                        self.log.info(f"{symbol} 使用onboardDate计算币龄: {age_days}天 (上线时间: {launch_ts})")
                    else:
                        # 如果没有onboardDate字段，回退到使用K线数据
                        self.log.warning(f"{symbol} No onboardDate in exchange info, falling back to K-line data")
                        k0 = self.get_klines(symbol, '1d', 100)
                        if not k0.empty:
                            launch_ts = k0.index[0]
                            # 使用安全的时区处理方式
                            try:
                                # 检查并处理时间戳时区
                                if pd.api.types.is_datetime64_any_dtype(type(launch_ts)):
                                    # 如果是pandas时间戳类型
                                    if hasattr(launch_ts, 'tz') and launch_ts.tz is None:
                                        launch_ts = launch_ts.tz_localize('UTC')
                                else:
                                    # 如果是普通datetime对象
                                    import datetime
                                    if isinstance(launch_ts, datetime.datetime):
                                        launch_ts = pd.Timestamp(launch_ts)
                                        if launch_ts.tz is None:
                                            launch_ts = launch_ts.tz_localize('UTC')
                            except Exception:
                                # 如果时区处理失败，记录日志但继续执行
                                self.log.warning(f"{symbol} 时间戳时区处理失败，使用默认值")
                            
                            current_time = pd.Timestamp.utcnow()
                            if current_time.tz is None:
                                current_time = current_time.tz_localize('UTC')
                            
                            # 安全地计算时间差
                            try:
                                age_days = (current_time - launch_ts).days
                            except Exception:
                                age_days = 500  # 使用默认值
                            self.log.info(f"{symbol} 使用K线数据计算币龄: {age_days}天")
                        else:
                            self.log.warning(f"{symbol} No K-line data, using default age: {age_days}天")
            else:
                # 如果获取交易对信息失败，回退到使用K线数据
                self.log.warning(f"{symbol} Failed to get exchange info, falling back to K-line data")
                k0 = self.get_klines(symbol, '1d', 100)
                if not k0.empty:
                    launch_ts = k0.index[0]
                    # 使用安全的时区处理方式
                    try:
                        # 检查并处理时间戳时区
                        if pd.api.types.is_datetime64_any_dtype(type(launch_ts)):
                            # 如果是pandas时间戳类型
                            if hasattr(launch_ts, 'tz') and launch_ts.tz is None:
                                launch_ts = launch_ts.tz_localize('UTC')
                        else:
                            # 如果是普通datetime对象
                            import datetime
                            if isinstance(launch_ts, datetime.datetime):
                                launch_ts = pd.Timestamp(launch_ts)
                                if launch_ts.tz is None:
                                    launch_ts = launch_ts.tz_localize('UTC')
                    except Exception:
                        # 如果时区处理失败，记录日志但继续执行
                        self.log.warning(f"{symbol} 时间戳时区处理失败，使用默认值")
                    
                    current_time = pd.Timestamp.utcnow()
                    if current_time.tz is None:
                        current_time = current_time.tz_localize('UTC')
                    
                    # 安全地计算时间差
                    try:
                        age_days = (current_time - launch_ts).days
                    except Exception:
                        age_days = 500  # 使用默认值
                    self.log.info(f"{symbol} 使用K线数据计算币龄: {age_days}天")
                else:
                    self.log.warning(f"{symbol} No K-line data, using default age: {age_days}天")
        except Exception as e:
            self.log.error(f"{symbol} 币龄计算异常: {e}, using default age: {age_days}天")
        

        
        # ===== ② 动态周期=====
        big_tf, entry_tf, exit_tf = self.dynamic_tf(age_days)
        
        # 记录配置信息
        self.log.info(f"{symbol}配置:上市{age_days:.1f}天->时间周期:{entry_tf}")
        
        df = self.get_klines(symbol, big_tf, 200)
        if df.empty: 
            self.log.warning(f"{symbol} K线数据获取失败")
            return
        df.name = symbol
        
        # 记录实时状态（在评分检查之前，确保所有币种都有状态记录）
        current_price = df['c'].iloc[-1]
        n = max(2, int(1440 / 60))  # 1h≈24根
        upper_band = df['h'].rolling(n).max().iloc[-1]
        lower_band = df['l'].rolling(n).min().iloc[-1]
        mid_band = (upper_band + lower_band) / 2
        
        # 判断通道位置
        if current_price >= upper_band:
            position_status = "通道上部"
        elif current_price >= mid_band:
            position_status = "通道中上部"
        elif current_price >= lower_band:
            position_status = "通道中下部"
        else:
            position_status = "通道下部"
            
        self.log.info(f"{symbol}|价格:{current_price:.2f}|周期:{entry_tf}|状态:{position_status}|上轨:{upper_band:.2f}|下轨:{lower_band:.2f}")
        
        score, M = self.score_symbol(df, age_days)
        if score < 7: 
            # 如果评分不足，从候选池中移除该币种
            if symbol in self.cand_cache:
                del self.cand_cache[symbol]
                self.log.info(f"从候选池移除{symbol}，评分不足: {score}分 (需要≥7分), M={M}")
            return
        mid_price = (df['h'].iloc[-1] + df['l'].iloc[-1]) / 2
        size = self.original_nominal / mid_price
        size = self.trader._round_quantity(size, {'step_size': step, 'min_qty': min_q})
        entry_p = self.trader._round_price(mid_price, {'tick_size': tick})
        
        # 检查是否已触发保证金警报
        if hasattr(self, 'margin_alert_symbols') and symbol in self.margin_alert_symbols:
            return False  # 静默跳过，避免重复警报
            
        # 资金闸门检查
        leverage = self.pos.get('leverage', 3) if self.pos else 3
        required = size * entry_p / leverage * 1.1
        if self.trader.get_total_balance() < required:
            self.log.error(f'{symbol} 可用保证金不足，跳过信号，等待保证金释放')
            return False
            
        # 开仓前检查并清理孤儿止损/止盈单
        self.log.info(f"{symbol} 开仓前检查孤儿订单...")
        orphan_stop_orders = self.check_orphan_stop_orders(symbol)
        if orphan_stop_orders:
            self.log.warning(f"{symbol} 发现 {len(orphan_stop_orders)} 个孤儿止损/止盈单，开始清理...")
            cleaned_count = self.clean_orphan_orders(symbol, include_stop_orders=True)
            if cleaned_count > 0:
                self.log.info(f"{symbol} 成功清理 {cleaned_count} 个孤儿订单，等待2秒确保生效...")
                time.sleep(2)  # 等待订单取消生效
                
                # 再次检查确保清理完成
                remaining_orphans = self.check_orphan_stop_orders(symbol)
                if remaining_orphans:
                    self.log.error(f"{symbol} 仍有 {len(remaining_orphans)} 个孤儿订单未清理完成，暂停开仓")
                    return False
                else:
                    self.log.info(f"{symbol} 孤儿订单清理完成，继续开仓")
            else:
                self.log.error(f"{symbol} 孤儿订单清理失败，暂停开仓")
                return False
        else:
            self.log.info(f"{symbol} 未发现孤儿订单，可以安全开仓")
            
        # 开仓并注册盯单任务
        order = self.trader.open_position(symbol, 'BUY', size, price=entry_p, timeInForce='GTC')
        if not order or not isinstance(order, dict) or 'orderId' not in order:
            # 开仓失败诊断机制
            self.diagnose_open_failure(symbol, size, entry_p, leverage)
            return
            
        # 注册异步盯单任务
        self.trader.order_queue.add_operation(
            operation=lambda: self.watch_fill_then_stop(
                symbol, order['orderId'], size, entry_p * 1.001
            ),
            callback=None,
            retry_count=1
        )
        
        # 添加到订单跟踪列表
        self.active_orders[order['orderId']] = {
            'symbol': symbol,
            'type': 'LIMIT',
            'side': 'BUY',
            'create_time': time.time(),
            'ttl': self.order_ttl,
            'size': size,
            'price': entry_p
        }
            
        self.log.info(f"{symbol} 开多 {size} @ {entry_p} 得分{score} - 等待成交确认")
        # 注意：不在此处设置self.pos，只有在watch_fill_then_stop确认成交后才设置

    def diagnose_open_failure(self, symbol, size, entry_p, leverage):
        """开仓失败诊断机制 - 分类失败原因并制定重试策略"""
        self.log.error(f"{symbol} 开仓失败，开始诊断...")
        
        # 初始化重试标记
        should_retry = False
        retry_delay = 0
        failure_reason = "unknown"
        
        # 1. 检查持仓数量
        current_positions = len([p for p in [self.pos] if p is not None])
        if current_positions >= 3:
            self.log.error(f"诊断结果: 持仓已达上限 {current_positions}/3，需要末位淘汰")
            failure_reason = "position_limit"
            should_retry = False  # 不重试，需要先平仓
            
        # 2. 检查保证金状态
        elif True:  # 继续检查其他原因
            total_balance = self.trader.get_total_balance()
            required_margin = size * entry_p / leverage * 1.1
            self.log.info(f"保证金检查: 可用={total_balance:.2f}, 需要={required_margin:.2f}")
            
            if total_balance < required_margin:
                self.log.error(f"诊断结果: 保证金不足 {total_balance:.2f} < {required_margin:.2f}")
                failure_reason = "insufficient_margin"
                
                # 检查并自动清理孤儿订单
                cleaned_orphan = self.clean_orphan_orders(symbol)
                # 检查并自动清理多余挂单
                cleaned_redundant = self.clean_redundant_orders(symbol)
                
                if cleaned_orphan or cleaned_redundant:
                    self.log.info("已清理部分订单，释放保证金，30秒后可重试开仓")
                    should_retry = True
                    retry_delay = 30
                else:
                    self.log.error("无可清理订单，保证金确实不足，触发用户警报")
                    self.trigger_margin_alert(symbol, total_balance, required_margin)
                    should_retry = False
                    
            # 3. 检查交易对状态
            elif symbol not in self.trader.symbols:
                self.log.error(f"诊断结果: 交易对 {symbol} 不在有效列表中")
                failure_reason = "invalid_symbol"
                should_retry = False  # 不重试，交易对无效
                
            # 4. 网络或API异常
            else:
                network_healthy = self._is_network_healthy()
                if not network_healthy:
                    network_status = self.network_status
                    error_msg = network_status.get('last_error', '未知网络错误')
                    latency = network_status.get('latency', 0)
                    
                    self.log.error(f"诊断结果: 网络/API异常 - {error_msg}, 延迟: {latency}ms")
                    failure_reason = f"network_error: {error_msg}"
                    should_retry = True
                    retry_delay = 60  # 网络异常时延迟更长时间重试
                else:
                    self.log.error(f"诊断结果: 未知原因导致开仓失败")
                    failure_reason = "unknown_error"
                    should_retry = True
                    retry_delay = 30  # 未知错误时适中延迟重试
                
        # 记录诊断结果
        self.log.info(f"失败分类: {failure_reason}, 是否重试: {should_retry}, 延迟: {retry_delay}秒")
        
        # 根据诊断结果决定是否加入重试队列
        if should_retry:
            self._schedule_retry(symbol, size, entry_p, leverage, retry_delay, failure_reason)
        else:
             self.log.warning(f"{symbol} 开仓失败且不适合重试，原因: {failure_reason}")
    
    def _schedule_retry(self, symbol, size, entry_p, leverage, delay_seconds, reason):
        """安排重试开仓任务"""
        if not hasattr(self, 'retry_queue'):
            self.retry_queue = []
            
        retry_time = time.time() + delay_seconds
        retry_task = {
            'symbol': symbol,
            'size': size,
            'entry_p': entry_p,
            'leverage': leverage,
            'retry_time': retry_time,
            'reason': reason,
            'attempts': 1  # 首次重试
        }
        
        self.retry_queue.append(retry_task)
        self.log.info(f"已安排 {symbol} 重试开仓，{delay_seconds}秒后执行，原因: {reason}")
    
    def _process_retry_queue(self):
        """处理重试队列"""
        if not hasattr(self, 'retry_queue') or not self.retry_queue:
            return
            
        current_time = time.time()
        completed_tasks = []
        
        for i, task in enumerate(self.retry_queue):
            if current_time >= task['retry_time']:
                symbol = task['symbol']
                self.log.info(f"执行 {symbol} 重试开仓，尝试次数: {task['attempts']}")
                
                # 重新尝试开仓
                order = self.trader.open_position(
                    symbol, 'BUY', task['size'], 
                    price=task['entry_p'], timeInForce='GTC'
                )
                
                if order and isinstance(order, dict) and 'orderId' in order:
                    self.log.info(f"{symbol} 重试开仓成功！")
                    completed_tasks.append(i)
                    # 注册盯单任务
                    self.trader.queue.add_task(
                        lambda: self.trader.watch_fill_then_stop(
                            symbol, order['orderId'], task['size'], task['entry_p'] * 1.001
                        ),
                        callback=None,
                        retry_count=1
                    )
                    # 更新持仓信息
                    self.symbol = symbol
                    self.pos = {
                        'symbol': symbol, 'entry': task['entry_p'], 'qty': task['size'], 
                        'stop': task['entry_p'] * 1.001, 'peak': task['entry_p'],
                        'add_count': 0, 'leverage': task['leverage'], 
                        'original_nominal': self.original_nominal, 'exit_pct_history': 0
                    }
                else:
                    # 重试失败
                    task['attempts'] += 1
                    if task['attempts'] <= 3:  # 最多重试3次
                        task['retry_time'] = current_time + (task['attempts'] * 30)  # 指数退避
                        self.log.warning(f"{symbol} 重试失败，安排第{task['attempts']}次重试")
                    else:
                        self.log.error(f"{symbol} 重试3次均失败，放弃重试")
                        completed_tasks.append(i)
        
        # 移除已完成的任务
        for i in reversed(completed_tasks):
            self.retry_queue.pop(i)
    
    def _manage_order_lifecycle(self):
        """管理订单生命周期：TTL检查、过期取消、状态更新"""
        if not self.active_orders:
            return
            
        current_time = time.time()
        expired_orders = []
        
        for order_id, order_info in self.active_orders.items():
            # 检查订单是否过期
            if current_time - order_info['create_time'] > order_info['ttl']:
                expired_orders.append(order_id)
                
        # 处理过期订单
        for order_id in expired_orders:
            order_info = self.active_orders[order_id]
            symbol = order_info['symbol']
            
            try:
                # 检查订单状态
                order_status = self.trader.http.get('/fapi/v1/order', {
                    'symbol': symbol,
                    'orderId': order_id
                })
                
                if order_status and order_status.get('status') in ['NEW', 'PARTIALLY_FILLED']:
                    # 取消过期的限价单
                    self.log.warning(f"取消过期限价单: {symbol} 订单ID:{order_id} (TTL:{order_info['ttl']}秒)")
                    cancel_result = self.trader.http.delete('/fapi/v1/order', {
                        'symbol': symbol,
                        'orderId': order_id
                    })
                    
                    if cancel_result:
                        self.log.info(f"成功取消过期订单: {symbol} {order_id}")
                    else:
                        self.log.error(f"取消订单失败: {symbol} {order_id}")
                        
                elif order_status and order_status.get('status') == 'FILLED':
                    self.log.info(f"订单已成交: {symbol} {order_id}")
                    
            except Exception as e:
                self.log.error(f"检查订单状态失败: {symbol} {order_id}, 错误: {e}")
                
            # 从跟踪列表中移除
            del self.active_orders[order_id]
    
    def _check_stop_order_failures(self):
        """检查所有持仓的止损单状态并告警"""
        try:
            # 获取所有实际持仓
            positions = self.trader.get_positions()
            active_positions = []
            
            for pos in positions:
                qty = float(pos.get('positionAmt', 0))
                if abs(qty) > 0.001:  # 过滤掉数量极小的持仓
                    symbol = pos.get('symbol')
                    entry_price = float(pos.get('entryPrice', 0))
                    
                    active_positions.append({
                        'symbol': symbol,
                        'qty': qty,
                        'entry_price': entry_price
                    })
            
            # 检查每个持仓的止损单状态
            for pos in active_positions:
                symbol = pos['symbol']
                qty = pos['qty']
                entry_price = pos['entry_price']
                
                try:
                    # 检查当前是否有止损单
                    open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': symbol})
                    
                    has_stop_order = False
                    if open_orders:
                        for order in open_orders:
                            if order.get('type') in ['STOP_MARKET', 'STOP']:
                                has_stop_order = True
                                break
                                
                    # 如果有持仓但没有止损单，发出告警并补挂
                    if not has_stop_order:
                        self.log.error(f"告警: {symbol} 持仓缺少止损单保护！")
                        
                        # 获取当前价格
                        current_price = float(self.trader.get_symbol_ticker(symbol)['price'])
                        
                        # 计算止损价格（基于当前价格的3%止损）
                        if qty > 0:  # 多头持仓
                            stop_price = current_price * 0.97
                        else:  # 空头持仓
                            stop_price = current_price * 1.03
                        
                        # 尝试重新挂止损单
                        try:
                            stop_order = self.trader.place_stop_loss_order(symbol, abs(qty), stop_price)
                            if stop_order and stop_order.get('orderId'):
                                self.log.info(f"已重新挂止损单: {symbol} @ {stop_price}")
                            else:
                                self.log.error(f"重新挂止损单失败: {symbol}")
                                
                        except Exception as e:
                            self.log.error(f"重新挂止损单异常: {symbol}, 错误: {e}")
                            
                except Exception as e:
                    self.log.error(f"检查 {symbol} 止损单状态失败: {e}")
                    
        except Exception as e:
            self.log.error(f"检查所有持仓止损单失败: {e}")
    
    def _check_network_status(self):
        """检测API连接状态和延迟"""
        current_time = time.time()
        
        # 检查是否需要进行网络检测
        if current_time - self.last_network_check < self.network_check_interval:
            return self.network_status
            
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 发送轻量级API请求测试连接
            response = self.trader.http.get('/fapi/v1/ping')
            
            # 计算延迟
            latency = (time.time() - start_time) * 1000  # 转换为毫秒
            
            if response is not None:
                self.network_status = {
                    'connected': True,
                    'latency': round(latency, 2),
                    'last_error': None
                }
                
                # 如果延迟过高，记录警告
                if latency > 2000:  # 2秒
                    self.log.warning(f"API延迟过高: {latency:.2f}ms")
                elif latency > 1000:  # 1秒
                    self.log.info(f"API延迟较高: {latency:.2f}ms")
                    
            else:
                self.network_status = {
                    'connected': False,
                    'latency': 0,
                    'last_error': 'API响应为空'
                }
                self.log.error("API连接失败: 响应为空")
                
        except Exception as e:
            self.network_status = {
                'connected': False,
                'latency': 0,
                'last_error': str(e)
            }
            self.log.error(f"网络检测失败: {e}")
            
        finally:
            self.last_network_check = current_time
            
        return self.network_status
    
    def _is_network_healthy(self):
        """检查网络是否健康"""
        status = self._check_network_status()
        
        # 网络不健康的条件
        if not status['connected']:
            return False
            
        if status['latency'] > 5000:  # 延迟超过5秒
            return False
            
        return True
    
    def check_orphan_orders(self, symbol):
        """检查孤儿订单"""
        try:
            # 获取当前所有挂单
            open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': symbol})
            if open_orders:
                self.log.warning(f"发现 {len(open_orders)} 个挂单可能占用保证金:")
                for order in open_orders:
                    order_id = order.get('orderId')
                    order_type = order.get('type')
                    side = order.get('side')
                    status = order.get('status')
                    self.log.warning(f"  订单ID: {order_id}, 类型: {order_type}, 方向: {side}, 状态: {status}")
                    
                    # 检查是否为孤儿订单（超过5分钟未成交的限价单）
                    order_time = order.get('time', 0) / 1000
                    if time.time() - order_time > 300 and order_type == 'LIMIT':
                        self.log.warning(f"  发现孤儿订单，建议取消: {order_id}")
            else:
                self.log.info(f"未发现 {symbol} 的挂单")
        except Exception as e:
            self.log.error(f"检查挂单失败: {e}")
            
    def check_orphan_stop_orders(self, symbol=None):
        """检查孤儿止损/止盈单 - 识别无对应持仓的止损止盈单"""
        orphan_orders = []
        try:
            if symbol:
                # 检查单个交易对
                symbols_to_check = [symbol]
            else:
                # 检查所有有挂单的交易对
                all_orders = self.trader.http.get('/fapi/v1/openOrders')
                symbols_to_check = list(set([order['symbol'] for order in all_orders if order.get('type') in ['STOP_MARKET', 'TAKE_PROFIT_MARKET']]))
            
            for sym in symbols_to_check:
                # 获取当前持仓
                position = self.trader.get_position(sym)
                position_amt = float(position['positionAmt']) if position else 0
                
                # 获取该交易对的止损/止盈单
                open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': sym})
                stop_profit_orders = [o for o in open_orders if o.get('type') in ['STOP_MARKET', 'TAKE_PROFIT_MARKET']]
                
                if stop_profit_orders and abs(position_amt) < 0.001:
                    # 无持仓但有止损/止盈单，这些是孤儿单
                    for order in stop_profit_orders:
                        orphan_orders.append({
                            'symbol': sym,
                            'orderId': order['orderId'],
                            'type': order['type'],
                            'side': order['side'],
                            'origQty': order['origQty'],
                            'stopPrice': order.get('stopPrice', order.get('price', 'N/A'))
                        })
                        self.log.warning(f"发现孤儿{order['type']}单: {sym} {order['orderId']} - 无对应持仓")
                        
        except Exception as e:
            self.log.error(f"检查孤儿止损/止盈单失败: {e}")
            
        return orphan_orders
    
    def cancel_order_with_confirmation(self, symbol, order_id, max_retries=3):
        """取消订单并确认取消成功
        Args:
            symbol: 交易对符号
            order_id: 订单ID
            max_retries: 最大重试次数
        Returns:
            bool: 是否成功取消
        """
        for attempt in range(max_retries):
            try:
                # 尝试取消订单
                cancel_result = self.trader.cancel_order(symbol, order_id)
                if cancel_result:
                    # 等待一小段时间
                    time.sleep(0.5)
                    
                    # 确认订单是否真的被取消
                    try:
                        order_status = self.trader.get_order(symbol, order_id)
                        if order_status and order_status.get('status') in ['CANCELED', 'EXPIRED']:
                            self.log.info(f"订单 {order_id} 取消确认成功")
                            return True
                        elif order_status and order_status.get('status') == 'FILLED':
                            self.log.warning(f"订单 {order_id} 在取消前已成交")
                            return True  # 已成交也算成功处理
                        else:
                            self.log.warning(f"订单 {order_id} 取消后状态异常: {order_status.get('status') if order_status else 'None'}")
                    except Exception as e:
                        self.log.error(f"确认订单 {order_id} 取消状态失败: {e}")
                else:
                    self.log.warning(f"取消订单 {order_id} 失败，尝试 {attempt + 1}/{max_retries}")
                    
            except Exception as e:
                self.log.error(f"取消订单 {order_id} 异常: {e}，尝试 {attempt + 1}/{max_retries}")
                
            if attempt < max_retries - 1:
                time.sleep(1)  # 重试前等待
                
        self.log.error(f"订单 {order_id} 取消失败，已达最大重试次数")
        return False
             
    def check_redundant_orders(self, symbol):
        """检查多余挂单"""
        try:
            # 获取当前持仓
            position = self.trader.get_position(symbol)
            if not position or float(position['positionAmt']) == 0:
                self.log.info(f"{symbol} 无持仓，检查是否有多余的止损/止盈单")
                # 如果无持仓但有止损止盈单，应该清理
                open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': symbol})
                if open_orders:
                    for order in open_orders:
                        order_type = order.get('type')
                        if order_type in ['STOP_MARKET', 'TAKE_PROFIT_MARKET']:
                            order_id = order.get('orderId')
                            self.log.warning(f"发现无持仓的止损/止盈单，建议取消: {order_id}")
            else:
                self.log.info(f"{symbol} 有持仓 {position['positionAmt']}，检查止损止盈单数量")
                # 检查是否有重复的止损止盈单
                open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': symbol})
                stop_orders = [o for o in open_orders if o.get('type') == 'STOP_MARKET']
                profit_orders = [o for o in open_orders if o.get('type') == 'TAKE_PROFIT_MARKET']
                
                if len(stop_orders) > 1:
                    self.log.warning(f"发现多个止损单 ({len(stop_orders)}个)，应只保留最优价格")
                if len(profit_orders) > 1:
                    self.log.warning(f"发现多个止盈单 ({len(profit_orders)}个)，应只保留最优价格")
                    
        except Exception as e:
             self.log.error(f"检查多余挂单失败: {e}")
             
    def clean_orphan_orders(self, symbol, include_stop_orders=True):
        """自动清理孤儿订单
        Args:
            symbol: 交易对符号
            include_stop_orders: 是否包含孤儿止损/止盈单的清理
        """
        cleaned_count = 0
        try:
            open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': symbol})
            if not open_orders:
                return cleaned_count
                
            current_time = time.time()
            
            # 获取当前持仓状态
            position = self.trader.get_position(symbol) if include_stop_orders else None
            position_amt = float(position['positionAmt']) if position else 0
            
            for order in open_orders:
                order_id = order.get('orderId')
                order_type = order.get('type')
                order_time = order.get('time', 0) / 1000
                should_cancel = False
                cancel_reason = ""
                
                # 清理超过5分钟未成交的限价单
                if (current_time - order_time > 300 and 
                    order_type == 'LIMIT' and 
                    order.get('status') == 'NEW'):
                    should_cancel = True
                    cancel_reason = f"超时未成交({(current_time - order_time):.0f}秒)"
                
                # 清理孤儿止损/止盈单（无对应持仓）
                elif (include_stop_orders and 
                      order_type in ['STOP_MARKET', 'TAKE_PROFIT_MARKET'] and 
                      abs(position_amt) < 0.001):
                    should_cancel = True
                    cancel_reason = "无对应持仓的孤儿单"
                
                if should_cancel:
                    self.log.info(f"自动取消孤儿订单: {order_id} ({cancel_reason})")
                    if self.cancel_order_with_confirmation(symbol, order_id):
                        cleaned_count += 1
                        self.log.info(f"已确认取消孤儿订单: {order_id}")
                    else:
                        self.log.error(f"取消孤儿订单失败: {order_id}")
                        
        except Exception as e:
            self.log.error(f"清理孤儿订单失败: {e}")
            
        return cleaned_count
        
    def clean_redundant_orders(self, symbol):
        """自动清理多余挂单"""
        cleaned_count = 0
        try:
            # 获取当前持仓
            position = self.trader.get_position(symbol)
            position_amt = float(position['positionAmt']) if position else 0
            
            open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': symbol})
            if not open_orders:
                return cleaned_count
                
            # 如果无持仓，清理所有止损止盈单
            if position_amt == 0:
                for order in open_orders:
                    order_type = order.get('type')
                    if order_type in ['STOP_MARKET', 'TAKE_PROFIT_MARKET']:
                        order_id = order.get('orderId')
                        self.log.info(f"清理无持仓的{order_type}单: {order_id}")
                        cancel_result = self.trader.cancel_order(symbol, order_id)
                        if cancel_result:
                            cleaned_count += 1
                            self.log.info(f"成功清理无持仓订单: {order_id}")
            else:
                # 如果有持仓，清理多余的止损止盈单（保留最新的）
                stop_orders = [o for o in open_orders if o.get('type') == 'STOP_MARKET']
                profit_orders = [o for o in open_orders if o.get('type') == 'TAKE_PROFIT_MARKET']
                
                # 保留最新的止损单，清理其他
                if len(stop_orders) > 1:
                    stop_orders.sort(key=lambda x: x.get('time', 0), reverse=True)
                    for order in stop_orders[1:]:  # 跳过最新的
                        order_id = order.get('orderId')
                        self.log.info(f"清理多余止损单: {order_id}")
                        cancel_result = self.trader.cancel_order(symbol, order_id)
                        if cancel_result:
                            cleaned_count += 1
                            
                # 保留最新的止盈单，清理其他
                if len(profit_orders) > 1:
                    profit_orders.sort(key=lambda x: x.get('time', 0), reverse=True)
                    for order in profit_orders[1:]:  # 跳过最新的
                        order_id = order.get('orderId')
                        self.log.info(f"清理多余止盈单: {order_id}")
                        cancel_result = self.trader.cancel_order(symbol, order_id)
                        if cancel_result:
                            cleaned_count += 1
                            
        except Exception as e:
            self.log.error(f"清理多余挂单失败: {e}")
            
        return cleaned_count
        
    def trigger_margin_alert(self, symbol, current_balance, required_margin):
        """触发保证金不足警报"""
        alert_msg = f"【保证金不足警报】{symbol} 开仓失败 - 可用:{current_balance:.2f} 需要:{required_margin:.2f}"
        self.log.error(alert_msg)
        
        # 记录到专门的警报日志
        alert_logger = logging.getLogger('MarginAlert')
        alert_logger.error(alert_msg)
        
        # 可以在这里添加更多警报机制，如:
        # - 发送邮件通知
        # - 发送微信/钉钉消息
        # - 写入专门的警报文件
        # - 暂停策略运行等
        
        # 暂时停止该币种的重复尝试（避免刷屏）
        if not hasattr(self, 'margin_alert_symbols'):
            self.margin_alert_symbols = set()
        self.margin_alert_symbols.add(symbol)
        
        self.log.info(f"已将 {symbol} 加入保证金警报列表，暂停重复尝试")

    def daily_close(self):
        if self.pos:
            self.trader.open_position(self.pos['symbol'], 'SELL', self.pos['qty'], price=self.pos['entry'] * 1.0001, timeInForce='GTC')
            self.log.info("14:55 强制平仓完成")
            self.pos = None
            self.add_count = 0

    def select_top3(self, candidate_list):
        """
        对候选池进行排序：
        1. 总分降序
        2. 深度降序（0.1 % 深度）
        3. 币龄升序（新币优先）
        只留前 3 名，其余立即平仓（若持仓中）。
        """
        if not candidate_list:
            return []
            
        # 实时拉深度（缓存 5 min 内）
        scored_candidates = []
        for symbol in candidate_list:
            if symbol in self.cand_cache:
                data = self.cand_cache[symbol]
                # 获取最新深度
                depth = self.cache.load_depth(symbol)
                if depth is None:
                    depth = self.get_depth01pct(symbol)
                    self.cache.save_depth(symbol, depth)
                
                # 构建候选数据
                candidate_data = {
                    'symbol': symbol,
                    'score': data.get('score', 0),
                    'depth': depth,
                    'age_days': data.get('age', 500),
                    'price': data.get('price', 0)
                }
                scored_candidates.append(candidate_data)
        
        if not scored_candidates:
            return []
            
        # 排序键：总分 ↓ 深度 ↓ 币龄 ↑
        scored_candidates.sort(key=lambda x: (
            x['score'],
            x['depth'],
            -x['age_days']
        ), reverse=True)

        top3 = scored_candidates[:3]
        top3_symbols = {c['symbol'] for c in top3}

        # 持仓中但不在 top3 → 立即平仓
        if self.pos and self.pos.get('symbol') not in top3_symbols:
            symbol_to_close = self.pos['symbol']
            self.log.info(f"末位淘汰: {symbol_to_close} 不在前3名，立即平仓")
            # 市价平仓
            try:
                self.trader.open_position(symbol_to_close, 'SELL', self.pos['qty'], timeInForce='IOC')
                self.pos = None
                self.add_count = 0
                self.log.info(f"末位淘汰平仓完成: {symbol_to_close}")
            except Exception as e:
                self.log.error(f"末位淘汰平仓失败: {symbol_to_close}, 错误: {e}")

        return top3

    def incremental_scan(self):
        """优化后的增量扫描：轻量并行处理 + 末位淘汰"""
        current_time = time.time()
        now_utc = pd.Timestamp.utcnow()
        
        # ① 每日冷启动：只拉基础信息（00:10执行）
        if now_utc.hour == 0 and now_utc.minute == 10 and current_time - self.last_full_scan > 3600:
            self.log.info("执行每日冷启动：仅更新全币种基础信息")
            symbols_info = self.cold_start_symbols()
            self.cache.save_symbols(symbols_info)
            self.last_full_scan = current_time
        
        # ② 持仓币轻量更新（1分钟频率）
        if self.pos:
            symbol = self.pos['symbol']
            # 只拉50根1分钟K线
            df = self.get_klines(symbol, '1m', 50)
            if not df.empty:
                current_price = df['c'].iloc[-1]
                self.check_position_conditions(symbol, current_price)
        
        # ③ 候选池并行更新（15分钟频率） + 末位淘汰
        candidates = list(self.cand_cache.keys())[:20]  # 只处理前20个高评分候选
        if current_time - self.last_candidate_scan > 900 and candidates:  # 15分钟
            self.last_candidate_scan = current_time
            self.log.info(f"并行更新候选池: {len(candidates)} 个币种")
            self.parallel_update(candidates, '15m', 50)  # 只拉50根K线
            
            # 末位淘汰：只留前3名
            top3 = self.select_top3(candidates)
            if top3:
                self.log.info(f"末位淘汰完成: {len(candidates)} → {len(top3)} 个币种")
                for i, candidate in enumerate(top3):
                    self.log.info(f"Top{i+1}: {candidate['symbol']} 得分:{candidate['score']} 深度:{candidate['depth']:.0f}$ 币龄:{candidate['age_days']:.0f}天")
        
        # ④ 深度数据缓存更新（5分钟频率）
        if current_time % 300 < 30:  # 每5分钟
            # 只更新持仓币和候选池的深度
            symbols_to_update = []
            if self.pos:
                symbols_to_update.append(self.pos['symbol'])
            symbols_to_update.extend(candidates[:10])  # 只更新前10个候选
            
            for symbol in symbols_to_update:
                depth = self.get_depth01pct(symbol)
                self.cache.save_depth(symbol, depth)

    def update_symbol(self, symbol, interval, limit):
        """更新单个币种数据并重新评分"""
        try:
            # 获取K线数据
            df = self.get_klines(symbol, interval, limit)
            if df.empty:
                self.log.warning(f"{symbol} K线数据为空")
                return
            
            # 计算币龄 - 优先使用onboardDate，回退到K线数据
            age_days = 500  # 默认值
            current_time = pd.Timestamp.utcnow()
            if current_time.tz is None:
                current_time = current_time.tz_localize('UTC')
            
            try:
                # 优先使用缓存的onboardDate
                symbols_info = self.cache.load_symbols(self.trader)
                symbol_info = next((s for s in symbols_info if s['symbol'] == symbol), None)
                
                if symbol_info and 'age_days' in symbol_info:
                    age_days = symbol_info['age_days']
                    self.log.debug(f"{symbol} 使用缓存的币龄: {age_days}天")
                else:
                    # 回退到API获取onboardDate
                    symbol_info_api = self.trader.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
                    if (symbol_info_api and 'symbols' in symbol_info_api and 
                        len(symbol_info_api['symbols']) > 0 and 
                        'onboardDate' in symbol_info_api['symbols'][0]):
                        
                        onboard_timestamp = symbol_info_api['symbols'][0]['onboardDate']
                        launch_ts = pd.to_datetime(onboard_timestamp, unit='ms')
                        if launch_ts.tz is None:
                            launch_ts = launch_ts.tz_localize('UTC')
                        age_days = (current_time - launch_ts).days
                        self.log.debug(f"{symbol} 使用API的onboardDate计算币龄: {age_days}天")
                    else:
                        # 最后回退到K线数据
                        if not df.empty:
                            first_time = df.index[0]
                            if hasattr(first_time, 'tz') and first_time.tz is None:
                                first_time = first_time.tz_localize('UTC')
                            elif not hasattr(first_time, 'tz'):
                                first_time = pd.Timestamp(first_time).tz_localize('UTC')
                            
                            age_days = (current_time - first_time).days
                            self.log.debug(f"{symbol} 使用K线数据计算币龄: {age_days}天")
            except Exception as e:
                self.log.warning(f"{symbol} 币龄计算异常: {e}, 使用默认值: {age_days}天")
            
            # 重新评分
            score, M = self.score_symbol(df, age_days)
            
            # 更新缓存
            depth = self.get_depth01pct(symbol)
            symbol_data = {
                'symbol': symbol,
                'score': score,
                'M': M,
                'age': age_days,
                'depth': depth,
                'price': df['c'].iloc[-1] if not df.empty else 0,
                'timestamp': time.time()
            }
            
            self.cand_cache[symbol] = symbol_data
            
            # 如果评分≥7且未持仓，加入候选池
            if score >= 7 and symbol not in self.cache.load_positions():
                current_candidates = self.cache.load_candidates()
                if symbol not in current_candidates:
                    current_candidates.append(symbol)
                    self.cache.save_candidates(current_candidates)
            
            self.log.debug(f"{symbol} 更新完成: 评分={score}, M={M}, 币龄={age_days}天")
            
        except Exception as e:
            self.log.error(f"更新币种{symbol}失败: {e}")



    def parallel_scan(self, symbols):
        """并行扫描候选池币种"""
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        # 先筛选出评分≥7的币种
        high_score_symbols = []
        for symbol in symbols:
            if symbol in self.cand_cache and self.cand_cache[symbol].get('score', 0) >= 7:
                high_score_symbols.append(symbol)
        
        if not high_score_symbols:
            self.log.info("候选池中没有评分≥7的币种，跳过扫描")
            return
        
        self.log.info(f"并行扫描 {len(high_score_symbols)} 个高评分币种: {', '.join(high_score_symbols)}")
        
        # 使用线程池并行扫描
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = {executor.submit(self.update_symbol, symbol, '15m', 200): symbol for symbol in high_score_symbols}
            
            for future in as_completed(futures):
                symbol = futures[future]
                try:
                    future.result()  # 获取结果，异常会抛出
                except Exception as e:
                    self.log.error(f"并行扫描币种 {symbol} 失败: {e}")

    def cold_start_symbols(self):
        """每日一次：仅拉取交易对精度等信息，不拉 K 线"""
        try:
            info = self.trader.http.get('/fapi/v1/exchangeInfo')
            if not info or 'symbols' not in info:
                return []
            symbols = [s for s in info['symbols']
                       if s['contractType'] == 'PERPETUAL'
                       and s['status'] == 'TRADING'
                       and s['symbol'].endswith('USDT')]
            # 只保存精度、上线日
            symbol_data = []
            for s in symbols:
                try:
                    # 计算币龄
                    age_days = 500
                    if 'onboardDate' in s:
                        onboard_timestamp = s['onboardDate']
                        launch_ts = pd.to_datetime(onboard_timestamp, unit='ms')
                        if launch_ts.tz is None:
                            launch_ts = launch_ts.tz_localize('UTC')
                        age_days = (pd.Timestamp.utcnow() - launch_ts).days
                    
                    symbol_data.append({
                        'symbol': s['symbol'],
                        'tick_size': float(s['filters'][0]['tickSize']),
                        'step_size': float(s['filters'][1]['stepSize']),
                        'min_qty': float(s['filters'][1]['minQty']),
                        'onboardDate': s.get('onboardDate', 0),
                        'age_days': age_days
                    })
                except Exception as e:
                    self.log.warning(f"处理交易对{s['symbol']}信息失败: {e}")
                    continue
            
            self.log.info(f"冷启动完成，共 {len(symbol_data)} 个交易对基础信息")
            return symbol_data
        except Exception as e:
            self.log.error(f"冷启动获取交易对信息失败: {e}")
            return []

    def parallel_update(self, symbols, interval='15m', limit=50):
        """并行更新候选池币种数据"""
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        if not symbols:
            self.log.warning("parallel_update: 传入的币种列表为空")
            return
            
        self.log.debug(f"开始并行更新 {len(symbols)} 个币种，间隔: {interval}, K线数量: {limit}")
        
        def _update_one(symbol):
            """单个币种的更新任务"""
            try:
                # 获取K线数据（小数据量）
                df = self.get_klines(symbol, interval, limit)
                if df.empty:
                    self.log.debug(f"{symbol}: K线数据为空")
                    return None
                
                # 设置DataFrame的name属性，供检查方法使用
                df.name = symbol
                
                # 先检查通道突破条件 - 硬开关
                if not self.check_channel_breakthrough(df):
                    self.log.info(f"{symbol}: 未通过通道突破检查，跳过")
                    return None
                else:
                    self.log.info(f"{symbol}: 通过通道突破检查，继续评分")
                
                # 获取币龄（分钟级）- get_klines返回的时间戳已经是UTC时区
                current_time = pd.Timestamp.utcnow()
                # 确保current_time有时区信息
                if current_time.tz is None:
                    current_time = current_time.tz_localize('UTC')
                
                if not df.empty:
                    first_time = df.index[0]
                    # 确保first_time有时区信息，使用安全的属性检查
                    try:
                        if hasattr(first_time, 'tz') and first_time.tz is None:
                            first_time = first_time.tz_localize('UTC')
                        elif not hasattr(first_time, 'tz'):
                            # 如果没有tz属性，转换为带时区的Timestamp
                            first_time = pd.Timestamp(first_time).tz_localize('UTC')
                    except Exception as tz_e:
                        # 如果时区处理失败，使用默认值
                        self.log.debug(f"{symbol}: 时区处理失败，使用默认币龄 - {tz_e}")
                        age_days = 500
                    else:
                        age_min = int((current_time - first_time).total_seconds() // 60)
                        age_days = age_min / (60 * 24)  # 转换为天数用于兼容性
                else:
                    age_days = 500  # 默认值
                
                # 评分
                try:
                    score, M = self.score_symbol(df, age_days)
                except Exception as score_e:
                    self.log.error(f"{symbol}: 评分计算失败 - {score_e}")
                    return None
                
                # 获取深度数据
                try:
                    depth = self.cache.load_depth(symbol)
                    if depth is None:
                        depth = self.get_depth01pct(symbol)
                        if depth is not None:
                            self.cache.save_depth(symbol, depth)
                        else:
                            self.log.debug(f"{symbol}: 深度数据获取失败")
                            return None
                except Exception as depth_e:
                    self.log.error(f"{symbol}: 深度数据处理失败 - {depth_e}")
                    return None
                
                result = {
                    'symbol': symbol, 
                    'score': score, 
                    'M': M, 
                    'age': age_days, 
                    'depth': depth,
                    'price': df['c'].iloc[-1] if not df.empty else 0,
                    'timestamp': time.time()
                }
                
                self.log.debug(f"{symbol}: 更新成功 (评分={score:.2f}, M={M:.4f}, 深度={depth:.0f}$, 币龄={age_days:.1f}天)")
                return result
                
            except KeyError as e:
                self.log.error(f"{symbol}: 数据字段缺失 - {e}")
                return None
            except ValueError as e:
                self.log.error(f"{symbol}: 数值转换错误 - {e}")
                return None
            except Exception as e:
                self.log.error(f"{symbol}: 更新失败 - {type(e).__name__}: {e}")
                return None
        
        # 并行处理
        start_time = time.time()
        success_count = 0
        error_count = 0
        timeout_count = 0
        min_score = self.cfg.get('min_score', 7)
        
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = {executor.submit(_update_one, symbol): symbol for symbol in symbols}
            
            for future in as_completed(futures, timeout=60):  # 60秒总超时
                symbol = futures[future]
                try:
                    result = future.result(timeout=10)  # 单个任务10秒超时
                    if result:
                        if result['score'] >= min_score:
                            self.cand_cache[result['symbol']] = result
                            success_count += 1
                            self.log.debug(f"{symbol}: 已加入候选池 (评分={result['score']:.2f})")
                        else:
                            self.log.debug(f"{symbol}: 评分过低 ({result['score']:.2f} < {min_score})，未加入候选池")
                    else:
                        error_count += 1
                        
                except TimeoutError:
                    timeout_count += 1
                    self.log.warning(f"{symbol}: 更新超时")
                    future.cancel()
                except Exception as e:
                    error_count += 1
                    self.log.error(f"{symbol}: 处理结果失败 - {type(e).__name__}: {e}")
            
            elapsed_time = time.time() - start_time
            total_processed = success_count + error_count + timeout_count
            
            self.log.info(f"并行更新完成: {success_count}个成功/{total_processed}个处理 (错误:{error_count}, 超时:{timeout_count}) 耗时:{elapsed_time:.1f}秒")
            
            if error_count > len(symbols) * 0.3:  # 错误率超过30%
                self.log.warning(f"并行更新错误率过高: {error_count}/{len(symbols)} ({error_count/len(symbols)*100:.1f}%)")

    def check_position_conditions(self, symbol, current_price):
        """检查持仓币的各种条件（1分钟频率）"""
        if not self.pos or self.pos['symbol'] != symbol:
            return
            
        profit_pct = (current_price - self.pos['entry']) / self.pos['entry']
        
        # 利润雪崩加仓（浮盈≥20%）
        if profit_pct >= 0.2 and self.pos['add_count'] < self.cfg['max_add']:
            # 基于当前持仓价值计算加仓数量，而不是初始名义价值
            current_position_value = self.pos['qty'] * current_price
            add_nominal = current_position_value * self.cfg['add_ratio'][min(self.pos['add_count'], len(self.cfg['add_ratio'])-1)]
            add_size = add_nominal / current_price
            add_size = self.trader._round_quantity(add_size, {'step_size': 0.001, 'min_qty': 0.001})
            
            # 确保加仓数量不超过最大限制
            symbol_info = self.trader.symbols.get(symbol, {})
            max_qty = symbol_info.get('max_qty', 1000000)
            if add_size > max_qty:
                add_size = max_qty
                self.log.warning(f"{symbol} 加仓数量超过最大限制，调整为: {add_size}")
            
            self.trader.open_position(symbol, 'BUY', add_size, price=current_price * 0.999, timeInForce='GTC')
            self.pos['qty'] += add_size
            self.pos['add_count'] += 1
            self.log.info(f"{symbol} 加仓 {add_size} @ {current_price}")
        
        # 30%固定止盈（浮盈≥60%）
        if profit_pct >= 0.6 and self.pos['qty'] > 0:
            take_size = self.pos['qty'] * 0.3
            # 确保不会卖出超过持仓数量的币
            if take_size <= self.pos['qty']:
                self.trader.open_position(symbol, 'SELL', take_size, price=current_price * 0.999, timeInForce='GTC')
                self.log.info(f"{symbol} 固定止盈 30% {take_size}")
            else:
                self.log.warning(f"{symbol} 固定止盈失败：计算的卖出数量({take_size})超过持仓数量({self.pos['qty']})")
        
        # 降档逃顶检查（深度/ATR突变）
        depth_val = self.get_depth01pct(symbol)
        df_1m = self.get_klines(symbol, '1m', 50)
        if not df_1m.empty:
            atr_val = self.calculate_atr(df_1m['h'], df_1m['l'], df_1m['c'], length=14).iloc[-1]
            exit_pct = 0
            if depth_val < 80000 or atr_val > df_1m['c'].rolling(60).std().iloc[-1] * 2:
                exit_pct += 0.25
            if exit_pct > 0 and self.pos['qty'] > 0:
                exit_size = self.pos['qty'] * exit_pct
                # 确保不会卖出超过持仓数量的币
                if exit_size <= self.pos['qty']:
                    self.trader.open_position(symbol, 'SELL', exit_size, price=current_price * 0.999, timeInForce='GTC')
                    self.pos['qty'] -= exit_size
                    self.pos['exit_pct_history'] = exit_pct
                    self.log.info(f"{symbol} 降档逃顶 平{exit_pct*100:.0f}%")
                else:
                    self.log.warning(f"{symbol} 降档逃顶失败：计算的卖出数量({exit_size})超过持仓数量({self.pos['qty']})")
        
        # 复位条件（价格突破+深度充足+波动正常）
        if not df_1m.empty:
            if (current_price > df_1m['h'].rolling(20).max().iloc[-1]) and (depth_val > 200000) and (atr_val < df_1m['c'].rolling(60).std().iloc[-1] * 1.5) and self.pos['exit_pct_history'] > 0:
                # 基于当前持仓价值计算复位数量，而不是初始名义价值
                current_position_value = self.pos['qty'] * current_price
                reset_nominal = current_position_value * self.pos['exit_pct_history']
                reset_size = reset_nominal / current_price
                reset_size = self.trader._round_quantity(reset_size, {'step_size': 0.001, 'min_qty': 0.001})
                
                # 确保复位数量不超过最大限制
                symbol_info = self.trader.symbols.get(symbol, {})
                max_qty = symbol_info.get('max_qty', 1000000)
                if reset_size > max_qty:
                    reset_size = max_qty
                    self.log.warning(f"{symbol} 复位数量超过最大限制，调整为: {reset_size}")
                
                self.trader.open_position(symbol, 'BUY', reset_size, price=current_price * 1.001, timeInForce='GTC')
                self.pos['qty'] += reset_size
                self.pos['exit_pct_history'] = 0
                self.log.info(f"{symbol} 自动复位买回 {reset_size}")
        
        # 保本硬止损（价格≤成本+0.1%）
        if current_price <= self.pos['stop']:
            self.trader.open_position(symbol, 'SELL', self.pos['qty'], price=current_price * 0.999, timeInForce='GTC')
            self.log.info(f"{symbol} 保本止损 全平")
            self.pos = None
            self.add_count = 0
            
        # 检查持仓数量是否过小（小于最小订单限制）
        if self.pos and self.pos['qty'] * current_price < 5:  # 5 USDT最小订单限制
            self.log.info(f"{symbol} 持仓数量过小 ({self.pos['qty'] * current_price:.2f} USDT)，清除持仓状态")
            self.pos = None
            self.add_count = 0

    def get_symbol_age_minutes(self, symbol):
        """获取币龄（分钟数），优先使用缓存的基础信息"""
        try:
            # 先尝试从缓存的全币种信息获取
            symbols_info = self.cache.load_symbols(self.trader)
            symbol_info = next((s for s in symbols_info if s['symbol'] == symbol), None)
            
            if symbol_info and 'age_days' in symbol_info:
                # 将天数转换为分钟数
                return symbol_info['age_days'] * 24 * 60
            
            # 回退到API获取
            symbol_info_api = self.trader.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
            if symbol_info_api and 'symbols' in symbol_info_api and len(symbol_info_api['symbols']) > 0:
                if 'onboardDate' in symbol_info_api['symbols'][0]:
                    onboard_timestamp = symbol_info_api['symbols'][0]['onboardDate']
                    launch_ts = pd.to_datetime(onboard_timestamp, unit='ms')
                    if launch_ts.tz is None:
                        launch_ts = launch_ts.tz_localize('UTC')
                    now_utc = pd.Timestamp.utcnow()
                    if launch_ts.tz is not None:
                        now_utc = now_utc.tz_localize('UTC')
                    age_minutes = int((now_utc - launch_ts).total_seconds() // 60)
                    return age_minutes
            
            # 最后回退到K线数据
            k0 = self.get_klines(symbol, '1d', 50)  # 减少数据量
            if not k0.empty:
                launch_ts = k0.index[0]  # get_klines返回的时间戳已经是UTC时区
                now_utc = pd.Timestamp.utcnow()
                if launch_ts.tz is not None:
                    now_utc = now_utc.tz_localize('UTC')
                age_minutes = int((now_utc - launch_ts).total_seconds() // 60)
                return age_minutes
            
            return 500 * 24 * 60  # 默认值（500天转换为分钟）
        except Exception as e:
            self.log.error(f"获取{symbol}币龄（分钟）失败: {e}")
            return 500 * 24 * 60

    def age_score(self, age_min):
        """分钟级年龄评分"""
        if 60*24*7 <= age_min < 60*24*365:   # 7天-365天
            return 2
        elif age_min >= 60*24*365:           # >365天
            return 1
        elif 60*6 <= age_min < 60*24*7:      # 6小时-7天（新币也满分）
            return 2
        else:                                # <6小时
            return 1
    
    def _global_orphan_check(self):
        """全局孤儿订单检查和清理"""
        try:
            self.log.info("开始全局孤儿订单检查...")
            
            # 检查所有交易对的孤儿止损/止盈单
            all_orphan_orders = self.check_orphan_stop_orders()  # 不传symbol参数，检查所有
            
            if not all_orphan_orders:
                self.log.debug("全局孤儿订单检查完成，未发现孤儿订单")
                return
            
            self.log.warning(f"发现 {len(all_orphan_orders)} 个孤儿止损/止盈单，开始清理...")
            
            # 按交易对分组清理
            orphan_by_symbol = {}
            for orphan in all_orphan_orders:
                symbol = orphan['symbol']
                if symbol not in orphan_by_symbol:
                    orphan_by_symbol[symbol] = []
                orphan_by_symbol[symbol].append(orphan)
            
            total_cleaned = 0
            for symbol, orders in orphan_by_symbol.items():
                self.log.info(f"清理 {symbol} 的 {len(orders)} 个孤儿订单...")
                
                # 使用现有的清理方法
                cleaned_count = self.clean_orphan_orders(symbol, include_stop_orders=True)
                total_cleaned += cleaned_count
                
                if cleaned_count > 0:
                    self.log.info(f"✅ {symbol}: 成功清理 {cleaned_count} 个孤儿订单")
                else:
                    self.log.warning(f"❌ {symbol}: 清理失败或无订单被清理")
                
                time.sleep(0.5)  # 避免频繁操作
            
            if total_cleaned > 0:
                self.log.info(f"全局孤儿订单清理完成！总共清理了 {total_cleaned} 个订单")
            else:
                self.log.warning("全局孤儿订单清理完成，但没有订单被清理")
                
        except Exception as e:
            self.log.error(f"全局孤儿订单检查失败: {e}")
            import traceback
            self.log.error(traceback.format_exc())

    def loop(self):
        # ⭐ 启动时预热（轻量级，不阻塞）
        self.warmup()
        self.log.info("进入主循环（非阻塞模式）")
        
        # 全局孤儿订单检查计时器
        last_global_orphan_check = pd.Timestamp.utcnow()
        
        while True:
            now = pd.Timestamp.utcnow()
            
            # 强制平仓时间
            if now.hour == 14 and now.minute == 55:
                self.daily_close()
                time.sleep(60)
                continue
            
            # 异步首次打分（分批处理，避免阻塞）
            self._async_first_score_batch()
            
            # 只有完成首次打分后才进行增量扫描
            if self.first_scoring_completed:
                self.incremental_scan()
            
            # 处理重试队列
            self._process_retry_queue()
            
            # 管理订单生命周期
            self._manage_order_lifecycle()
            
            # 检查止损单状态
            self._check_stop_order_failures()
            
            # 检查网络状态
            self._check_network_status()
            
            # 定期全局孤儿订单检查（每10分钟）
            if (now - last_global_orphan_check).total_seconds() >= 600:  # 10分钟 = 600秒
                self._global_orphan_check()
                last_global_orphan_check = now
            
            # 如果没有持仓，使用末位淘汰逻辑选择top3币种
            if not self.pos:
                candidate_symbols = list(self.cand_cache.keys())[:20]  # 处理前20个候选
                
                if candidate_symbols:
                    # 先并行更新候选池数据
                    self.parallel_update(candidate_symbols, '15m', 50)
                    
                    # 使用末位淘汰选择top3
                    top3 = self.select_top3(candidate_symbols)
                    
                    # 只对top3中的币种进行开仓
                    if top3:
                        best_candidate = top3[0]  # 选择排名第一的币种
                        best_symbol = best_candidate['symbol']
                        self.log.info(f"末位淘汰选择币种: {best_symbol} (评分: {best_candidate['score']}, 深度: {best_candidate['depth']:.0f}$)")
                        self.run_symbol(best_symbol)
                    else:
                        self.log.debug("候选池中没有符合条件的币种")
                else:
                    self.log.debug("候选池为空")
            
            # 1秒间隔，快速响应
            time.sleep(1)