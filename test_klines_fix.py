#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试get_klines方法修复效果的脚本
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import json
import logging

def test_klines_fix():
    """测试get_klines方法的修复效果"""
    print("🔍 开始测试get_klines方法修复效果...")
    
    try:
        # 导入必要的模块
        from strategy.maker_channel import MakerChannelStrategy
        from binance_trader import BinanceTrader
        
        # 加载配置
        import yaml
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print('✅ 配置加载成功')
        
        # 初始化交易器（使用测试模式）
        trader = BinanceTrader(config)
        print('✅ 交易器初始化成功')
        
        # 初始化策略
        strategy = MakerChannelStrategy(trader, config)
        print('✅ 策略初始化成功')
        
        # 测试get_klines方法
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        
        for symbol in test_symbols:
            try:
                print(f"\n📊 测试获取 {symbol} 的K线数据...")
                
                # 测试不同的时间周期
                intervals = ['15m', '1h']
                for interval in intervals:
                    klines = strategy.get_klines(symbol, interval, 50)
                    
                    if klines is not None:
                        print(f"  ✅ {symbol} {interval}: 成功获取 {len(klines)} 条K线数据")
                    else:
                        print(f"  ⚠️  {symbol} {interval}: 未获取到K线数据")
                        
            except Exception as e:
                print(f"  ❌ {symbol}: 获取K线数据时发生错误: {e}")
                return False
        
        print('\n🎉 get_klines方法测试完成，未发现UnboundLocalError错误！')
        return True
        
    except Exception as e:
        print(f'❌ 测试过程中发生错误: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_variable_scope():
    """测试变量作用域问题"""
    print("\n🔍 测试变量作用域修复...")
    
    try:
        # 模拟原来的错误情况
        def test_function():
            age_days = 0.5  # 模拟新币
            
            # 这里应该先定义变量
            max_retries = 5 if age_days < 1 else 3
            retry_delay = 2.0 if age_days < 1 else 1.0
            
            # 然后使用变量
            info_msg = f"币龄 {age_days:.1f} 天，重试次数 {max_retries}，重试间隔 {retry_delay}s"
            print(f"  ✅ 变量作用域测试通过: {info_msg}")
            
            return True
        
        result = test_function()
        if result:
            print("✅ 变量作用域修复验证成功")
        
        return result
        
    except Exception as e:
        print(f"❌ 变量作用域测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试K线获取方法修复效果...\n")
    
    # 测试变量作用域
    scope_test = test_variable_scope()
    
    # 测试实际方法
    method_test = test_klines_fix()
    
    if scope_test and method_test:
        print("\n✅ 所有测试通过，修复成功！")
        exit(0)
    else:
        print("\n❌ 测试失败，请检查修复效果")
        exit(1)