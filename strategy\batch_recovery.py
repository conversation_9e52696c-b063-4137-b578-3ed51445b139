"""
批次处理恢复机制
实现异常中断后能够从上次位置继续处理
"""

import os
import json
import time
import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class BatchRecoveryState:
    """批次恢复状态"""
    cycle_id: str
    total_symbols: int
    processed_symbols: List[str]
    failed_symbols: List[str]
    current_batch_index: int
    last_checkpoint_time: float
    recovery_attempts: int = 0
    max_recovery_attempts: int = 3

class BatchRecoveryManager:
    """批次处理恢复管理器"""
    
    def __init__(self, recovery_dir: str = "recovery_data"):
        self.recovery_dir = recovery_dir
        self.log = logging.getLogger("BatchRecovery")
        
        # 确保恢复目录存在
        os.makedirs(self.recovery_dir, exist_ok=True)
        
        # 恢复配置
        self.checkpoint_interval = 300  # 5分钟保存一次检查点
        self.max_recovery_age = 86400  # 24小时内的恢复数据有效
        
    def save_checkpoint(self, cycle_id: str, total_symbols: int, 
                       processed_symbols: List[str], failed_symbols: List[str],
                       current_batch_index: int) -> bool:
        """保存检查点"""
        try:
            recovery_state = BatchRecoveryState(
                cycle_id=cycle_id,
                total_symbols=total_symbols,
                processed_symbols=processed_symbols.copy(),
                failed_symbols=failed_symbols.copy(),
                current_batch_index=current_batch_index,
                last_checkpoint_time=time.time()
            )
            
            checkpoint_file = os.path.join(self.recovery_dir, f"checkpoint_{cycle_id}.json")
            
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(recovery_state), f, indent=2, ensure_ascii=False)
            
            self.log.info(f"检查点已保存: {checkpoint_file}")
            return True
            
        except Exception as e:
            self.log.error(f"保存检查点失败: {e}")
            return False
    
    def load_checkpoint(self, cycle_id: str) -> Optional[BatchRecoveryState]:
        """加载检查点"""
        try:
            checkpoint_file = os.path.join(self.recovery_dir, f"checkpoint_{cycle_id}.json")
            
            if not os.path.exists(checkpoint_file):
                self.log.debug(f"检查点文件不存在: {checkpoint_file}")
                return None
            
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            recovery_state = BatchRecoveryState(**data)
            
            # 检查恢复数据是否过期
            age = time.time() - recovery_state.last_checkpoint_time
            if age > self.max_recovery_age:
                self.log.warning(f"恢复数据过期 ({age:.1f}秒)，删除检查点")
                self.cleanup_checkpoint(cycle_id)
                return None
            
            # 检查恢复尝试次数
            if recovery_state.recovery_attempts >= recovery_state.max_recovery_attempts:
                self.log.error(f"恢复尝试次数超限 ({recovery_state.recovery_attempts})")
                return None
            
            self.log.info(f"检查点已加载: {checkpoint_file}")
            return recovery_state
            
        except Exception as e:
            self.log.error(f"加载检查点失败: {e}")
            return None
    
    def update_recovery_attempt(self, cycle_id: str) -> bool:
        """更新恢复尝试次数"""
        try:
            recovery_state = self.load_checkpoint(cycle_id)
            if not recovery_state:
                return False
            
            recovery_state.recovery_attempts += 1
            
            checkpoint_file = os.path.join(self.recovery_dir, f"checkpoint_{cycle_id}.json")
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(recovery_state), f, indent=2, ensure_ascii=False)
            
            self.log.info(f"恢复尝试次数已更新: {recovery_state.recovery_attempts}")
            return True
            
        except Exception as e:
            self.log.error(f"更新恢复尝试次数失败: {e}")
            return False
    
    def cleanup_checkpoint(self, cycle_id: str) -> bool:
        """清理检查点"""
        try:
            checkpoint_file = os.path.join(self.recovery_dir, f"checkpoint_{cycle_id}.json")
            
            if os.path.exists(checkpoint_file):
                os.remove(checkpoint_file)
                self.log.info(f"检查点已清理: {checkpoint_file}")
            
            return True
            
        except Exception as e:
            self.log.error(f"清理检查点失败: {e}")
            return False
    
    def cleanup_old_checkpoints(self) -> int:
        """清理过期的检查点"""
        cleaned_count = 0
        
        try:
            current_time = time.time()
            
            for filename in os.listdir(self.recovery_dir):
                if filename.startswith("checkpoint_") and filename.endswith(".json"):
                    filepath = os.path.join(self.recovery_dir, filename)
                    
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        last_checkpoint_time = data.get('last_checkpoint_time', 0)
                        age = current_time - last_checkpoint_time
                        
                        if age > self.max_recovery_age:
                            os.remove(filepath)
                            cleaned_count += 1
                            self.log.info(f"清理过期检查点: {filename}")
                    
                    except Exception as e:
                        self.log.warning(f"处理检查点文件 {filename} 失败: {e}")
            
            if cleaned_count > 0:
                self.log.info(f"清理完成，共清理 {cleaned_count} 个过期检查点")
            
        except Exception as e:
            self.log.error(f"清理过期检查点失败: {e}")
        
        return cleaned_count
    
    def get_recovery_status(self) -> Dict:
        """获取恢复状态"""
        try:
            checkpoints = []
            
            for filename in os.listdir(self.recovery_dir):
                if filename.startswith("checkpoint_") and filename.endswith(".json"):
                    filepath = os.path.join(self.recovery_dir, filename)
                    
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        checkpoints.append({
                            'cycle_id': data.get('cycle_id'),
                            'total_symbols': data.get('total_symbols'),
                            'processed_count': len(data.get('processed_symbols', [])),
                            'failed_count': len(data.get('failed_symbols', [])),
                            'current_batch_index': data.get('current_batch_index'),
                            'last_checkpoint_time': data.get('last_checkpoint_time'),
                            'recovery_attempts': data.get('recovery_attempts', 0),
                            'age_hours': (time.time() - data.get('last_checkpoint_time', 0)) / 3600
                        })
                    
                    except Exception as e:
                        self.log.warning(f"读取检查点 {filename} 失败: {e}")
            
            return {
                'total_checkpoints': len(checkpoints),
                'checkpoints': checkpoints,
                'recovery_dir': self.recovery_dir,
                'max_recovery_age_hours': self.max_recovery_age / 3600
            }
            
        except Exception as e:
            self.log.error(f"获取恢复状态失败: {e}")
            return {'error': str(e)}
    
    def can_recover(self, cycle_id: str) -> bool:
        """检查是否可以恢复"""
        recovery_state = self.load_checkpoint(cycle_id)
        return recovery_state is not None
    
    def get_resume_position(self, cycle_id: str) -> Optional[Dict]:
        """获取恢复位置信息"""
        recovery_state = self.load_checkpoint(cycle_id)
        
        if not recovery_state:
            return None
        
        return {
            'cycle_id': recovery_state.cycle_id,
            'resume_batch_index': recovery_state.current_batch_index,
            'processed_symbols': recovery_state.processed_symbols,
            'failed_symbols': recovery_state.failed_symbols,
            'remaining_symbols': recovery_state.total_symbols - len(recovery_state.processed_symbols) - len(recovery_state.failed_symbols),
            'progress_percentage': (len(recovery_state.processed_symbols) + len(recovery_state.failed_symbols)) / recovery_state.total_symbols * 100
        }

# 集成到批次监控器的恢复功能
class BatchMonitorWithRecovery:
    """带恢复功能的批次监控器"""
    
    def __init__(self, batch_monitor, recovery_manager: BatchRecoveryManager):
        self.batch_monitor = batch_monitor
        self.recovery_manager = recovery_manager
        self.log = logging.getLogger("BatchMonitorWithRecovery")
        
        # 自动检查点保存
        self.last_checkpoint_time = 0
        self.checkpoint_interval = 300  # 5分钟
    
    def start_scan_cycle_with_recovery(self, symbols: List[str], cycle_id: str = None) -> str:
        """启动带恢复功能的扫描周期"""
        
        # 检查是否有可恢复的周期
        if cycle_id and self.recovery_manager.can_recover(cycle_id):
            resume_info = self.recovery_manager.get_resume_position(cycle_id)
            
            if resume_info:
                self.log.info(f"检测到可恢复的扫描周期: {cycle_id}")
                self.log.info(f"恢复位置: 批次 {resume_info['resume_batch_index']}, "
                             f"进度 {resume_info['progress_percentage']:.1f}%")
                
                # 更新恢复尝试次数
                self.recovery_manager.update_recovery_attempt(cycle_id)
                
                # 过滤已处理的币种
                processed_set = set(resume_info['processed_symbols'] + resume_info['failed_symbols'])
                remaining_symbols = [s for s in symbols if s not in processed_set]
                
                # 启动恢复的扫描周期
                actual_cycle_id = self.batch_monitor.start_scan_cycle(remaining_symbols)
                
                # 保存恢复检查点
                self._save_checkpoint_if_needed(actual_cycle_id, symbols, 
                                              resume_info['processed_symbols'],
                                              resume_info['failed_symbols'],
                                              resume_info['resume_batch_index'])
                
                return actual_cycle_id
        
        # 正常启动新的扫描周期
        actual_cycle_id = self.batch_monitor.start_scan_cycle(symbols)
        
        # 保存初始检查点
        self._save_checkpoint_if_needed(actual_cycle_id, symbols, [], [], 0)
        
        return actual_cycle_id
    
    def complete_batch_processing_with_checkpoint(self, batch_id: str, 
                                                processed_symbols: List[str],
                                                failed_symbols: List[str]) -> bool:
        """完成批次处理并保存检查点"""
        
        # 正常完成批次处理
        result = self.batch_monitor.complete_batch_processing(batch_id, processed_symbols, failed_symbols)
        
        if result:
            # 获取当前状态并保存检查点
            progress = self.batch_monitor.get_progress_status()
            
            if progress and progress.get('cycle_id'):
                cycle_id = progress['cycle_id']
                
                # 收集所有已处理和失败的币种
                all_processed = []
                all_failed = []
                current_batch_index = 0
                
                if hasattr(self.batch_monitor, 'current_cycle') and self.batch_monitor.current_cycle:
                    for batch in self.batch_monitor.current_cycle.batches:
                        all_processed.extend(batch.processed_symbols)
                        all_failed.extend(batch.failed_symbols)
                        if batch.status == "processing":
                            current_batch_index = batch.batch_index
                
                # 保存检查点
                self._save_checkpoint_if_needed(cycle_id, 
                                              all_processed + all_failed + processed_symbols + failed_symbols,
                                              all_processed + processed_symbols,
                                              all_failed + failed_symbols,
                                              current_batch_index)
        
        return result
    
    def _save_checkpoint_if_needed(self, cycle_id: str, total_symbols: List[str],
                                  processed_symbols: List[str], failed_symbols: List[str],
                                  current_batch_index: int):
        """根据需要保存检查点"""
        current_time = time.time()
        
        if current_time - self.last_checkpoint_time >= self.checkpoint_interval:
            self.recovery_manager.save_checkpoint(
                cycle_id=cycle_id,
                total_symbols=len(total_symbols),
                processed_symbols=processed_symbols,
                failed_symbols=failed_symbols,
                current_batch_index=current_batch_index
            )
            self.last_checkpoint_time = current_time
    
    def cleanup_completed_cycle(self, cycle_id: str):
        """清理已完成周期的恢复数据"""
        self.recovery_manager.cleanup_checkpoint(cycle_id)
        self.log.info(f"已清理周期 {cycle_id} 的恢复数据")