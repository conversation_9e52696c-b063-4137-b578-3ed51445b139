# 简化版MakerChannel动态通道突破策略

## 策略概述

这是一个简化版的MakerChannel动态通道突破策略，具有以下核心特征：

- 通道突破型趋势跟踪策略
- 全币种扫描，评分筛选机制
- 只做Maker的限价单模式
- 浮盈加仓，最多3-4次
- 每日强制平仓机制
- 负费率复利设计

## 技术特点

- Python 3.8+ 实现
- 使用ccxt库连接交易所
- 异步IO设计
- 完整的日志系统
- 错误处理和重试机制
- 配置文件驱动

## 目录结构

```
simple_strategy/
├── config/
│   └── config.json          # 策略配置文件
├── data/                    # 数据缓存目录
├── logs/                    # 日志目录
├── modules/                 # 策略模块
│   ├── cache_manager.py     # 缓存管理模块
│   ├── exchange_connector.py # 交易所连接模块
│   ├── symbol_scorer.py     # 交易对评分模块
│   ├── channel_breakout_detector.py # 通道突破检测模块
│   ├── position_manager.py  # 仓位管理模块
│   ├── risk_manager.py      # 风险管理模块
│   └── performance_monitor.py # 性能监控模块
├── strategy.py              # 策略主程序
├── requirements.txt         # 依赖包列表
└── README.md                # 说明文档
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置策略

编辑 `config/config.json` 文件：

```json
{
  "exchange": {
    "name": "binance",
    "api_key": "YOUR_API_KEY",
    "api_secret": "YOUR_API_SECRET",
    "testnet": true,
    "rate_limit": 10
  },
  "strategy": {
    "name": "maker_channel",
    "symbols": ["BTC/USDT", "ETH/USDT", "BNB/USDT"],
    "timeframe": "15m",
    "channel_period": 20,
    "breakout_threshold": 1.002,
    "position_size_usd": 100,
    "max_positions": 3,
    "max_additions": 3,
    "stop_loss_pct": 0.03,
    "take_profit_pct": 0.06,
    "trailing_stop_pct": 0.01,
    "daily_close_time": "00:00"
  },
  "risk_management": {
    "max_account_risk": 0.1,
    "max_correlation": 0.7,
    "enable_kill_switch": true
  },
  "logging": {
    "level": "INFO",
    "file": "logs/strategy.log",
    "max_size": "10MB",
    "backup_count": 5
  }
}
```

## 运行策略

```bash
python strategy.py
```

## 模块说明

### 1. 数据获取和缓存管理 (cache_manager.py)

负责管理所有市场数据和策略状态的本地缓存，减少API调用频率，提高系统性能。

### 2. 币种评分和筛选系统 (symbol_scorer.py)

基于多因子对交易对进行评分和筛选：
- 价格变化 (30%)
- 成交量 (25%)
- 波动率 (20%)
- 动量 (15%)
- 深度 (10%)

### 3. 通道突破信号识别 (channel_breakout_detector.py)

检测价格是否突破通道上轨，确认趋势启动信号。

### 4. 订单管理和仓位控制 (position_manager.py)

管理订单执行和仓位控制，包括：
- 限价单入场
- 浮盈加仓
- 止损止盈管理
- 每日强制平仓

### 5. 风险监控和止损逻辑 (risk_manager.py)

实施多层次风险控制：
- 账户风险控制
- 相关性风险控制
- 紧急停止开关
- 订单验证

### 6. 性能监控和指标收集 (performance_monitor.py)

收集和记录策略运行性能指标：
- 订单执行统计
- 盈亏统计
- 最大回撤
- API调用统计

## 策略逻辑

1. **初始化阶段**
   - 连接交易所
   - 加载交易对信息
   - 初始化各模块

2. **主循环**
   - 定期扫描市场
   - 评分筛选候选交易对
   - 检测通道突破信号
   - 执行交易逻辑

3. **入场逻辑**
   - 等待价格回调到0.38%位置
   - 创建限价单入场
   - 设置止损和止盈

4. **持仓管理**
   - 移动止损保护利润
   - 浮盈加仓增加收益
   - 每日强制平仓

5. **风险控制**
   - 多层次风险检查
   - 紧急停止机制
   - 订单验证

## 注意事项

1. **API密钥安全**
   - 不要在代码中硬编码API密钥
   - 使用环境变量或配置文件管理密钥

2. **测试环境**
   - 建议先在测试网环境验证策略
   - 确保充分测试后再在实盘运行

3. **风险管理**
   - 合理设置仓位大小
   - 严格控制账户风险
   - 定期监控策略表现

4. **性能监控**
   - 关注日志信息
   - 定期查看性能指标
   - 及时调整策略参数

## 免责声明

本策略仅供学习和研究使用，不构成投资建议。数字货币交易存在高风险，可能导致重大损失。在使用本策略前，请确保您充分理解相关风险，并根据自身情况谨慎决策。