#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单递归测试 - 专门验证递归问题是否解决
"""

import sys
import json
import traceback

def test_simple_recursion():
    """简单递归测试"""
    print("开始简单递归测试...")
    
    try:
        # 1. 测试基础导入
        print("1. 测试基础导入...")
        from binance_trader import BinanceTrader
        print("   ✅ BinanceTrader导入成功")
        
        # 2. 测试配置加载
        print("2. 测试配置加载...")
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("   ✅ 配置加载成功")
        
        # 3. 测试BinanceTrader初始化
        print("3. 测试BinanceTrader初始化...")
        trader = BinanceTrader(config)
        print("   ✅ BinanceTrader初始化成功")
        
        # 4. 测试K线数据获取（最容易触发递归的操作）
        print("4. 测试K线数据获取...")
        try:
            klines = trader.get_klines('BTCUSDT', '15m', 5)
            if klines is not None and len(klines) > 0:
                print(f"   ✅ 成功获取 {len(klines)} 条K线数据")
            else:
                print("   ⚠️  K线数据为空（可能是网络或API问题，但没有递归错误）")
        except RecursionError:
            print("   ❌ 递归错误仍然存在！")
            raise
        except Exception as e:
            print(f"   ⚠️  其他错误（非递归）: {e}")
        
        print("\n🎉 递归测试通过！没有发生递归错误！")
        return True
        
    except RecursionError as e:
        print(f"\n❌ 递归错误仍然存在: {e}")
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"\n⚠️  其他错误（非递归）: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_recursion()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)