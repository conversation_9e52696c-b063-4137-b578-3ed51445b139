#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的策略K线数据获取
"""

import sys
import json
import time
import logging

def test_strategy():
    """测试策略"""
    print("开始测试修复后的策略...")
    
    try:
        # 导入模块
        from binance_trader import BinanceTrader
        from strategy.maker_channel import MakerChannelStrategy
        
        # 加载配置
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 添加策略需要的配置
        config['first_nominal'] = 100
        config['order_ttl'] = 300
        config['network_check_interval'] = 60
        config['max_symbol_failures'] = 5
        config['failure_cooldown'] = 3600
        config['network_error_cooldown'] = 300
        
        # 初始化
        trader = BinanceTrader(config)
        print("BinanceTrader初始化成功")
        
        # 设置日志级别
        logging.basicConfig(level=logging.INFO)
        
        strategy = MakerChannelStrategy(trader, config)
        print("MakerChannelStrategy初始化成功")
        
        # 测试之前有问题的币种
        problem_symbols = ['ALPINEUSDT', 'BCHUSDT', 'EGLDUSDT', 'ARUSDT']
        
        success = 0
        total = len(problem_symbols)
        
        for symbol in problem_symbols:
            try:
                print(f"\n测试 {symbol}...")
                start_time = time.time()
                
                klines_df = strategy.get_klines(symbol, '15m', 20)
                
                end_time = time.time()
                
                if klines_df is not None and len(klines_df) > 0:
                    print(f"  ✅ 成功: {len(klines_df)}条数据, 耗时{end_time-start_time:.2f}s")
                    success += 1
                else:
                    print(f"  ❌ 失败: 返回空数据")
                    
            except Exception as e:
                print(f"  ❌ 异常: {e}")
            
            time.sleep(1)  # 避免请求过快
        
        print(f"\n📊 测试结果: {success}/{total} 成功")
        success_rate = (success / total) * 100
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate >= 75:
            print("✅ 修复效果良好！")
        else:
            print("⚠️ 仍需进一步优化")
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_strategy()