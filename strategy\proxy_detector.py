"""
服务器代理检测逻辑
确保阿里云服务器严格禁用代理功能
"""

import os
import sys
import json
import logging
import subprocess
import urllib.request
import urllib.error
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import platform

@dataclass
class ProxyDetectionResult:
    """代理检测结果"""
    has_proxy: bool
    proxy_type: str = ""
    proxy_address: str = ""
    detection_method: str = ""
    error_message: str = ""

class ProxyDetector:
    """代理检测器"""
    
    def __init__(self):
        self.log = logging.getLogger("ProxyDetector")
        self.system = platform.system().lower()
        
    def detect_all_proxies(self) -> Dict[str, ProxyDetectionResult]:
        """检测所有类型的代理设置"""
        results = {}
        
        # 检测系统代理
        results['system_proxy'] = self._detect_system_proxy()
        
        # 检测环境变量代理
        results['env_proxy'] = self._detect_environment_proxy()
        
        # 检测应用程序代理
        results['app_proxy'] = self._detect_application_proxy()
        
        # 检测网络连接
        results['network_test'] = self._test_direct_connection()
        
        return results
    
    def _detect_system_proxy(self) -> ProxyDetectionResult:
        """检测系统级代理设置"""
        try:
            if self.system == "windows":
                return self._detect_windows_proxy()
            elif self.system == "linux":
                return self._detect_linux_proxy()
            else:
                return ProxyDetectionResult(
                    has_proxy=False,
                    error_message=f"不支持的操作系统: {self.system}"
                )
        except Exception as e:
            return ProxyDetectionResult(
                has_proxy=False,
                error_message=f"系统代理检测失败: {e}"
            )
    
    def _detect_windows_proxy(self) -> ProxyDetectionResult:
        """检测Windows系统代理"""
        try:
            # 检查WinHTTP代理设置
            result = subprocess.run(
                ["netsh", "winhttp", "show", "proxy"],
                capture_output=True,
                text=True,
                encoding='gbk'
            )
            
            if result.returncode == 0:
                output = result.stdout.lower()
                
                if "直接访问" in output or "direct access" in output:
                    return ProxyDetectionResult(
                        has_proxy=False,
                        detection_method="netsh winhttp"
                    )
                elif "代理服务器" in output or "proxy server" in output:
                    # 提取代理地址
                    lines = result.stdout.split('\n')
                    proxy_address = ""
                    for line in lines:
                        if "代理服务器" in line or "proxy server" in line:
                            proxy_address = line.split(':')[-1].strip()
                            break
                    
                    return ProxyDetectionResult(
                        has_proxy=True,
                        proxy_type="HTTP",
                        proxy_address=proxy_address,
                        detection_method="netsh winhttp"
                    )
            
            # 检查Internet Explorer代理设置
            try:
                import winreg
                
                key = winreg.OpenKey(
                    winreg.HKEY_CURRENT_USER,
                    r"Software\Microsoft\Windows\CurrentVersion\Internet Settings"
                )
                
                proxy_enable = winreg.QueryValueEx(key, "ProxyEnable")[0]
                
                if proxy_enable:
                    proxy_server = winreg.QueryValueEx(key, "ProxyServer")[0]
                    winreg.CloseKey(key)
                    
                    return ProxyDetectionResult(
                        has_proxy=True,
                        proxy_type="HTTP",
                        proxy_address=proxy_server,
                        detection_method="IE Registry"
                    )
                
                winreg.CloseKey(key)
                
            except Exception as e:
                self.log.debug(f"IE代理检测失败: {e}")
            
            return ProxyDetectionResult(
                has_proxy=False,
                detection_method="Windows System"
            )
            
        except Exception as e:
            return ProxyDetectionResult(
                has_proxy=False,
                error_message=f"Windows代理检测失败: {e}"
            )
    
    def _detect_linux_proxy(self) -> ProxyDetectionResult:
        """检测Linux系统代理"""
        try:
            # 检查系统代理配置文件
            proxy_files = [
                "/etc/environment",
                "/etc/profile",
                "~/.bashrc",
                "~/.profile"
            ]
            
            for file_path in proxy_files:
                expanded_path = os.path.expanduser(file_path)
                if os.path.exists(expanded_path):
                    with open(expanded_path, 'r') as f:
                        content = f.read().lower()
                        
                        if any(proxy in content for proxy in ['http_proxy', 'https_proxy', 'ftp_proxy']):
                            return ProxyDetectionResult(
                                has_proxy=True,
                                proxy_type="System Config",
                                detection_method=f"Config file: {file_path}"
                            )
            
            return ProxyDetectionResult(
                has_proxy=False,
                detection_method="Linux System Config"
            )
            
        except Exception as e:
            return ProxyDetectionResult(
                has_proxy=False,
                error_message=f"Linux代理检测失败: {e}"
            )
    
    def _detect_environment_proxy(self) -> ProxyDetectionResult:
        """检测环境变量代理"""
        try:
            proxy_vars = [
                'HTTP_PROXY', 'http_proxy',
                'HTTPS_PROXY', 'https_proxy',
                'FTP_PROXY', 'ftp_proxy',
                'ALL_PROXY', 'all_proxy'
            ]
            
            found_proxies = []
            
            for var in proxy_vars:
                value = os.environ.get(var)
                if value:
                    found_proxies.append(f"{var}={value}")
            
            if found_proxies:
                return ProxyDetectionResult(
                    has_proxy=True,
                    proxy_type="Environment Variable",
                    proxy_address="; ".join(found_proxies),
                    detection_method="Environment Variables"
                )
            
            return ProxyDetectionResult(
                has_proxy=False,
                detection_method="Environment Variables"
            )
            
        except Exception as e:
            return ProxyDetectionResult(
                has_proxy=False,
                error_message=f"环境变量代理检测失败: {e}"
            )
    
    def _detect_application_proxy(self) -> ProxyDetectionResult:
        """检测应用程序级代理"""
        try:
            # 检查Python urllib代理设置
            proxy_handler = urllib.request.getproxies()
            
            if proxy_handler:
                proxy_info = []
                for protocol, proxy_url in proxy_handler.items():
                    proxy_info.append(f"{protocol}={proxy_url}")
                
                return ProxyDetectionResult(
                    has_proxy=True,
                    proxy_type="Application Level",
                    proxy_address="; ".join(proxy_info),
                    detection_method="urllib.request.getproxies()"
                )
            
            return ProxyDetectionResult(
                has_proxy=False,
                detection_method="Application Level"
            )
            
        except Exception as e:
            return ProxyDetectionResult(
                has_proxy=False,
                error_message=f"应用程序代理检测失败: {e}"
            )
    
    def _test_direct_connection(self) -> ProxyDetectionResult:
        """测试直连网络连接"""
        try:
            # 测试多个目标地址
            test_urls = [
                "https://api.binance.com/api/v3/ping",
                "https://www.google.com",
                "https://httpbin.org/ip"
            ]
            
            connection_results = []
            
            for url in test_urls:
                try:
                    # 创建不使用代理的请求
                    proxy_handler = urllib.request.ProxyHandler({})
                    opener = urllib.request.build_opener(proxy_handler)
                    
                    request = urllib.request.Request(url)
                    request.add_header('User-Agent', 'ProxyDetector/1.0')
                    
                    response = opener.open(request, timeout=10)
                    
                    if response.getcode() == 200:
                        connection_results.append(f"{url}: 直连成功")
                    else:
                        connection_results.append(f"{url}: 响应码 {response.getcode()}")
                
                except urllib.error.URLError as e:
                    connection_results.append(f"{url}: 连接失败 - {e}")
                except Exception as e:
                    connection_results.append(f"{url}: 错误 - {e}")
            
            # 如果至少有一个连接成功，认为是直连
            success_count = sum(1 for result in connection_results if "直连成功" in result)
            
            return ProxyDetectionResult(
                has_proxy=success_count == 0,  # 如果没有成功连接，可能有代理阻挡
                proxy_type="Network Test",
                proxy_address=f"成功连接: {success_count}/{len(test_urls)}",
                detection_method="Direct Connection Test"
            )
            
        except Exception as e:
            return ProxyDetectionResult(
                has_proxy=False,
                error_message=f"网络连接测试失败: {e}"
            )
    
    def generate_report(self, results: Dict[str, ProxyDetectionResult]) -> str:
        """生成检测报告"""
        report_lines = [
            "=" * 60,
            "服务器代理检测报告",
            "=" * 60,
            f"检测时间: {self._get_current_time()}",
            f"操作系统: {platform.system()} {platform.release()}",
            f"Python版本: {sys.version}",
            ""
        ]
        
        # 总体状态
        has_any_proxy = any(result.has_proxy for result in results.values())
        
        if has_any_proxy:
            report_lines.extend([
                "⚠️  警告: 检测到代理设置!",
                "建议立即禁用所有代理以确保直连模式",
                ""
            ])
        else:
            report_lines.extend([
                "✅ 良好: 未检测到代理设置",
                "服务器处于直连模式",
                ""
            ])
        
        # 详细检测结果
        report_lines.append("详细检测结果:")
        report_lines.append("-" * 40)
        
        for detection_type, result in results.items():
            report_lines.append(f"\n{detection_type.upper()}:")
            
            if result.has_proxy:
                report_lines.append(f"  状态: ❌ 检测到代理")
                report_lines.append(f"  类型: {result.proxy_type}")
                report_lines.append(f"  地址: {result.proxy_address}")
            else:
                report_lines.append(f"  状态: ✅ 无代理")
            
            report_lines.append(f"  检测方法: {result.detection_method}")
            
            if result.error_message:
                report_lines.append(f"  错误信息: {result.error_message}")
        
        # 建议措施
        report_lines.extend([
            "",
            "建议措施:",
            "-" * 40
        ])
        
        if has_any_proxy:
            report_lines.extend([
                "1. 立即禁用所有代理设置",
                "2. 清除环境变量中的代理配置",
                "3. 重启应用程序以确保设置生效",
                "4. 重新运行检测确认代理已禁用"
            ])
        else:
            report_lines.extend([
                "1. 保持当前直连配置",
                "2. 定期运行代理检测确保状态",
                "3. 监控网络连接质量"
            ])
        
        return "\n".join(report_lines)
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def save_report(self, results: Dict[str, ProxyDetectionResult], 
                   filename: str = "proxy_detection_report.txt") -> bool:
        """保存检测报告到文件"""
        try:
            report = self.generate_report(results)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            
            self.log.info(f"代理检测报告已保存: {filename}")
            return True
            
        except Exception as e:
            self.log.error(f"保存报告失败: {e}")
            return False

def main():
    """主函数 - 运行代理检测"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    detector = ProxyDetector()
    
    print("开始代理检测...")
    results = detector.detect_all_proxies()
    
    # 生成并显示报告
    report = detector.generate_report(results)
    print(report)
    
    # 保存报告
    detector.save_report(results)
    
    # 检查是否有代理
    has_proxy = any(result.has_proxy for result in results.values())
    
    if has_proxy:
        print("\n⚠️  发现代理设置，请立即处理!")
        return 1
    else:
        print("\n✅ 代理检测通过，服务器处于直连模式")
        return 0

if __name__ == "__main__":
    sys.exit(main())