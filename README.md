# 批次处理系统 (Batch Processing System)

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/status-stable-brightgreen.svg)](https://github.com)

一个高效、可扩展的批次处理系统，专为大规模数据处理和监控而设计。支持实时监控、可视化报告和灵活配置。

## ✨ 特性

- 🚀 **高性能批次处理**: 支持大规模数据的分批处理
- 📊 **实时监控**: 控制台和HTML双重监控界面
- ⚙️ **灵活配置**: 多种配置模板，支持自定义参数
- 📈 **可视化报告**: 自动生成HTML进度报告
- 🔧 **易于扩展**: 模块化设计，支持自定义处理逻辑
- 🛡️ **稳定可靠**: 完善的错误处理和恢复机制

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行演示
```bash
python batch_system_demo.py
```

### 基本使用
```python
from strategy.batch_integration import BatchProcessingSystem

# 创建系统实例
system = BatchProcessingSystem()

# 应用配置模板
system.apply_config_template("stable_scan")

# 启动批次扫描
symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
cycle_id = system.start_batch_scan(symbols)

# 处理批次...
# (详细使用方法请参考使用说明)

# 停止系统
system.stop_system()
```

## 📁 项目结构

```
allmace/
├── strategy/
│   ├── batch_config.py          # 配置管理模块
│   ├── batch_monitor.py         # 监控核心模块
│   ├── batch_visualizer.py      # 可视化模块
│   └── batch_integration.py     # 系统集成模块
├── batch_system_demo.py         # 演示脚本
├── test_batch_system.py         # 测试脚本
├── batch_config.json            # 配置文件
├── batch_progress.html          # HTML报告
├── 批次处理系统优化完成报告.md    # 完成报告
├── 批次处理系统使用说明.md       # 详细使用说明
└── README.md                    # 项目说明
```

## 🎯 核心组件

### BatchConfig
配置管理模块，提供灵活的参数配置和模板管理。

### BatchMonitor  
监控核心模块，负责批次状态跟踪和进度管理。

### BatchVisualizer
可视化模块，提供控制台显示和HTML报告生成。

### BatchProcessingSystem
系统集成模块，统一管理各个组件的协调工作。

## 📊 监控界面

### 控制台监控
实时显示处理进度、统计信息和系统状态。

### HTML报告
自动生成的可视化报告，支持实时刷新和历史记录。

## ⚙️ 配置模板

- **stable_scan**: 稳定扫描模式（推荐生产环境）
- **fast_scan**: 快速扫描模式（高频场景）
- **debug_mode**: 调试模式（开发测试）

## 📚 文档

- [详细使用说明](批次处理系统使用说明.md) - 完整的使用指南和API参考
- [优化完成报告](批次处理系统优化完成报告.md) - 系统架构和优化详情
- [演示脚本](batch_system_demo.py) - 完整的使用示例

## 🧪 测试

```bash
# 运行完整测试
python test_batch_system.py

# 运行演示脚本
python batch_system_demo.py
```

## 📈 性能指标

- **处理能力**: 支持数千个币种的批次处理
- **响应时间**: 平均批次处理时间 < 30秒
- **成功率**: 正常情况下成功率 > 95%
- **资源占用**: 内存使用 < 512MB

## 🛠️ 故障排除

### 常见问题
1. **系统启动失败**: 检查配置文件和依赖包
2. **监控显示异常**: 重置显示组件
3. **批次处理超时**: 调整超时配置
4. **HTML报告生成失败**: 检查文件权限

详细的故障排除指南请参考[使用说明](批次处理系统使用说明.md#故障排除)。

## 🔄 版本历史

### v2.0.0 (当前版本)
- ✅ 完整的批次处理系统
- ✅ 实时监控和可视化
- ✅ 灵活的配置管理
- ✅ HTML报告生成
- ✅ 完善的错误处理

### v1.0.0
- 基础批次处理功能
- 简单的监控界面

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

### 开发指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有任何问题或建议，请通过以下方式联系：

- 📧 邮箱: <EMAIL>
- 💬 讨论: GitHub Discussions
- 🐛 Bug报告: GitHub Issues

---

**开发团队**: 批次处理系统开发组  
**最后更新**: 2025-09-26  
**文档版本**: v2.0.0

> 感谢使用批次处理系统！如果这个项目对您有帮助，请给我们一个 ⭐️