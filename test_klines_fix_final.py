#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线数据获取修复最终验证脚本
验证所有修复是否生效
"""

import sys
import json
import time
import logging
from datetime import datetime

def test_klines_fix():
    """测试K线数据获取修复"""
    print("开始测试K线数据获取修复...")
    
    try:
        # 1. 导入必要模块
        print("1. 导入模块...")
        from binance_trader import BinanceTrader
        from strategy.maker_channel import MakerChannelStrategy
        
        # 2. 加载配置
        print("2. 加载配置...")
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 添加策略需要的配置参数
        config['first_nominal'] = 100
        config['order_ttl'] = 300
        config['network_check_interval'] = 60
        config['max_symbol_failures'] = 5
        config['failure_cooldown'] = 3600
        config['network_error_cooldown'] = 300
        
        # 3. 初始化交易器和策略
        print("3. 初始化BinanceTrader和策略...")
        trader = BinanceTrader(config)
        
        # 设置适当的日志级别
        logging.getLogger('MakerChannel').setLevel(logging.INFO)
        
        try:
            strategy = MakerChannelStrategy(trader, config)
            print("   ✅ 策略初始化成功")
        except Exception as e:
            print(f"   ❌ 策略初始化失败: {e}")
            return
        
        # 4. 测试之前有问题的币种
        print("\n4. 测试之前有问题的币种...")
        problem_symbols = ['ALPINEUSDT', 'BCHUSDT', 'EGLDUSDT', 'ARUSDT', 'MYXUSDT']
        
        success_count = 0
        fail_count = 0
        
        for symbol in problem_symbols:
            print(f"\n--- 测试 {symbol} ---")
            try:
                start_time = time.time()
                
                # 测试策略的get_klines方法
                klines_df = strategy.get_klines(symbol, '15m', 20)
                
                end_time = time.time()
                
                print(f"   响应时间: {end_time - start_time:.2f}秒")
                print(f"   响应类型: {type(klines_df)}")
                
                if klines_df is not None:
                    if hasattr(klines_df, '__len__'):
                        print(f"   ✅ 成功获取数据，长度: {len(klines_df)}")
                        if hasattr(klines_df, 'columns'):
                            print(f"   DataFrame列: {list(klines_df.columns)}")
                            print(f"   数据范围: {klines_df.index[0]} 到 {klines_df.index[-1]}")
                        success_count += 1
                    else:
                        print(f"   ✅ 成功获取数据: {klines_df}")
                        success_count += 1
                else:
                    print(f"   ❌ 获取失败，返回None")
                    fail_count += 1
                    
            except Exception as e:
                print(f"   ❌ 获取异常: {e}")
                fail_count += 1
                import traceback
                traceback.print_exc()
            
            time.sleep(0.5)  # 避免请求过快
        
        # 5. 测试正常币种作为对比
        print(f"\n5. 测试正常币种作为对比...")
        normal_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        
        for symbol in normal_symbols:
            try:
                start_time = time.time()
                klines_df = strategy.get_klines(symbol, '15m', 20)
                end_time = time.time()
                
                status = "✅" if klines_df is not None else "❌"
                length = len(klines_df) if klines_df is not None and hasattr(klines_df, '__len__') else 0
                print(f"   {status} {symbol}: 长度={length}, 耗时={end_time-start_time:.2f}s")
                
            except Exception as e:
                print(f"   ❌ {symbol}: 异常 - {e}")
        
        # 6. 测试网络状态检查
        print(f"\n6. 测试网络状态检查...")
        try:
            network_status = strategy._check_network_status()
            print(f"   网络状态: {network_status}")
            
            # 即使网络检查失败，也应该能获取数据
            if not network_status.get('connected', False):
                print("   网络检查显示异常，但应该仍能获取数据...")
                test_symbol = 'BTCUSDT'
                klines_df = strategy.get_klines(test_symbol, '15m', 10)
                if klines_df is not None:
                    print(f"   ✅ 网络检查异常时仍能获取数据: {len(klines_df)}条")
                else:
                    print(f"   ❌ 网络检查异常时无法获取数据")
            
        except Exception as e:
            print(f"   网络状态检查异常: {e}")
        
        # 7. 总结测试结果
        print(f"\n🔍 测试结果总结:")
        print(f"   问题币种测试: 成功{success_count}个, 失败{fail_count}个")
        total_symbols = len(problem_symbols)
        success_rate = (success_count / total_symbols) * 100 if total_symbols > 0 else 0
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print(f"   ✅ 修复效果良好！")
        elif success_rate >= 50:
            print(f"   ⚠️ 修复有一定效果，但仍需优化")
        else:
            print(f"   ❌ 修复效果不佳，需要进一步调查")
        
        print("\n🔍 K线数据获取修复验证完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_klines_fix()