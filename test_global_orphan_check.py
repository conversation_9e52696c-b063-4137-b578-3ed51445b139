#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml
import time
from strategy.maker_channel import MakerChannelStrategy
from binance_trader import BinanceTrader

def test_global_orphan_check():
    """测试全局孤儿订单检查功能"""
    try:
        # 读取配置
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 初始化交易器
        trader = BinanceTrader(config)
        
        # 初始化策略
        strategy = MakerChannelStrategy(trader, config)
        
        print("=== 测试全局孤儿订单检查功能 ===")
        
        # 直接调用全局孤儿订单检查方法
        print("正在执行全局孤儿订单检查...")
        strategy._global_orphan_check()
        
        print("\n=== 测试完成 ===")
        
        # 显示当前活跃持仓
        print("\n=== 当前活跃持仓 ===")
        all_positions = trader.get_positions()
        active_positions = [p for p in all_positions if abs(float(p.get('positionAmt', 0))) > 0.001]
        
        if active_positions:
            print(f"当前有 {len(active_positions)} 个活跃持仓:")
            for pos in active_positions:
                symbol = pos.get('symbol')
                qty = pos.get('positionAmt')
                unrealized_pnl = pos.get('unRealizedProfit', '0')
                print(f"  {symbol}: {qty} (未实现盈亏: {unrealized_pnl})")
        else:
            print("当前无活跃持仓")
            
    except Exception as e:
        print(f"测试过程发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_global_orphan_check()