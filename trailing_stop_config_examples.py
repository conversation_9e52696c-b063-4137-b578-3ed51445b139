#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动止损配置示例
提供不同交易场景下的移动止损配置模板
"""

from typing import Dict, Any


class TrailingStopConfigTemplates:
    """移动止损配置模板类"""
    
    @staticmethod
    def get_conservative_config() -> Dict[str, Any]:
        """保守型配置 - 适用于风险厌恶型交易者"""
        return {
            "enabled": True,
            "trigger_profit_pct": 0.03,        # 3% 浮盈才激活
            "confirmation_bars": 5,             # 需要5个周期确认
            
            "normal_mode": {
                "profit_steps": [0.04, 0.07, 0.12, 0.20],
                "stop_ratios": [0.6, 0.7, 0.75, 0.8],   # 较宽松的止损
                "min_move_pct": 0.008,          # 0.8% 最小移动
                "max_move_pct": 0.015           # 1.5% 最大移动
            },
            
            "extreme_mode": {
                "profit_steps": [0.06, 0.10, 0.16, 0.25],
                "stop_ratios": [0.5, 0.6, 0.65, 0.7],
                "min_move_pct": 0.01,
                "max_move_pct": 0.02
            },
            
            "risk_control": {
                "max_drawdown_pct": 0.12,      # 12% 最大回撤
                "volatility_threshold": 0.04,
                "volatility_window": 25,
                "mode_switch_threshold": 0.06
            }
        }
    
    @staticmethod
    def get_aggressive_config() -> Dict[str, Any]:
        """激进型配置 - 适用于追求高收益的交易者"""
        return {
            "enabled": True,
            "trigger_profit_pct": 0.015,       # 1.5% 浮盈就激活
            "confirmation_bars": 2,             # 只需2个周期确认
            
            "normal_mode": {
                "profit_steps": [0.02, 0.04, 0.07, 0.12],
                "stop_ratios": [0.3, 0.4, 0.5, 0.6],    # 较紧的止损
                "min_move_pct": 0.005,          # 0.5% 最小移动
                "max_move_pct": 0.025           # 2.5% 最大移动
            },
            
            "extreme_mode": {
                "profit_steps": [0.03, 0.06, 0.10, 0.18],
                "stop_ratios": [0.25, 0.35, 0.45, 0.55],
                "min_move_pct": 0.008,
                "max_move_pct": 0.03
            },
            
            "risk_control": {
                "max_drawdown_pct": 0.20,      # 20% 最大回撤
                "volatility_threshold": 0.025,
                "volatility_window": 15,
                "mode_switch_threshold": 0.04
            }
        }
    
    @staticmethod
    def get_balanced_config() -> Dict[str, Any]:
        """平衡型配置 - 适用于大多数交易场景"""
        return {
            "enabled": True,
            "trigger_profit_pct": 0.02,        # 2% 浮盈激活
            "confirmation_bars": 3,             # 3个周期确认
            
            "normal_mode": {
                "profit_steps": [0.03, 0.05, 0.08, 0.15],
                "stop_ratios": [0.4, 0.5, 0.6, 0.7],    # 中等止损
                "min_move_pct": 0.006,          # 0.6% 最小移动
                "max_move_pct": 0.02            # 2% 最大移动
            },
            
            "extreme_mode": {
                "profit_steps": [0.05, 0.08, 0.12, 0.20],
                "stop_ratios": [0.3, 0.4, 0.5, 0.6],
                "min_move_pct": 0.008,
                "max_move_pct": 0.025
            },
            
            "risk_control": {
                "max_drawdown_pct": 0.15,      # 15% 最大回撤
                "volatility_threshold": 0.03,
                "volatility_window": 20,
                "mode_switch_threshold": 0.05
            }
        }
    
    @staticmethod
    def get_scalping_config() -> Dict[str, Any]:
        """剥头皮配置 - 适用于短线高频交易"""
        return {
            "enabled": True,
            "trigger_profit_pct": 0.008,       # 0.8% 浮盈激活
            "confirmation_bars": 1,             # 1个周期确认
            
            "normal_mode": {
                "profit_steps": [0.01, 0.02, 0.035, 0.06],
                "stop_ratios": [0.2, 0.3, 0.4, 0.5],    # 非常紧的止损
                "min_move_pct": 0.002,          # 0.2% 最小移动
                "max_move_pct": 0.01            # 1% 最大移动
            },
            
            "extreme_mode": {
                "profit_steps": [0.015, 0.03, 0.05, 0.08],
                "stop_ratios": [0.15, 0.25, 0.35, 0.45],
                "min_move_pct": 0.003,
                "max_move_pct": 0.015
            },
            
            "risk_control": {
                "max_drawdown_pct": 0.08,      # 8% 最大回撤
                "volatility_threshold": 0.02,
                "volatility_window": 10,
                "mode_switch_threshold": 0.03
            }
        }
    
    @staticmethod
    def get_swing_trading_config() -> Dict[str, Any]:
        """波段交易配置 - 适用于中长线持仓"""
        return {
            "enabled": True,
            "trigger_profit_pct": 0.05,        # 5% 浮盈激活
            "confirmation_bars": 8,             # 8个周期确认
            
            "normal_mode": {
                "profit_steps": [0.08, 0.15, 0.25, 0.40],
                "stop_ratios": [0.5, 0.6, 0.7, 0.8],    # 宽松止损
                "min_move_pct": 0.01,           # 1% 最小移动
                "max_move_pct": 0.03            # 3% 最大移动
            },
            
            "extreme_mode": {
                "profit_steps": [0.10, 0.18, 0.30, 0.50],
                "stop_ratios": [0.4, 0.5, 0.6, 0.7],
                "min_move_pct": 0.015,
                "max_move_pct": 0.04
            },
            
            "risk_control": {
                "max_drawdown_pct": 0.25,      # 25% 最大回撤
                "volatility_threshold": 0.04,
                "volatility_window": 30,
                "mode_switch_threshold": 0.07
            }
        }
    
    @staticmethod
    def get_crypto_config() -> Dict[str, Any]:
        """加密货币配置 - 适用于高波动数字资产"""
        return {
            "enabled": True,
            "trigger_profit_pct": 0.04,        # 4% 浮盈激活
            "confirmation_bars": 4,             # 4个周期确认
            
            "normal_mode": {
                "profit_steps": [0.06, 0.12, 0.20, 0.35],
                "stop_ratios": [0.4, 0.5, 0.6, 0.7],
                "min_move_pct": 0.01,           # 1% 最小移动
                "max_move_pct": 0.04            # 4% 最大移动
            },
            
            "extreme_mode": {
                "profit_steps": [0.08, 0.15, 0.25, 0.45],
                "stop_ratios": [0.3, 0.4, 0.5, 0.6],
                "min_move_pct": 0.015,
                "max_move_pct": 0.06
            },
            
            "risk_control": {
                "max_drawdown_pct": 0.30,      # 30% 最大回撤
                "volatility_threshold": 0.06,  # 高波动率阈值
                "volatility_window": 20,
                "mode_switch_threshold": 0.08
            }
        }
    
    @staticmethod
    def get_forex_config() -> Dict[str, Any]:
        """外汇配置 - 适用于外汇市场"""
        return {
            "enabled": True,
            "trigger_profit_pct": 0.015,       # 1.5% 浮盈激活
            "confirmation_bars": 3,             # 3个周期确认
            
            "normal_mode": {
                "profit_steps": [0.02, 0.04, 0.07, 0.12],
                "stop_ratios": [0.35, 0.45, 0.55, 0.65],
                "min_move_pct": 0.003,          # 0.3% 最小移动
                "max_move_pct": 0.015           # 1.5% 最大移动
            },
            
            "extreme_mode": {
                "profit_steps": [0.03, 0.06, 0.10, 0.16],
                "stop_ratios": [0.25, 0.35, 0.45, 0.55],
                "min_move_pct": 0.005,
                "max_move_pct": 0.02
            },
            
            "risk_control": {
                "max_drawdown_pct": 0.10,      # 10% 最大回撤
                "volatility_threshold": 0.02,
                "volatility_window": 24,        # 24小时窗口
                "mode_switch_threshold": 0.035
            }
        }
    
    @staticmethod
    def get_stock_config() -> Dict[str, Any]:
        """股票配置 - 适用于股票市场"""
        return {
            "enabled": True,
            "trigger_profit_pct": 0.025,       # 2.5% 浮盈激活
            "confirmation_bars": 5,             # 5个周期确认
            
            "normal_mode": {
                "profit_steps": [0.04, 0.08, 0.15, 0.25],
                "stop_ratios": [0.5, 0.6, 0.7, 0.75],
                "min_move_pct": 0.005,          # 0.5% 最小移动
                "max_move_pct": 0.02            # 2% 最大移动
            },
            
            "extreme_mode": {
                "profit_steps": [0.06, 0.12, 0.20, 0.35],
                "stop_ratios": [0.4, 0.5, 0.6, 0.65],
                "min_move_pct": 0.008,
                "max_move_pct": 0.03
            },
            
            "risk_control": {
                "max_drawdown_pct": 0.18,      # 18% 最大回撤
                "volatility_threshold": 0.025,
                "volatility_window": 20,
                "mode_switch_threshold": 0.04
            }
        }


def get_config_by_strategy(strategy_type: str) -> Dict[str, Any]:
    """根据策略类型获取配置"""
    templates = TrailingStopConfigTemplates()
    
    config_map = {
        'conservative': templates.get_conservative_config,
        'aggressive': templates.get_aggressive_config,
        'balanced': templates.get_balanced_config,
        'scalping': templates.get_scalping_config,
        'swing': templates.get_swing_trading_config,
        'crypto': templates.get_crypto_config,
        'forex': templates.get_forex_config,
        'stock': templates.get_stock_config
    }
    
    if strategy_type.lower() not in config_map:
        raise ValueError(f"未知的策略类型: {strategy_type}")
    
    return config_map[strategy_type.lower()]()


def customize_config(base_config: Dict[str, Any], **kwargs) -> Dict[str, Any]:
    """自定义配置参数"""
    config = base_config.copy()
    
    # 支持的自定义参数
    if 'trigger_profit_pct' in kwargs:
        config['trigger_profit_pct'] = kwargs['trigger_profit_pct']
    
    if 'confirmation_bars' in kwargs:
        config['confirmation_bars'] = kwargs['confirmation_bars']
    
    if 'max_drawdown_pct' in kwargs:
        config['risk_control']['max_drawdown_pct'] = kwargs['max_drawdown_pct']
    
    if 'volatility_threshold' in kwargs:
        config['risk_control']['volatility_threshold'] = kwargs['volatility_threshold']
    
    # 批量更新normal_mode参数
    if 'normal_mode' in kwargs:
        config['normal_mode'].update(kwargs['normal_mode'])
    
    # 批量更新extreme_mode参数
    if 'extreme_mode' in kwargs:
        config['extreme_mode'].update(kwargs['extreme_mode'])
    
    return config


def validate_config_compatibility(config: Dict[str, Any]) -> bool:
    """验证配置兼容性"""
    try:
        # 检查必需字段
        required_fields = ['enabled', 'trigger_profit_pct', 'confirmation_bars']
        for field in required_fields:
            if field not in config:
                print(f"缺少必需字段: {field}")
                return False
        
        # 检查数值范围
        if not (0 < config['trigger_profit_pct'] < 1):
            print("trigger_profit_pct 应该在 0-1 之间")
            return False
        
        if config['confirmation_bars'] < 1:
            print("confirmation_bars 应该大于等于 1")
            return False
        
        # 检查模式配置
        for mode in ['normal_mode', 'extreme_mode']:
            if mode in config:
                mode_config = config[mode]
                
                # 检查profit_steps和stop_ratios长度一致
                if len(mode_config['profit_steps']) != len(mode_config['stop_ratios']):
                    print(f"{mode} 中 profit_steps 和 stop_ratios 长度不一致")
                    return False
                
                # 检查profit_steps递增
                steps = mode_config['profit_steps']
                if not all(steps[i] < steps[i+1] for i in range(len(steps)-1)):
                    print(f"{mode} 中 profit_steps 应该递增")
                    return False
        
        print("配置验证通过")
        return True
        
    except Exception as e:
        print(f"配置验证异常: {e}")
        return False


if __name__ == "__main__":
    # 示例用法
    print("移动止损配置示例")
    print("=" * 50)
    
    # 获取平衡型配置
    balanced_config = get_config_by_strategy('balanced')
    print("平衡型配置:")
    print(f"激活阈值: {balanced_config['trigger_profit_pct']:.1%}")
    print(f"确认周期: {balanced_config['confirmation_bars']}")
    
    # 自定义配置
    custom_config = customize_config(
        balanced_config,
        trigger_profit_pct=0.03,
        max_drawdown_pct=0.12,
        normal_mode={'min_move_pct': 0.008}
    )
    
    print("\n自定义配置:")
    print(f"激活阈值: {custom_config['trigger_profit_pct']:.1%}")
    print(f"最大回撤: {custom_config['risk_control']['max_drawdown_pct']:.1%}")
    
    # 验证配置
    print(f"\n配置验证结果: {validate_config_compatibility(custom_config)}")