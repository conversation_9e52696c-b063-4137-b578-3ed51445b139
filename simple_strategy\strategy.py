import asyncio
import logging
import json
import time
import pandas as pd
from typing import Dict, List, Any
from datetime import datetime
import ccxt

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/strategy.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 导入模块
from modules.cache_manager import CacheManager
from modules.exchange_connector import ExchangeConnector
from modules.symbol_scorer import SymbolScorer
from modules.channel_breakout_detector import ChannelBreakoutDetector
from modules.position_manager import PositionManager
from modules.risk_manager import RiskManager
from modules.performance_monitor import PerformanceMonitor

class MakerChannelStrategy:
    def __init__(self, config_path: str = "config/config.json"):
        # 加载配置
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        # 初始化模块
        self.cache_manager = CacheManager()
        self.exchange = ExchangeConnector(self.config)
        self.scorer = SymbolScorer(self.config)
        self.detector = ChannelBreakoutDetector(self.config)
        self.position_manager = PositionManager(self.config, self.cache_manager)
        self.risk_manager = RiskManager(self.config)
        self.performance_monitor = PerformanceMonitor()
        
        # 策略状态
        self.is_running = False
        self.last_scan_time = 0
        self.scan_interval = 60  # 1分钟扫描一次
        
    async def initialize(self):
        """初始化策略"""
        logger.info("初始化策略...")
        # 获取并缓存交易对信息
        await self._load_symbols()
        logger.info("策略初始化完成")
    
    async def _load_symbols(self):
        """加载交易对信息"""
        try:
            # 从交易所获取所有交易对
            markets = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.exchange.load_markets
            )
            
            # 筛选USDT交易对
            symbols = [symbol for symbol in markets.keys() if symbol.endswith('/USDT')]
            
            # 保存到缓存
            self.cache_manager.save_symbols(symbols)
            logger.info(f"加载了 {len(symbols)} 个交易对")
            return symbols
        except Exception as e:
            logger.error(f"加载交易对信息失败: {e}")
            # 尝试从缓存加载
            return self.cache_manager.load_symbols()
    
    async def run(self):
        """运行策略主循环"""
        logger.info("启动策略主循环...")
        self.is_running = True
        
        while self.is_running:
            try:
                # 检查是否需要每日平仓
                if self.position_manager.should_close_all_positions():
                    await self._close_all_positions()
                
                # 执行策略逻辑
                await self._execute_strategy()
                
                # 等待下次执行
                await asyncio.sleep(self.scan_interval)
                
            except Exception as e:
                logger.error(f"策略执行出错: {e}")
                self.performance_monitor.record_error()
                await asyncio.sleep(10)  # 出错后等待10秒再继续
    
    async def _execute_strategy(self):
        """执行策略逻辑"""
        # 1. 获取候选交易对
        candidates = await self._scan_symbols()
        
        # 2. 评分和筛选
        ranked_symbols = self.scorer.rank_symbols(candidates)
        
        # 3. 检查突破信号
        for symbol_data in ranked_symbols:
            symbol = symbol_data['symbol']
            
            # 检查是否可以开新仓
            if not self.position_manager.can_open_new_position():
                logger.info("已达最大持仓数，跳过开仓")
                continue
            
            # 获取K线数据
            ohlcv = await self.exchange.fetch_ohlcv(symbol, self.config['strategy']['timeframe'], 100)
            if not ohlcv or len(ohlcv) < 20:
                continue
            
            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # 计算技术指标
            df = self.detector.calculate_indicators(df)
            
            # 检测突破信号
            is_breakout, signal_info = self.detector.detect_breakout(df)
            
            if is_breakout:
                logger.info(f"发现突破信号: {symbol}")
                
                # 风险检查
                if not self.risk_manager.check_kill_switch():
                    # 执行开仓逻辑
                    await self._open_position(symbol, signal_info['upper_band'])
    
    async def _scan_symbols(self) -> List[Dict[str, Any]]:
        """扫描交易对"""
        try:
            # 使用缓存的交易对或重新加载
            symbols = self.cache_manager.load_symbols()
            if not symbols:
                symbols = await self._load_symbols()
            
            candidates = []
            
            # 获取每个交易对的基本数据
            for symbol in symbols[:50]:  # 限制处理前50个交易对
                try:
                    ticker = await self.exchange.fetch_ticker(symbol)
                    if not ticker:
                        continue
                    
                    # 构造交易对数据
                    symbol_data = {
                        'symbol': symbol,
                        'price_change': ticker.get('percentage', 0),
                        'volume': ticker.get('quoteVolume', 0),
                        'volatility': ticker.get('high', 0) / ticker.get('low', 1) - 1 if ticker.get('low', 0) > 0 else 0,
                        'momentum': 0,  # 简化处理
                        'depth': 0  # 简化处理
                    }
                    
                    candidates.append(symbol_data)
                    
                except Exception as e:
                    logger.error(f"获取交易对数据失败 {symbol}: {e}")
                    continue
            
            return candidates
            
        except Exception as e:
            logger.error(f"扫描交易对失败: {e}")
            return []
    
    async def _open_position(self, symbol: str, resistance_level: float):
        """开仓逻辑"""
        try:
            # 获取当前价格
            ticker = await self.exchange.fetch_ticker(symbol)
            if not ticker:
                return
            
            current_price = ticker['last']
            
            # 计算回踩价格（0.38%回踩）
            pullback_price = resistance_level * 0.9962
            
            # 检查是否值得入场
            if current_price > pullback_price:
                logger.info(f"{symbol} 价格已高于回踩位，等待回调")
                return
            
            # 计算仓位大小
            position_size = self.position_manager.calculate_position_size(symbol, pullback_price)
            if position_size <= 0:
                return
            
            # 风险验证
            balance = await self.exchange.fetch_balance()
            account_balance = balance.get('total', {}).get('USDT', 0)
            
            if not self.risk_manager.validate_order(symbol, position_size, pullback_price, account_balance):
                return
            
            # 创建限价单
            order = await self.exchange.create_limit_order(
                symbol, 'buy', position_size, pullback_price
            )
            
            if order and order.get('id'):
                logger.info(f"创建限价单成功: {symbol} buy {position_size}@{pullback_price}")
                
                # 保存持仓信息
                position_data = {
                    'symbol': symbol,
                    'amount': position_size,
                    'entry_price': pullback_price,
                    'stop_loss': pullback_price * (1 - self.config['strategy']['stop_loss_pct']),
                    'take_profit': pullback_price * (1 + self.config['strategy']['take_profit_pct']),
                    'addition_count': 0,
                    'has_moved_stop': False,
                    'order_id': order['id']
                }
                
                self.position_manager.update_position(symbol, position_data)
                self.performance_monitor.record_order(True)
            else:
                logger.error(f"创建限价单失败: {symbol}")
                self.performance_monitor.record_order(False)
                
        except Exception as e:
            logger.error(f"开仓失败 {symbol}: {e}")
            self.performance_monitor.record_error()
    
    async def _close_all_positions(self):
        """平仓所有头寸"""
        logger.info("执行每日平仓...")
        positions = self.position_manager.get_positions()
        
        for symbol in list(positions.keys()):
            try:
                position = positions[symbol]
                # 创建市价单平仓
                order = await self.exchange.create_market_order(
                    symbol, 'sell', position['amount']
                )
                
                if order:
                    logger.info(f"平仓成功: {symbol}")
                    # 移除持仓记录
                    self.position_manager.remove_position(symbol)
                    self.performance_monitor.record_order(True)
                else:
                    logger.error(f"平仓失败: {symbol}")
                    self.performance_monitor.record_order(False)
                    
            except Exception as e:
                logger.error(f"平仓出错 {symbol}: {e}")
                self.performance_monitor.record_error()
    
    def stop(self):
        """停止策略"""
        logger.info("停止策略...")
        self.is_running = False

# 主函数
async def main():
    strategy = MakerChannelStrategy()
    await strategy.initialize()
    
    try:
        await strategy.run()
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        strategy.stop()

if __name__ == "__main__":
    asyncio.run(main())