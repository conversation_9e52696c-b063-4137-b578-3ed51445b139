#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
移动止损管理器
实现基于浮盈触发的移动止损机制，支持常规和极端行情差异化处理
"""

import yaml
import logging
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from config_validator import ConfigValidator
from trailing_stop_logger import TrailingStopLogger


class MarketMode(Enum):
    """市场模式枚举"""
    NORMAL = "normal"      # 常规行情
    EXTREME = "extreme"    # 极端行情


@dataclass
class TrailingStopConfig:
    """移动止损配置"""
    enabled: bool = False
    trigger_profit_pct: float = 0.08
    
    # 常规行情设置
    normal_profit_step_pct: float = 0.10
    normal_stop_move_pct: float = 0.07
    
    # 极端行情设置
    extreme_enabled: bool = False
    extreme_profit_step_pct: float = 0.05
    extreme_stop_move_pct: float = 0.04
    volatility_threshold: float = 0.15
    
    # 风控规则
    only_move_up: bool = True
    min_stop_distance_pct: float = 0.02
    max_trailing_count: int = 10


@dataclass
class TrailingStopState:
    """移动止损状态"""
    symbol: str
    side: str = "LONG"  # 添加交易方向属性
    is_active: bool = False
    initial_entry_price: float = 0.0
    current_stop_price: float = 0.0
    highest_stop_price: float = 0.0
    highest_profit_pct: float = 0.0
    trailing_count: int = 0
    last_move_time: Optional[datetime] = None
    market_mode: MarketMode = MarketMode.NORMAL
    volatility_history: List[float] = field(default_factory=list)


class TrailingStopManager:
    """移动止损管理器"""
    
    def __init__(self, config_path: str = "config/config.yaml", logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        self.validator = ConfigValidator()
        
        # 初始化日志记录器
        self.stop_logger = TrailingStopLogger(logger=self.logger)
        
        # 验证配置
        if not self.validate_config():
            raise ValueError("移动止损配置验证失败")
        
        # 价格历史缓存（用于计算波动率）
        self.price_history: Dict[str, List[Tuple[datetime, float]]] = {}
        
        self.logger.info("移动止损管理器初始化完成")
    
    def _load_config(self, config_path: str) -> TrailingStopConfig:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            trailing_config = config_data.get('trailing_stop', {})
            
            return TrailingStopConfig(
                enabled=trailing_config.get('enabled', False),
                trigger_profit_pct=trailing_config.get('trigger_profit_pct', 0.08),
                
                normal_profit_step_pct=trailing_config.get('normal_market', {}).get('profit_step_pct', 0.10),
                normal_stop_move_pct=trailing_config.get('normal_market', {}).get('stop_move_pct', 0.07),
                
                extreme_enabled=trailing_config.get('extreme_market', {}).get('enabled', False),
                extreme_profit_step_pct=trailing_config.get('extreme_market', {}).get('profit_step_pct', 0.05),
                extreme_stop_move_pct=trailing_config.get('extreme_market', {}).get('stop_move_pct', 0.04),
                volatility_threshold=trailing_config.get('extreme_market', {}).get('volatility_threshold', 0.15),
                
                only_move_up=trailing_config.get('risk_control', {}).get('only_move_up', True),
                min_stop_distance_pct=trailing_config.get('risk_control', {}).get('min_stop_distance_pct', 0.02),
                max_trailing_count=trailing_config.get('risk_control', {}).get('max_trailing_count', 10)
            )
            
        except Exception as e:
            self.log.error(f"加载移动止损配置失败: {e}")
            return TrailingStopConfig()
    
    def validate_config(self) -> bool:
        """验证配置参数的合理性"""
        try:
            errors = []
            
            # 基础参数验证
            if self.config.trigger_profit_pct <= 0 or self.config.trigger_profit_pct > 1:
                errors.append("触发浮盈百分比必须在0-100%之间")
            
            # 常规行情参数验证
            if self.config.normal_profit_step_pct <= 0 or self.config.normal_profit_step_pct > 1:
                errors.append("常规行情盈利步长必须在0-100%之间")
            
            if self.config.normal_stop_move_pct <= 0 or self.config.normal_stop_move_pct > 1:
                errors.append("常规行情止损移动幅度必须在0-100%之间")
            
            # 极端行情参数验证
            if self.config.extreme_enabled:
                if self.config.extreme_profit_step_pct <= 0 or self.config.extreme_profit_step_pct > 1:
                    errors.append("极端行情盈利步长必须在0-100%之间")
                
                if self.config.extreme_stop_move_pct <= 0 or self.config.extreme_stop_move_pct > 1:
                    errors.append("极端行情止损移动幅度必须在0-100%之间")
                
                if self.config.volatility_threshold <= 0 or self.config.volatility_threshold > 1:
                    errors.append("波动率阈值必须在0-100%之间")
            
            # 风控参数验证
            if self.config.min_stop_distance_pct <= 0 or self.config.min_stop_distance_pct > 0.5:
                errors.append("最小止损距离必须在0-50%之间")
            
            if self.config.max_trailing_count <= 0 or self.config.max_trailing_count > 50:
                errors.append("最大移动次数必须在1-50之间")
            
            # 逻辑合理性验证
            if self.config.normal_stop_move_pct >= self.config.normal_profit_step_pct:
                errors.append("常规行情止损移动幅度不应大于等于盈利步长")
            
            if self.config.extreme_enabled and self.config.extreme_stop_move_pct >= self.config.extreme_profit_step_pct:
                errors.append("极端行情止损移动幅度不应大于等于盈利步长")
            
            if errors:
                for error in errors:
                    self.logger.error(f"配置验证失败: {error}")
                return False
            
            self.logger.info("移动止损配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证异常: {e}")
            return False
    
    def initialize_position(self, symbol: str, side: str, entry_price: float, current_price: float) -> TrailingStopState:
        """初始化持仓的移动止损状态"""
        try:
            if not self.config.enabled:
                return None
            
            # 创建移动止损状态
            state = TrailingStopState(
                symbol=symbol,
                side=side,
                is_active=False,
                initial_entry_price=entry_price,
                current_stop_price=0.0,
                highest_stop_price=0.0,
                highest_profit_pct=0.0,
                trailing_count=0,
                last_move_time=None,
                market_mode=MarketMode.NORMAL,
                volatility_history=[]
            )
            
            # 记录初始化日志
            self.logger.info(f"{symbol} 移动止损状态初始化完成，入场价: {entry_price:.6f}")
            return state
            
        except Exception as e:
            self.logger.error(f"{symbol} 移动止损初始化失败: {e}")
            return None
    
    def calculate_volatility(self, symbol: str, price_history: List[float]) -> float:
        """计算价格波动率"""
        try:
            if len(price_history) < 2:
                return 0.0
            
            # 计算价格变化率
            returns = []
            for i in range(1, len(price_history)):
                returns.append((price_history[i] - price_history[i-1]) / price_history[i-1])
            
            # 计算标准差作为波动率
            if len(returns) == 0:
                return 0.0
            
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            volatility = variance ** 0.5
            
            return volatility
            
        except Exception as e:
            self.log.error(f"{symbol} 波动率计算失败: {e}")
            return 0.0
    
    def determine_market_mode(self, symbol: str, current_volatility: float) -> MarketMode:
        """确定当前市场模式"""
        try:
            if not self.config.extreme_enabled:
                return MarketMode.NORMAL
            
            # 更新波动率历史
            state = self.trailing_states.get(symbol)
            if state:
                state.volatility_history.append(current_volatility)
                # 保持最近20个数据点
                if len(state.volatility_history) > 20:
                    state.volatility_history.pop(0)
                
                # 计算平均波动率
                avg_volatility = sum(state.volatility_history) / len(state.volatility_history)
                
                if avg_volatility > self.config.volatility_threshold:
                    return MarketMode.EXTREME
            
            return MarketMode.NORMAL
            
        except Exception as e:
            self.logger.error(f"{symbol} 市场模式判断失败: {e}")
            return MarketMode.NORMAL
    
    def update_trailing_stop(self, state: TrailingStopState, current_price: float, 
                           price_history: Optional[List[float]] = None) -> Tuple[bool, Optional[float]]:
        """更新移动止损"""
        try:
            if not self.config.enabled or not state:
                return False, None
            
            # 计算当前浮盈百分比
            if state.side == "LONG":
                profit_pct = (current_price - state.initial_entry_price) / state.initial_entry_price
            else:
                profit_pct = (state.initial_entry_price - current_price) / state.initial_entry_price
            
            # 检查是否达到激活条件
            if not state.is_active and profit_pct >= self.config.trigger_profit_pct:
                state.is_active = True
                state.current_stop_price = current_price * (0.97 if state.side == "LONG" else 1.03)
                state.highest_stop_price = state.current_stop_price
                
                # 记录激活日志
                self.stop_logger.log_activation(
                    symbol=state.symbol,
                    entry_price=state.initial_entry_price,
                    current_price=current_price,
                    unrealized_pnl_pct=profit_pct,
                    initial_stop_price=state.current_stop_price
                )
                
                self.logger.info(f"{state.symbol} 移动止损已激活，当前浮盈: {profit_pct:.2%}")
                return True, state.current_stop_price
            
            if not state.is_active:
                return False, None
            
            # 确定市场模式
            current_volatility = 0.0
            if price_history:
                current_volatility = self.calculate_volatility(state.symbol, price_history)
            
            market_mode = self.determine_market_mode(state.symbol, current_volatility)
            
            # 根据市场模式选择参数
            if market_mode == MarketMode.EXTREME:
                profit_step = self.config.extreme_profit_step_pct
                stop_move = self.config.extreme_stop_move_pct
            else:
                profit_step = self.config.normal_profit_step_pct
                stop_move = self.config.normal_stop_move_pct
            
            # 计算应该移动的次数
            expected_moves = int(profit_pct / profit_step)
            
            # 检查是否需要移动止损
            if expected_moves > state.trailing_count and state.trailing_count < self.config.max_trailing_count:
                # 计算需要移动的次数
                moves_to_make = expected_moves - state.trailing_count
                total_move_pct = moves_to_make * stop_move
                
                if state.side == "LONG":
                    new_stop_price = state.current_stop_price * (1 + total_move_pct)
                else:
                    new_stop_price = state.current_stop_price * (1 - total_move_pct)
                
                # 风控检查：只允许上移
                if self.config.only_move_up:
                    if state.side == "LONG" and new_stop_price <= state.highest_stop_price:
                        return False, None
                    elif state.side == "SHORT" and new_stop_price >= state.highest_stop_price:
                        return False, None
                
                # 检查最小止损距离
                min_distance = current_price * self.config.min_stop_distance_pct
                if state.side == "LONG" and (current_price - new_stop_price) < min_distance:
                    new_stop_price = current_price - min_distance
                elif state.side == "SHORT" and (new_stop_price - current_price) < min_distance:
                    new_stop_price = current_price + min_distance
                
                # 更新状态
                old_stop_price = state.current_stop_price
                state.current_stop_price = new_stop_price
                state.highest_stop_price = max(state.highest_stop_price, new_stop_price) if state.side == "LONG" else min(state.highest_stop_price, new_stop_price)
                state.trailing_count = expected_moves
                state.last_update_time = datetime.now()
                state.last_profit_level = profit_pct
                
                # 记录移动日志
                self.stop_logger.log_update(
                    symbol=state.symbol,
                    old_stop_price=old_stop_price,
                    new_stop_price=new_stop_price,
                    current_price=current_price,
                    unrealized_pnl_pct=profit_pct,
                    market_mode=market_mode.value,
                    profit_step=self.config.normal_profit_step_pct if market_mode == MarketMode.NORMAL else self.config.extreme_profit_step_pct,
                    stop_move=stop_move,
                    move_count=moves_to_make,
                    highest_stop_price=state.highest_stop_price,
                    reason="价格上涨触发移动"
                )
                
                self.logger.info(f"{state.symbol} 移动止损更新: {new_stop_price:.6f} "
                            f"(模式: {market_mode.value}, 移动次数: {moves_to_make}, "
                            f"当前浮盈: {profit_pct:.2%})")
                
                return True, new_stop_price
            
            return False, None
            
        except Exception as e:
            self.logger.error(f"{state.symbol} 移动止损更新失败: {e}")
            return False, None
    
    def remove_position(self, symbol: str) -> bool:
        """移除持仓的移动止损状态"""
        try:
            # 记录禁用日志
            self.stop_logger.log_disable(
                symbol=symbol,
                reason="持仓关闭"
            )
            
            self.logger.info(f"{symbol} 移动止损状态已清除")
            return True
        except Exception as e:
            self.logger.error(f"{symbol} 移动止损状态清除失败: {e}")
            return False
    
    def check_stop_triggered(self, state: TrailingStopState, current_price: float) -> bool:
        """检查是否触发移动止损"""
        try:
            if not state or not state.is_active or not state.current_stop_price:
                return False
            
            triggered = False
            if state.side == "LONG" and current_price <= state.current_stop_price:
                triggered = True
            elif state.side == "SHORT" and current_price >= state.current_stop_price:
                triggered = True
            
            if triggered:
                # 记录触发日志
                protected_profit = (state.current_stop_price - state.initial_entry_price) / state.initial_entry_price if state.side == "LONG" else (state.initial_entry_price - state.current_stop_price) / state.initial_entry_price
                final_pnl_pct = (current_price - state.initial_entry_price) / state.initial_entry_price if state.side == "LONG" else (state.initial_entry_price - current_price) / state.initial_entry_price
                
                self.stop_logger.log_trigger(
                    symbol=state.symbol,
                    trigger_price=current_price,
                    protected_profit=protected_profit,
                    final_pnl_pct=final_pnl_pct,
                    total_moves=state.trailing_count
                )
                
                self.logger.info(f"{state.symbol} 移动止损已触发，触发价: {current_price:.6f}")
            
            return triggered
            
        except Exception as e:
            self.logger.error(f"{state.symbol} 移动止损触发检查失败: {e}")
            return False
    
    def get_daily_summary(self) -> Dict:
        """获取日度摘要"""
        return self.stop_logger.get_daily_summary()
    
    def get_performance_metrics(self) -> Dict:
        """获取性能指标"""
        return self.stop_logger.get_performance_metrics()
    
    def is_enabled(self) -> bool:
        """检查移动止损是否启用"""
        return self.config.enabled