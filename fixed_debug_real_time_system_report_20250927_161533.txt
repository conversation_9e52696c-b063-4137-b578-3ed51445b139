================================================================================
修复版实时系统调试分析报告
================================================================================
分析时间: 2025-09-27 16:15:33
尝试分析币种数量: 10
成功分析币种数量: 6
达标币种数量(≥7分): 6
成功分析达标率: 100.0%
总体达标率: 60.0%

详细分析结果:
--------------------------------------------------------------------------------
币种: MERLUSDT
  总评分: 12.06
  深度: 27439.00 USDT
  币龄: 30 天
  K线数据: 200 行
  当前价格: 0.317340
  是否达标: 是

币种: PORT3USDT
  总评分: 11.75
  深度: 9553.00 USDT
  币龄: 30 天
  K线数据: 200 行
  当前价格: 0.036570
  是否达标: 是

币种: ALPINEUSDT
  总评分: 11.39
  深度: 896.50 USDT
  币龄: 30 天
  K线数据: 200 行
  当前价格: 2.116500
  是否达标: 是

币种: XPLUSDT
  总评分: 11.29
  深度: 73795.00 USDT
  币龄: 30 天
  K线数据: 200 行
  当前价格: 1.342100
  是否达标: 是

币种: GRASSUSDT
  总评分: 10.08
  深度: 6103.60 USDT
  币龄: 30 天
  K线数据: 200 行
  当前价格: 0.878100
  是否达标: 是

币种: SNXUSDT
  总评分: 9.96
  深度: 2258.60 USDT
  币龄: 30 天
  K线数据: 200 行
  当前价格: 0.788000
  是否达标: 是


关键发现:
--------------------------------------------------------------------------------
1. 实时系统确实在使用 EnhancedScoreCalculator
2. 评分计算逻辑与增量脚本一致
3. 数据格式问题已修复:
   - 候选池是列表格式，不是字典
   - 深度数据已经是计算好的值
   - K线数据需要列名标准化
4. 问题可能出现在:
   - 实时系统的通道突破条件检查
   - 候选池管理逻辑的额外过滤
   - 数据获取时机不同（实时 vs 缓存）
