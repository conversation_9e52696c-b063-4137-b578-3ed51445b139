#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from binance_trader import BinanceTrader

def load_config():
    """加载配置文件"""
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 确保network_params存在且结构正确
        if 'network_params' not in config:
            # 从根级别提取参数到network_params
            config['network_params'] = {
                'api_key': config.get('api_key', ''),
                'api_secret': config.get('api_secret', ''),
                'base_url': config.get('base_url', 'https://fapi.binance.com'),
                'max_rate': config.get('max_rate', 15),
                'trade_rate': config.get('trade_rate', 10),
                'proxy': config.get('proxy', {
                    'enabled': False,
                    'host': '',
                    'port': 0
                })
            }
        else:
            # 确保network_params中包含所有必要字段
            network_params = config['network_params']
            if 'api_key' not in network_params:
                network_params['api_key'] = config.get('api_key', '')
            if 'api_secret' not in network_params:
                network_params['api_secret'] = config.get('api_secret', '')
            if 'base_url' not in network_params:
                network_params['base_url'] = config.get('base_url', 'https://fapi.binance.com')
            if 'proxy' not in network_params:
                network_params['proxy'] = config.get('proxy', {
                    'enabled': False,
                    'host': '',
                    'port': 0
                })
        
        return config
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None

def test_single_kline_request(trader, symbol, limit, thread_id):
    """单个K线请求测试"""
    try:
        print(f"[线程{thread_id}] 开始获取 {symbol} 的K线数据，limit={limit}")
        start_time = time.time()
        
        klines = trader.get_klines(symbol, '1m', limit)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if isinstance(klines, list) and len(klines) > 0:
            print(f"[线程{thread_id}] ✅ {symbol}: 成功获取 {len(klines)} 条K线数据，耗时 {duration:.2f}s")
            return {
                'thread_id': thread_id,
                'symbol': symbol,
                'success': True,
                'count': len(klines),
                'duration': duration,
                'first_timestamp': klines[0][0] if klines else None,
                'last_timestamp': klines[-1][0] if klines else None
            }
        else:
            print(f"[线程{thread_id}] ❌ {symbol}: K线数据获取失败或为空，返回: {klines}")
            return {
                'thread_id': thread_id,
                'symbol': symbol,
                'success': False,
                'count': 0,
                'duration': duration,
                'error': str(klines)
            }
    except Exception as e:
        print(f"[线程{thread_id}] ❌ {symbol}: 异常 - {e}")
        return {
            'thread_id': thread_id,
            'symbol': symbol,
            'success': False,
            'count': 0,
            'duration': 0,
            'error': str(e)
        }

def test_concurrent_kline_requests():
    """测试并发K线请求"""
    print("=" * 60)
    print("🧪 并发K线数据获取测试")
    print("=" * 60)
    
    # 加载配置
    config = load_config()
    if not config:
        print("❌ 配置加载失败，退出测试")
        return
    
    # 初始化交易器
    try:
        trader = BinanceTrader(config)
        print("✅ BinanceTrader 初始化成功")
    except Exception as e:
        print(f"❌ BinanceTrader 初始化失败: {e}")
        return
    
    # 测试币种和参数
    test_symbols = ['BCHUSDT', 'TRXUSDT', 'ALPINEUSDT', 'NKNUSDT', 'FLOWUSDT']
    test_limits = [20, 200]
    
    # 创建测试任务
    tasks = []
    for i, symbol in enumerate(test_symbols):
        for j, limit in enumerate(test_limits):
            tasks.append((symbol, limit, f"{i+1}-{j+1}"))
    
    print(f"📋 准备执行 {len(tasks)} 个并发任务")
    print(f"🔧 测试币种: {test_symbols}")
    print(f"🔧 测试限制: {test_limits}")
    print("-" * 60)
    
    # 执行并发测试
    results = []
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=10) as executor:
        # 提交所有任务
        future_to_task = {
            executor.submit(test_single_kline_request, trader, symbol, limit, thread_id): (symbol, limit, thread_id)
            for symbol, limit, thread_id in tasks
        }
        
        # 收集结果
        for future in as_completed(future_to_task):
            result = future.result()
            results.append(result)
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📊 测试结果分析")
    print("=" * 60)
    
    successful_requests = [r for r in results if r['success']]
    failed_requests = [r for r in results if not r['success']]
    
    print(f"✅ 成功请求: {len(successful_requests)}/{len(results)}")
    print(f"❌ 失败请求: {len(failed_requests)}/{len(results)}")
    print(f"⏱️  总耗时: {total_duration:.2f}s")
    print(f"📈 成功率: {len(successful_requests)/len(results)*100:.1f}%")
    
    if successful_requests:
        avg_duration = sum(r['duration'] for r in successful_requests) / len(successful_requests)
        print(f"⏱️  平均请求耗时: {avg_duration:.2f}s")
        
        # 按数据量统计
        count_stats = {}
        for r in successful_requests:
            count = r['count']
            if count not in count_stats:
                count_stats[count] = 0
            count_stats[count] += 1
        
        print(f"📊 数据量分布: {count_stats}")
    
    if failed_requests:
        print("\n❌ 失败请求详情:")
        for r in failed_requests:
            print(f"   线程{r['thread_id']} - {r['symbol']}: {r.get('error', '未知错误')}")
    
    # 检查是否有"仅1条数据"的问题
    single_data_requests = [r for r in successful_requests if r['count'] == 1]
    if single_data_requests:
        print(f"\n⚠️  发现 {len(single_data_requests)} 个请求只返回1条数据:")
        for r in single_data_requests:
            print(f"   线程{r['thread_id']} - {r['symbol']}: 只获取到1条数据")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    test_concurrent_kline_requests()