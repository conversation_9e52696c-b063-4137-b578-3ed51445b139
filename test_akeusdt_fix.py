#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AKEUSDT K线数据获取修复效果
验证新币种处理器是否解决问题
"""

import sys
import os
import time
import pandas as pd
import yaml
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入新币种处理器
from new_coin_handler import NewCoinHandler

# 导入策略相关模块
from strategy.maker_channel import MakerChannelStrategy
from binance_trader import BinanceTrader

def test_new_coin_handler():
    """测试新币种处理器"""
    print("=" * 60)
    print("测试新币种处理器")
    print("=" * 60)
    
    # 加载配置文件
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print("配置文件config.yaml未找到，使用默认配置")
        config = {
            'network_params': {
                'api_key': 'test_key',
                'api_secret': 'test_secret'
            }
        }
    
    # 初始化交易器（用于NewCoinHandler）
    trader = BinanceTrader(config)
    
    # 初始化新币种处理器
    handler = NewCoinHandler(trader)
    
    # 测试AKEUSDT是否被识别为新币种
    test_symbols = ['AKEUSDT', 'BTCUSDT', 'ETHUSDT']
    
    for symbol in test_symbols:
        # 模拟不同的币龄
        age_days_list = [0.5, 2, 10, 100]
        
        for age_days in age_days_list:
            is_new = handler.is_new_coin(symbol, age_days)
            print(f"{symbol} (币龄{age_days}天): {'新币种' if is_new else '非新币种'}")
    
    print("\n" + "=" * 60)
    print("测试新币种K线数据获取")
    print("=" * 60)
    
    # 测试AKEUSDT的K线数据获取
    symbol = 'AKEUSDT'
    age_days = 0.5  # 模拟极新币种
    
    try:
        df = handler.get_klines_for_new_coin(symbol, '15m', 200, age_days)
        if df is not None:
            print(f"✓ {symbol}: 成功获取{len(df)}条K线数据")
            print(f"  时间范围: {df.index[0]} 到 {df.index[-1]}")
            print(f"  数据列: {list(df.columns)}")
            
            # 验证数据
            is_valid, message = handler.validate_new_coin_data(symbol, df)
            print(f"  数据验证: {'通过' if is_valid else '失败'} - {message}")
        else:
            print(f"✗ {symbol}: 获取K线数据失败")
    except Exception as e:
        print(f"✗ {symbol}: 获取K线数据异常 - {e}")

def test_strategy_integration():
    """测试策略集成效果"""
    print("\n" + "=" * 60)
    print("测试策略集成效果")
    print("=" * 60)
    
    try:
        # 加载配置文件
        import yaml
        try:
            with open('config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
        except FileNotFoundError:
            print("配置文件config.yaml未找到，使用默认配置")
            config = {
                'network_params': {
                    'api_key': 'test_key',
                    'api_secret': 'test_secret'
                },
                'first_nominal': 100,
                'symbol_limit': 50,
                'min_score': 7
            }
        
        # 初始化交易器和策略
        trader = BinanceTrader(config)
        import logging
        logger = logging.getLogger('test')
        
        # 初始化策略
        strategy = MakerChannelStrategy(trader, config)
        
        # 测试AKEUSDT的K线数据获取
        symbol = 'AKEUSDT'
        intervals = ['15m', '1h', '4h']
        
        for interval in intervals:
            try:
                df = strategy.get_klines(symbol, interval, 100)
                if df is not None:
                    print(f"✓ {symbol} ({interval}): 成功获取{len(df)}条K线数据")
                    print(f"  时间范围: {df.index[0]} 到 {df.index[-1]}")
                else:
                    print(f"✗ {symbol} ({interval}): 获取K线数据失败")
            except Exception as e:
                print(f"✗ {symbol} ({interval}): 获取K线数据异常 - {e}")
                
    except Exception as e:
        print(f"✗ 策略集成测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def test_multiple_symbols():
    """测试多个币种的K线数据获取"""
    print("\n" + "=" * 60)
    print("测试多个币种K线数据获取")
    print("=" * 60)
    
    try:
        # 加载配置文件
        import yaml
        try:
            with open('config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
        except FileNotFoundError:
            print("配置文件config.yaml未找到，使用默认配置")
            config = {
                'network_params': {
                    'api_key': 'test_key',
                    'api_secret': 'test_secret'
                },
                'first_nominal': 100,
                'symbol_limit': 50,
                'min_score': 7
            }
        
        # 初始化交易器和策略
        trader = BinanceTrader(config)
        import logging
        logger = logging.getLogger('test')
        
        # 初始化策略
        strategy = MakerChannelStrategy(trader, config)
        
        # 测试多个币种
        test_symbols = ['AKEUSDT', 'BTCUSDT', 'ETHUSDT']
        interval = '15m'
        limit = 100
        
        for symbol in test_symbols:
            try:
                df = strategy.get_klines(symbol, interval, limit)
                if df is not None:
                    print(f"✓ {symbol}: 成功获取{len(df)}条K线数据")
                    print(f"  时间范围: {df.index[0]} 到 {df.index[-1]}")
                    
                    # 检查是否为新币种处理
                    if hasattr(strategy, 'new_coin_handler'):
                        is_new = strategy.new_coin_handler.is_new_coin(symbol)
                        print(f"  新币种识别: {'是' if is_new else '否'}")
                else:
                    print(f"✗ {symbol}: 获取K线数据失败")
            except Exception as e:
                print(f"✗ {symbol}: 获取K线数据异常 - {e}")
                
    except Exception as e:
        print(f"✗ 多币种测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    print(f"AKEUSDT K线数据获取修复效果测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 测试新币种处理器
    test_new_coin_handler()
    
    # 2. 测试策略集成效果
    test_strategy_integration()
    
    # 3. 测试多个币种
    test_multiple_symbols()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()