#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络状态检查调试脚本
测试MakerChannelStrategy的网络检查逻辑是否过于严格
"""

import sys
import json
import time
import logging
from datetime import datetime

def debug_network_check():
    """调试网络状态检查"""
    print("开始调试网络状态检查...")
    
    try:
        # 1. 导入必要模块
        print("1. 导入模块...")
        from binance_trader import BinanceTrader
        from strategy.maker_channel import MakerChannelStrategy
        
        # 2. 加载配置
        print("2. 加载配置...")
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 添加策略需要的配置参数
        config['first_nominal'] = 100
        config['order_ttl'] = 300
        config['network_check_interval'] = 60
        config['max_symbol_failures'] = 5
        config['failure_cooldown'] = 3600
        config['network_error_cooldown'] = 300
        
        # 3. 初始化交易器和策略
        print("3. 初始化BinanceTrader和策略...")
        trader = BinanceTrader(config)
        
        # 临时禁用日志输出，避免刷屏
        logging.getLogger('MakerChannel').setLevel(logging.ERROR)
        
        try:
            strategy = MakerChannelStrategy(trader, config)
            print("   ✅ 策略初始化成功")
        except Exception as e:
            print(f"   ❌ 策略初始化失败: {e}")
            return
        
        # 4. 测试网络状态检查
        print("\n4. 测试网络状态检查...")
        
        # 多次测试网络状态检查
        for i in range(5):
            try:
                start_time = time.time()
                network_status = strategy._check_network_status()
                end_time = time.time()
                
                print(f"   第{i+1}次检查:")
                print(f"     连接状态: {'✅' if network_status.get('connected', False) else '❌'}")
                print(f"     API状态: {'✅' if network_status.get('binance_api_ok', False) else '❌'}")
                print(f"     延迟: {network_status.get('latency', 0):.2f}ms")
                print(f"     检查耗时: {(end_time - start_time)*1000:.2f}ms")
                if network_status.get('last_error'):
                    print(f"     错误信息: {network_status.get('last_error')}")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"   第{i+1}次检查异常: {e}")
        
        # 5. 测试直接ping API
        print(f"\n5. 测试直接ping API...")
        for i in range(3):
            try:
                start_time = time.time()
                response = trader.http.get('/fapi/v1/ping')
                end_time = time.time()
                
                print(f"   第{i+1}次ping:")
                print(f"     响应: {response}")
                print(f"     耗时: {(end_time - start_time)*1000:.2f}ms")
                
            except Exception as e:
                print(f"   第{i+1}次ping异常: {e}")
        
        # 6. 测试在网络检查失败情况下的K线获取
        print(f"\n6. 测试K线获取（包含网络检查）...")
        
        # 临时启用DEBUG日志来看详细过程
        strategy_logger = logging.getLogger('MakerChannel')
        original_level = strategy_logger.level
        strategy_logger.setLevel(logging.DEBUG)
        
        # 添加控制台处理器来查看日志
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        strategy_logger.addHandler(console_handler)
        
        test_symbols = ['BTCUSDT', 'ALPINEUSDT', 'BCHUSDT']
        
        for symbol in test_symbols:
            print(f"\n   测试 {symbol}:")
            try:
                start_time = time.time()
                klines_df = strategy.get_klines(symbol, '15m', 20)
                end_time = time.time()
                
                print(f"     结果类型: {type(klines_df)}")
                print(f"     耗时: {(end_time - start_time):.2f}s")
                
                if klines_df is not None:
                    if hasattr(klines_df, '__len__'):
                        print(f"     ✅ 成功获取数据，长度: {len(klines_df)}")
                    else:
                        print(f"     ✅ 成功获取数据: {klines_df}")
                else:
                    print(f"     ❌ 获取失败，返回None")
                    
            except Exception as e:
                print(f"     ❌ 获取异常: {e}")
        
        # 恢复日志级别
        strategy_logger.setLevel(original_level)
        strategy_logger.removeHandler(console_handler)
        
        # 7. 检查网络检查间隔设置
        print(f"\n7. 检查网络检查配置...")
        print(f"   网络检查间隔: {strategy.network_check_interval}秒")
        print(f"   上次检查时间: {strategy.last_network_check}")
        print(f"   当前时间: {time.time()}")
        print(f"   距离上次检查: {time.time() - strategy.last_network_check:.2f}秒")
        
        print("\n🔍 网络状态检查调试完成！")
        
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_network_check()