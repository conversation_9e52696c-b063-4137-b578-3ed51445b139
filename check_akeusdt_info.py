#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from binance_trader import BinanceTrader
import datetime

def load_config():
    """加载配置文件"""
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 确保network_params存在
        if 'network_params' not in config:
            config['network_params'] = {
                'api_key': config.get('api_key', ''),
                'api_secret': config.get('api_secret', ''),
                'base_url': config.get('base_url', 'https://fapi.binance.com'),
                'max_rate': config.get('max_rate', 15),
                'trade_rate': config.get('trade_rate', 5),
                'proxy': config.get('proxy', {})
            }
        else:
            # 确保network_params中有必要的字段
            if 'api_key' not in config['network_params']:
                config['network_params']['api_key'] = config.get('api_key', '')
            if 'api_secret' not in config['network_params']:
                config['network_params']['api_secret'] = config.get('api_secret', '')
            if 'base_url' not in config['network_params']:
                config['network_params']['base_url'] = config.get('base_url', 'https://fapi.binance.com')
            if 'max_rate' not in config['network_params']:
                config['network_params']['max_rate'] = config.get('max_rate', 15)
            if 'trade_rate' not in config['network_params']:
                config['network_params']['trade_rate'] = config.get('trade_rate', 5)
            if 'proxy' not in config['network_params']:
                config['network_params']['proxy'] = config.get('proxy', {})
        
        return config
    except Exception as e:
        print(f"加载配置失败: {e}")
        return None

def check_symbol_info(trader, symbol):
    """检查币种信息"""
    try:
        # 获取交易对信息
        exchange_info = trader.http.get('/fapi/v1/exchangeInfo')
        
        symbol_info = None
        for s in exchange_info.get('symbols', []):
            if s['symbol'] == symbol:
                symbol_info = s
                break
        
        if symbol_info:
            print(f"\n=== {symbol} 交易对信息 ===")
            print(f"状态: {symbol_info.get('status')}")
            print(f"基础资产: {symbol_info.get('baseAsset')}")
            print(f"报价资产: {symbol_info.get('quoteAsset')}")
            print(f"上市时间: {datetime.datetime.fromtimestamp(symbol_info.get('onboardDate', 0)/1000)}")
            print(f"交易权限: {symbol_info.get('permissions', [])}")
            
            # 检查过滤器
            filters = symbol_info.get('filters', [])
            for f in filters:
                if f['filterType'] == 'PRICE_FILTER':
                    print(f"价格过滤器: 最小价格={f.get('minPrice')}, 最大价格={f.get('maxPrice')}")
                elif f['filterType'] == 'LOT_SIZE':
                    print(f"数量过滤器: 最小数量={f.get('minQty')}, 最大数量={f.get('maxQty')}")
        else:
            print(f"{symbol} 未找到交易对信息")
            
        return symbol_info
        
    except Exception as e:
        print(f"获取交易对信息失败: {e}")
        return None

def check_klines_history(trader, symbol):
    """检查K线历史数据"""
    try:
        print(f"\n=== {symbol} K线历史数据检查 ===")
        
        # 测试不同的limit值
        for limit in [5, 10, 20, 50, 100, 200]:
            try:
                klines = trader.http.get('/fapi/v1/klines', {
                    'symbol': symbol,
                    'interval': '15m',
                    'limit': limit
                })
                
                if klines:
                    actual_count = len(klines)
                    print(f"请求 {limit} 条，实际获得 {actual_count} 条")
                    
                    if actual_count > 0:
                        # 显示第一条和最后一条的时间
                        first_time = datetime.datetime.fromtimestamp(klines[0][0]/1000)
                        last_time = datetime.datetime.fromtimestamp(klines[-1][0]/1000)
                        print(f"  时间范围: {first_time} 到 {last_time}")
                else:
                    print(f"请求 {limit} 条，返回空数据")
                    
            except Exception as e:
                print(f"请求 {limit} 条K线失败: {e}")
        
        # 测试不同时间周期
        print(f"\n=== {symbol} 不同时间周期测试 ===")
        for interval in ['1m', '5m', '15m', '1h', '4h', '1d']:
            try:
                klines = trader.http.get('/fapi/v1/klines', {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': 20
                })
                
                if klines:
                    print(f"{interval}: {len(klines)} 条数据")
                else:
                    print(f"{interval}: 无数据")
                    
            except Exception as e:
                print(f"{interval}: 获取失败 - {e}")
                
    except Exception as e:
        print(f"检查K线历史数据失败: {e}")

def main():
    print("=== AKEUSDT 币种信息检查 ===")
    
    # 加载配置
    config = load_config()
    if not config:
        return
    
    try:
        # 初始化交易器
        trader = BinanceTrader(config)
        print(f"成功初始化BinanceTrader，代理设置: {config['network_params'].get('proxy', {})}")
        
        # 检查AKEUSDT信息
        symbol_info = check_symbol_info(trader, 'AKEUSDT')
        
        # 检查K线历史数据
        check_klines_history(trader, 'AKEUSDT')
        
        # 对比其他正常币种
        print(f"\n=== 对比正常币种 BTCUSDT ===")
        check_klines_history(trader, 'BTCUSDT')
        
    except Exception as e:
        print(f"检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()