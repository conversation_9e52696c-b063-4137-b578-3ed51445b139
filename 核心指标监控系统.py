#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心指标监控系统
实时监控策略关键指标：订单成功率、持仓管理、末位淘汰执行频率等
"""

import time
import json
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import pandas as pd

@dataclass
class OrderMetrics:
    """订单指标"""
    total_orders: int = 0
    successful_orders: int = 0
    failed_orders: int = 0
    cancelled_orders: int = 0
    success_rate: float = 0.0
    avg_execution_time: float = 0.0
    total_execution_time: float = 0.0

@dataclass
class PositionMetrics:
    """持仓指标"""
    total_positions: int = 0
    active_positions: int = 0
    closed_positions: int = 0
    profitable_positions: int = 0
    losing_positions: int = 0
    win_rate: float = 0.0
    avg_holding_time: float = 0.0
    total_pnl: float = 0.0
    avg_pnl: float = 0.0

@dataclass
class EliminationMetrics:
    """末位淘汰指标"""
    total_eliminations: int = 0
    elimination_frequency: float = 0.0  # 每小时淘汰次数
    eliminated_symbols: List[str] = None
    avg_elimination_interval: float = 0.0
    last_elimination_time: Optional[datetime] = None

@dataclass
class SystemMetrics:
    """系统指标"""
    uptime: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    network_latency: float = 0.0
    api_call_count: int = 0
    api_error_count: int = 0
    api_success_rate: float = 0.0

@dataclass
class PerformanceMetrics:
    """性能指标"""
    total_return: float = 0.0
    daily_return: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_trade_duration: float = 0.0

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化指标收集器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 指标存储
        self.order_metrics = OrderMetrics()
        self.position_metrics = PositionMetrics()
        self.elimination_metrics = EliminationMetrics(eliminated_symbols=[])
        self.system_metrics = SystemMetrics()
        self.performance_metrics = PerformanceMetrics()
        
        # 历史数据存储
        self.order_history = deque(maxlen=1000)
        self.position_history = deque(maxlen=1000)
        self.elimination_history = deque(maxlen=1000)
        self.performance_history = deque(maxlen=1000)
        
        # 时间窗口数据
        self.hourly_data = defaultdict(list)
        self.daily_data = defaultdict(list)
        
        # 监控配置
        self.monitoring_interval = self.config.get('monitoring_interval', 60)  # 秒
        self.alert_thresholds = self.config.get('alert_thresholds', {
            'order_success_rate': 0.9,
            'api_success_rate': 0.95,
            'max_drawdown': 0.1,
            'elimination_frequency': 5.0  # 每小时最大淘汰次数
        })
        
        # 告警去重机制
        self.last_alerts = {}  # 存储上次告警的时间和内容
        self.alert_cooldown = 300  # 告警冷却时间（秒），5分钟内不重复相同告警
        
        # 运行状态
        self.running = False
        self.start_time = time.time()
        self.last_update_time = time.time()
        
        # 线程锁
        self.lock = threading.Lock()
    
    def start_monitoring(self):
        """开始监控"""
        self.running = True
        self.start_time = time.time()
        self.logger.info("核心指标监控系统启动")
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        self.logger.info("核心指标监控系统停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                self._update_system_metrics()
                self._check_alerts()
                self._cleanup_old_data()
                time.sleep(self.monitoring_interval)
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(5)
    
    def record_order(self, order_data: Dict[str, Any]):
        """记录订单数据"""
        with self.lock:
            try:
                order_info = {
                    'timestamp': datetime.now(),
                    'symbol': order_data.get('symbol'),
                    'side': order_data.get('side'),
                    'type': order_data.get('type'),
                    'status': order_data.get('status'),
                    'execution_time': order_data.get('execution_time', 0),
                    'error': order_data.get('error')
                }
                
                self.order_history.append(order_info)
                
                # 更新订单指标
                self.order_metrics.total_orders += 1
                
                if order_info['status'] == 'FILLED':
                    self.order_metrics.successful_orders += 1
                    self.order_metrics.total_execution_time += order_info['execution_time']
                elif order_info['status'] == 'CANCELLED':
                    self.order_metrics.cancelled_orders += 1
                else:
                    self.order_metrics.failed_orders += 1
                
                # 计算成功率
                if self.order_metrics.total_orders > 0:
                    self.order_metrics.success_rate = (
                        self.order_metrics.successful_orders / self.order_metrics.total_orders
                    )
                
                # 计算平均执行时间
                if self.order_metrics.successful_orders > 0:
                    self.order_metrics.avg_execution_time = (
                        self.order_metrics.total_execution_time / self.order_metrics.successful_orders
                    )
                
                self.logger.debug(f"记录订单: {order_info['symbol']} {order_info['status']}")
                
            except Exception as e:
                self.logger.error(f"记录订单数据失败: {e}")
    
    def record_position(self, position_data: Dict[str, Any]):
        """记录持仓数据"""
        with self.lock:
            try:
                position_info = {
                    'timestamp': datetime.now(),
                    'symbol': position_data.get('symbol'),
                    'side': position_data.get('side'),
                    'size': position_data.get('size', 0),
                    'entry_price': position_data.get('entry_price', 0),
                    'exit_price': position_data.get('exit_price'),
                    'pnl': position_data.get('pnl', 0),
                    'holding_time': position_data.get('holding_time', 0),
                    'status': position_data.get('status', 'open')
                }
                
                self.position_history.append(position_info)
                
                # 更新持仓指标
                if position_info['status'] == 'open':
                    self.position_metrics.active_positions += 1
                elif position_info['status'] == 'closed':
                    self.position_metrics.closed_positions += 1
                    self.position_metrics.total_pnl += position_info['pnl']
                    
                    if position_info['pnl'] > 0:
                        self.position_metrics.profitable_positions += 1
                    else:
                        self.position_metrics.losing_positions += 1
                
                self.position_metrics.total_positions = (
                    self.position_metrics.active_positions + self.position_metrics.closed_positions
                )
                
                # 计算胜率
                total_closed = self.position_metrics.closed_positions
                if total_closed > 0:
                    self.position_metrics.win_rate = (
                        self.position_metrics.profitable_positions / total_closed
                    )
                    self.position_metrics.avg_pnl = (
                        self.position_metrics.total_pnl / total_closed
                    )
                
                self.logger.debug(f"记录持仓: {position_info['symbol']} {position_info['status']}")
                
            except Exception as e:
                self.logger.error(f"记录持仓数据失败: {e}")
    
    def record_elimination(self, elimination_data: Dict[str, Any]):
        """记录末位淘汰数据"""
        with self.lock:
            try:
                elimination_info = {
                    'timestamp': datetime.now(),
                    'symbol': elimination_data.get('symbol'),
                    'reason': elimination_data.get('reason', 'rank_elimination'),
                    'rank': elimination_data.get('rank'),
                    'score': elimination_data.get('score')
                }
                
                self.elimination_history.append(elimination_info)
                
                # 更新末位淘汰指标
                self.elimination_metrics.total_eliminations += 1
                self.elimination_metrics.last_elimination_time = elimination_info['timestamp']
                
                if elimination_info['symbol'] not in self.elimination_metrics.eliminated_symbols:
                    self.elimination_metrics.eliminated_symbols.append(elimination_info['symbol'])
                
                # 计算淘汰频率（每小时）
                current_time = datetime.now()
                one_hour_ago = current_time - timedelta(hours=1)
                recent_eliminations = [
                    e for e in self.elimination_history 
                    if e['timestamp'] >= one_hour_ago
                ]
                self.elimination_metrics.elimination_frequency = len(recent_eliminations)
                
                # 计算平均淘汰间隔
                if len(self.elimination_history) >= 2:
                    intervals = []
                    for i in range(1, len(self.elimination_history)):
                        interval = (
                            self.elimination_history[i]['timestamp'] - 
                            self.elimination_history[i-1]['timestamp']
                        ).total_seconds() / 60  # 分钟
                        intervals.append(interval)
                    
                    self.elimination_metrics.avg_elimination_interval = sum(intervals) / len(intervals)
                
                self.logger.info(f"记录末位淘汰: {elimination_info['symbol']}")
                
            except Exception as e:
                self.logger.error(f"记录末位淘汰数据失败: {e}")
    
    def record_api_call(self, success: bool, response_time: float = 0):
        """记录API调用"""
        with self.lock:
            self.system_metrics.api_call_count += 1
            if not success:
                self.system_metrics.api_error_count += 1
            
            # 计算API成功率
            if self.system_metrics.api_call_count > 0:
                self.system_metrics.api_success_rate = (
                    (self.system_metrics.api_call_count - self.system_metrics.api_error_count) /
                    self.system_metrics.api_call_count
                )
            
            # 更新网络延迟
            if success and response_time > 0:
                self.system_metrics.network_latency = response_time
    
    def _update_system_metrics(self):
        """更新系统指标"""
        try:
            import psutil
            
            # 系统运行时间
            self.system_metrics.uptime = time.time() - self.start_time
            
            # CPU和内存使用率
            self.system_metrics.cpu_usage = psutil.cpu_percent()
            self.system_metrics.memory_usage = psutil.virtual_memory().percent
            
        except ImportError:
            # 如果没有psutil，使用基础指标
            self.system_metrics.uptime = time.time() - self.start_time
    
    def update_system_metrics(self):
        """公共方法：更新系统指标"""
        self._update_system_metrics()
    
    def _check_alerts(self):
        """检查告警条件"""
        alerts = []
        current_time = time.time()
        
        # 检查订单成功率 - 增加更严格的条件，避免无意义告警
        if (self.order_metrics.success_rate < self.alert_thresholds['order_success_rate'] and 
            self.order_metrics.total_orders >= 50 and  # 提高最小订单数量要求
            self.order_metrics.success_rate > 0):  # 避免0成功率的无意义告警
            alert_msg = f"订单成功率过低: {self.order_metrics.success_rate:.2%} (总订单: {self.order_metrics.total_orders})"
            alerts.append(alert_msg)
        
        # 检查API成功率
        if (self.system_metrics.api_success_rate < self.alert_thresholds['api_success_rate'] and 
            self.system_metrics.api_call_count >= 10):
            alert_msg = f"API成功率过低: {self.system_metrics.api_success_rate:.2%}"
            alerts.append(alert_msg)
        
        # 检查末位淘汰频率
        if self.elimination_metrics.elimination_frequency > self.alert_thresholds['elimination_frequency']:
            alert_msg = f"末位淘汰频率过高: {self.elimination_metrics.elimination_frequency:.1f}/小时"
            alerts.append(alert_msg)
        
        # 发送告警（带去重机制）
        for alert in alerts:
            # 检查是否在冷却期内
            if alert in self.last_alerts:
                last_alert_time = self.last_alerts[alert]
                if current_time - last_alert_time < self.alert_cooldown:
                    continue  # 跳过重复告警
            
            # 记录告警时间并发送
            self.last_alerts[alert] = current_time
            self.logger.warning(f"监控告警: {alert}")
        
        # 清理过期的告警记录（超过24小时）
        expired_alerts = []
        for alert_msg, alert_time in self.last_alerts.items():
            if current_time - alert_time > 86400:  # 24小时
                expired_alerts.append(alert_msg)
        
        for expired_alert in expired_alerts:
            del self.last_alerts[expired_alert]
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 清理超过24小时的历史数据
            cutoff_time = datetime.now() - timedelta(hours=24)
            
            # 清理订单历史
            self.order_history = deque([
                order for order in self.order_history 
                if order['timestamp'] >= cutoff_time
            ], maxlen=1000)
            
            # 清理持仓历史
            self.position_history = deque([
                position for position in self.position_history 
                if position['timestamp'] >= cutoff_time
            ], maxlen=1000)
            
            # 清理淘汰历史
            self.elimination_history = deque([
                elimination for elimination in self.elimination_history 
                if elimination['timestamp'] >= cutoff_time
            ], maxlen=1000)
            
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        with self.lock:
            return {
                'timestamp': datetime.now().isoformat(),
                'order_metrics': asdict(self.order_metrics),
                'position_metrics': asdict(self.position_metrics),
                'elimination_metrics': {
                    **asdict(self.elimination_metrics),
                    'last_elimination_time': (
                        self.elimination_metrics.last_elimination_time.isoformat()
                        if self.elimination_metrics.last_elimination_time else None
                    )
                },
                'system_metrics': asdict(self.system_metrics),
                'performance_metrics': asdict(self.performance_metrics)
            }
    
    def get_metrics_summary(self) -> str:
        """获取指标摘要"""
        metrics = self.get_current_metrics()
        
        summary = f"""
=== 核心指标监控摘要 ===
更新时间: {metrics['timestamp']}

【订单指标】
- 总订单数: {metrics['order_metrics']['total_orders']}
- 成功率: {metrics['order_metrics']['success_rate']:.2%}
- 平均执行时间: {metrics['order_metrics']['avg_execution_time']:.2f}秒

【持仓指标】
- 活跃持仓: {metrics['position_metrics']['active_positions']}
- 已平仓: {metrics['position_metrics']['closed_positions']}
- 胜率: {metrics['position_metrics']['win_rate']:.2%}
- 总盈亏: {metrics['position_metrics']['total_pnl']:.2f}

【末位淘汰指标】
- 总淘汰次数: {metrics['elimination_metrics']['total_eliminations']}
- 淘汰频率: {metrics['elimination_metrics']['elimination_frequency']:.1f}/小时
- 平均间隔: {metrics['elimination_metrics']['avg_elimination_interval']:.1f}分钟

【系统指标】
- 运行时间: {metrics['system_metrics']['uptime']:.0f}秒
- API成功率: {metrics['system_metrics']['api_success_rate']:.2%}
- API调用数: {metrics['system_metrics']['api_call_count']}
"""
        return summary
    
    def export_metrics(self, filepath: str):
        """导出指标数据"""
        try:
            metrics_data = {
                'export_time': datetime.now().isoformat(),
                'current_metrics': self.get_current_metrics(),
                'order_history': [
                    {**order, 'timestamp': order['timestamp'].isoformat()}
                    for order in list(self.order_history)
                ],
                'position_history': [
                    {**position, 'timestamp': position['timestamp'].isoformat()}
                    for position in list(self.position_history)
                ],
                'elimination_history': [
                    {**elimination, 'timestamp': elimination['timestamp'].isoformat()}
                    for elimination in list(self.elimination_history)
                ]
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"指标数据已导出到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"导出指标数据失败: {e}")
    
    def generate_report(self) -> Dict[str, Any]:
        """生成监控报告"""
        current_time = datetime.now()
        
        # 计算时间段统计
        one_hour_ago = current_time - timedelta(hours=1)
        one_day_ago = current_time - timedelta(days=1)
        
        # 最近1小时统计
        recent_orders = [o for o in self.order_history if o['timestamp'] >= one_hour_ago]
        recent_eliminations = [e for e in self.elimination_history if e['timestamp'] >= one_hour_ago]
        
        # 最近24小时统计
        daily_orders = [o for o in self.order_history if o['timestamp'] >= one_day_ago]
        daily_positions = [p for p in self.position_history if p['timestamp'] >= one_day_ago]
        
        report = {
            'report_time': current_time.isoformat(),
            'system_uptime': time.time() - self.start_time,
            'recent_1h': {
                'orders': len(recent_orders),
                'successful_orders': len([o for o in recent_orders if o['status'] == 'FILLED']),
                'eliminations': len(recent_eliminations)
            },
            'recent_24h': {
                'orders': len(daily_orders),
                'positions_opened': len([p for p in daily_positions if p['status'] == 'open']),
                'positions_closed': len([p for p in daily_positions if p['status'] == 'closed']),
                'total_pnl': sum(p['pnl'] for p in daily_positions if p['status'] == 'closed')
            },
            'current_status': self.get_current_metrics(),
            'alerts': self._get_current_alerts()
        }
        
        return report
    
    def check_alerts(self) -> List[str]:
        """检查并返回当前告警列表"""
        alerts = []
        
        # 检查订单成功率 - 增加更严格的条件，避免无意义告警
        if (self.order_metrics.success_rate < self.alert_thresholds['order_success_rate'] and 
            self.order_metrics.total_orders >= 50 and  # 提高最小订单数量要求
            self.order_metrics.success_rate > 0):  # 避免0成功率的无意义告警
            alerts.append(f"订单成功率过低: {self.order_metrics.success_rate:.2%} (总订单: {self.order_metrics.total_orders})")
        
        # 检查API成功率
        if (self.system_metrics.api_success_rate < self.alert_thresholds['api_success_rate'] and 
            self.system_metrics.api_call_count >= 10):
            alerts.append(f"API成功率过低: {self.system_metrics.api_success_rate:.2%}")
        
        # 检查末位淘汰频率
        if self.elimination_metrics.elimination_frequency > self.alert_thresholds['elimination_frequency']:
            alerts.append(f"末位淘汰频率过高: {self.elimination_metrics.elimination_frequency:.1f}/小时")
        
        return alerts
    
    def _get_current_alerts(self) -> List[str]:
        """获取当前告警（内部使用）"""
        return self.check_alerts()


# 全局监控实例
_metrics_collector = None

def get_metrics_collector() -> MetricsCollector:
    """获取全局监控实例"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector

def init_metrics_collector(config: Dict[str, Any] = None) -> MetricsCollector:
    """初始化全局监控实例"""
    global _metrics_collector
    _metrics_collector = MetricsCollector(config)
    return _metrics_collector