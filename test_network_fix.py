#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接测试脚本
测试币安API连接和代理配置
"""

import requests
import time
import json
from binance_trader import BinanceTrader
from config_manager import ConfigManager

def test_direct_connection():
    """测试直连币安API"""
    print("=== 测试直连币安API ===")
    try:
        response = requests.get(
            "https://fapi.binance.com/fapi/v1/ping",
            timeout=10
        )
        print(f"✓ 直连成功，状态码: {response.status_code}")
        return True
    except Exception as e:
        print(f"✗ 直连失败: {e}")
        return False

def test_proxy_connection():
    """测试代理连接币安API"""
    print("\n=== 测试代理连接币安API ===")
    try:
        proxies = {
            'http': 'http://127.0.0.1:7897',
            'https': 'http://127.0.0.1:7897'
        }
        response = requests.get(
            "https://fapi.binance.com/fapi/v1/ping",
            proxies=proxies,
            timeout=10
        )
        print(f"✓ 代理连接成功，状态码: {response.status_code}")
        return True
    except Exception as e:
        print(f"✗ 代理连接失败: {e}")
        return False

def test_binance_trader_klines():
    """测试BinanceTrader的K线获取"""
    print("\n=== 测试BinanceTrader K线获取 ===")
    try:
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.get_config('main')
        
        # 初始化交易器
        trader = BinanceTrader(config)
        
        # 测试符号列表
        test_symbols = ['BTCUSDT', 'SSVUSDT', 'BCHUSDT', 'PUMPUSDT', 'ALPINEUSDT']
        
        for symbol in test_symbols:
            print(f"\n测试 {symbol}:")
            try:
                # 获取K线数据
                klines = trader.get_klines(symbol, '15m', 200)
                if klines and len(klines) > 0:
                    print(f"  ✓ 成功获取 {len(klines)} 条K线数据")
                    # 显示第一条和最后一条数据的时间戳
                    first_time = klines[0][0] / 1000
                    last_time = klines[-1][0] / 1000
                    print(f"  ✓ 时间范围: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(first_time))} 到 {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_time))}")
                else:
                    print(f"  ✗ 未获取到K线数据")
            except Exception as e:
                print(f"  ✗ 获取失败: {e}")
                
    except Exception as e:
        print(f"✗ BinanceTrader初始化失败: {e}")

def test_api_with_different_limits():
    """测试不同limit参数的API调用"""
    print("\n=== 测试不同limit参数 ===")
    try:
        config_manager = ConfigManager()
        config = config_manager.get_config('main')
        
        trader = BinanceTrader(config)
        
        symbol = 'SSVUSDT'
        limits = [1, 10, 50, 100, 200]
        
        for limit in limits:
            try:
                klines = trader.get_klines(symbol, '15m', limit)
                if klines:
                    print(f"  limit={limit}: 获取到 {len(klines)} 条K线")
                else:
                    print(f"  limit={limit}: 未获取到数据")
            except Exception as e:
                print(f"  limit={limit}: 失败 - {e}")
                
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def main():
    print("开始网络连接诊断...")
    
    # 测试直连
    direct_ok = test_direct_connection()
    
    # 测试代理
    proxy_ok = test_proxy_connection()
    
    # 测试BinanceTrader
    test_binance_trader_klines()
    
    # 测试不同limit参数
    test_api_with_different_limits()
    
    print("\n=== 诊断总结 ===")
    print(f"直连状态: {'✓ 正常' if direct_ok else '✗ 异常'}")
    print(f"代理状态: {'✓ 正常' if proxy_ok else '✗ 异常'}")
    
    if not direct_ok and proxy_ok:
        print("建议: 网络需要通过代理访问，确保代理服务正常运行")
    elif direct_ok and not proxy_ok:
        print("建议: 可以考虑禁用代理，使用直连")
    elif not direct_ok and not proxy_ok:
        print("建议: 检查网络连接和代理服务状态")
    else:
        print("网络连接正常，问题可能在其他地方")

if __name__ == "__main__":
    main()