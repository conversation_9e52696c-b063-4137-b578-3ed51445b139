import pandas as pd
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class SymbolScorer:
    def __init__(self, config: dict):
        self.config = config
        self.weights = {
            'price_change': 0.3,
            'volume': 0.25,
            'volatility': 0.2,
            'momentum': 0.15,
            'depth': 0.1
        }
    
    def calculate_score(self, symbol_data: Dict[str, Any]) -> float:
        """计算交易对评分"""
        try:
            scores = {}
            
            # 价格变化评分 (30%)
            price_change = symbol_data.get('price_change', 0)
            scores['price_change'] = self._normalize_score(price_change, 0, 10)
            
            # 成交量评分 (25%)
            volume = symbol_data.get('volume', 0)
            scores['volume'] = self._normalize_score(volume, 0, 1000000)
            
            # 波动率评分 (20%)
            volatility = symbol_data.get('volatility', 0)
            scores['volatility'] = self._normalize_score(volatility, 0, 0.1)
            
            # 动量评分 (15%)
            momentum = symbol_data.get('momentum', 0)
            scores['momentum'] = self._normalize_score(momentum, -1, 1)
            
            # 深度评分 (10%)
            depth = symbol_data.get('depth', 0)
            scores['depth'] = self._normalize_score(depth, 0, 100000)
            
            # 加权计算总分
            total_score = sum(scores[key] * self.weights[key] for key in scores)
            return max(0, min(10, total_score))  # 限制在0-10之间
            
        except Exception as e:
            logger.error(f"计算评分失败: {e}")
            return 0.0
    
    def _normalize_score(self, value: float, min_val: float, max_val: float) -> float:
        """将值标准化到0-10范围"""
        if max_val == min_val:
            return 5.0
        normalized = (value - min_val) / (max_val - min_val) * 10
        return max(0, min(10, normalized))
    
    def rank_symbols(self, symbols_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对交易对进行排名"""
        try:
            # 计算每个交易对的评分
            for symbol_data in symbols_data:
                symbol_data['score'] = self.calculate_score(symbol_data)
            
            # 按评分排序
            ranked_symbols = sorted(symbols_data, key=lambda x: x['score'], reverse=True)
            return ranked_symbols
            
        except Exception as e:
            logger.error(f"交易对排名失败: {e}")
            return symbols_data