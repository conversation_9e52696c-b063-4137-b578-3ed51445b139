#!/usr/bin/env python3
"""
K线数据获取修复效果验证脚本
"""

import sys
import json
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from binance_trader import BinanceTrader
from strategy.maker_channel import MakerChannelStrategy

def test_klines_fix():
    """测试K线数据获取修复效果"""
    
    # 加载配置
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return False
    
    # 添加缺失的配置项
    if 'first_nominal' not in config:
        config['first_nominal'] = 100  # 默认值
    if 'max_nominal' not in config:
        config['max_nominal'] = 1000  # 默认值
    
    # 初始化交易器
    trader = BinanceTrader(config)
    
    # 初始化策略
    strategy = MakerChannelStrategy(trader, config)
    
    # 测试有问题的币种
    test_symbols = ['BCHUSDT', 'ALPINEUSDT', 'SSVUSDT', 'PORT3USDT', 'GASUSDT']
    
    print("开始测试K线数据获取修复效果...")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_symbols)
    
    for symbol in test_symbols:
        print(f"\n测试 {symbol}:")
        
        try:
            # 获取K线数据
            df = strategy.get_klines(symbol, '15m', 50)
            
            if df is not None and len(df) >= 20:
                print(f"  ✓ 成功获取 {len(df)} 条K线数据")
                print(f"  ✓ 数据时间范围: {df.index[0]} 到 {df.index[-1]}")
                success_count += 1
            else:
                print(f"  ✗ 获取失败或数据不足: {len(df) if df is not None else 0} 条")
                
        except Exception as e:
            print(f"  ✗ 异常: {e}")
        
        # 短暂延迟避免API限制
        time.sleep(0.5)
    
    print("\n" + "=" * 60)
    print(f"测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！K线数据获取修复成功")
        return True
    else:
        print("⚠️  部分测试失败，可能需要进一步调试")
        return False

def test_cache_behavior():
    """测试缓存行为"""
    print("\n测试缓存行为...")
    
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return False
    
    # 添加缺失的配置项
    if 'first_nominal' not in config:
        config['first_nominal'] = 100  # 默认值
    if 'max_nominal' not in config:
        config['max_nominal'] = 1000  # 默认值
    
    trader = BinanceTrader(config)
    strategy = MakerChannelStrategy(trader, config)
    
    symbol = 'BTCUSDT'
    
    print(f"第一次获取 {symbol} K线数据...")
    start_time = time.time()
    df1 = strategy.get_klines(symbol, '15m', 50)
    first_time = time.time() - start_time
    
    print(f"第二次获取 {symbol} K线数据（应该使用缓存）...")
    start_time = time.time()
    df2 = strategy.get_klines(symbol, '15m', 50)
    second_time = time.time() - start_time
    
    if df1 is not None and df2 is not None:
        print(f"  第一次耗时: {first_time:.3f}s")
        print(f"  第二次耗时: {second_time:.3f}s")
        
        if second_time < first_time * 0.5:  # 缓存应该显著更快
            print("  ✓ 缓存工作正常")
            return True
        else:
            print("  ⚠️  缓存可能未生效")
            return False
    else:
        print("  ✗ 数据获取失败")
        return False

if __name__ == "__main__":
    print("K线数据获取修复验证")
    print("=" * 60)
    
    # 测试修复效果
    fix_success = test_klines_fix()
    
    # 测试缓存行为
    cache_success = test_cache_behavior()
    
    print("\n" + "=" * 60)
    print("总体测试结果:")
    print(f"  修复效果: {'✓ 通过' if fix_success else '✗ 失败'}")
    print(f"  缓存行为: {'✓ 正常' if cache_success else '✗ 异常'}")
    
    if fix_success and cache_success:
        print("\n🎉 所有测试通过！修复成功")
    else:
        print("\n⚠️  存在问题，需要进一步调试")