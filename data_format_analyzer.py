#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据格式分析脚本
深入分析实时系统与增量脚本的数据格式差异
"""

import pickle
import json
import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging

class DataFormatAnalyzer:
    """数据格式分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = logging.getLogger('data_format_analyzer')
        self.logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        self.cache_dir = 'cache'
    
    def analyze_candidate_cache_structure(self):
        """分析候选池缓存结构"""
        cache_file = os.path.join(self.cache_dir, 'candidates.pkl')
        if not os.path.exists(cache_file):
            self.logger.error(f"候选池缓存文件不存在: {cache_file}")
            return
        
        try:
            with open(cache_file, 'rb') as f:
                candidates = pickle.load(f)
            
            self.logger.info(f"候选池数据类型: {type(candidates)}")
            self.logger.info(f"候选池大小: {len(candidates)}")
            
            if isinstance(candidates, list):
                self.logger.info("候选池是列表格式")
                if candidates:
                    self.logger.info(f"第一个元素类型: {type(candidates[0])}")
                    self.logger.info(f"第一个元素内容: {candidates[0]}")
                    self.logger.info(f"前5个元素: {candidates[:5]}")
            elif isinstance(candidates, dict):
                self.logger.info("候选池是字典格式")
                keys = list(candidates.keys())[:5]
                self.logger.info(f"前5个键: {keys}")
                if keys:
                    first_key = keys[0]
                    self.logger.info(f"第一个值类型: {type(candidates[first_key])}")
                    self.logger.info(f"第一个值内容: {candidates[first_key]}")
            
            return candidates
        except Exception as e:
            self.logger.error(f"分析候选池缓存失败: {e}")
            return None
    
    def analyze_symbols_data(self):
        """分析symbols.json数据结构"""
        symbols_file = os.path.join(self.cache_dir, 'symbols.json')
        if not os.path.exists(symbols_file):
            self.logger.warning(f"symbols.json文件不存在: {symbols_file}")
            return None
        
        try:
            with open(symbols_file, 'r') as f:
                symbols_data = json.load(f)
            
            self.logger.info(f"symbols数据类型: {type(symbols_data)}")
            self.logger.info(f"symbols数据大小: {len(symbols_data)}")
            
            if isinstance(symbols_data, list):
                self.logger.info("symbols是列表格式")
                if symbols_data:
                    self.logger.info(f"第一个元素: {symbols_data[0]}")
            elif isinstance(symbols_data, dict):
                self.logger.info("symbols是字典格式")
                keys = list(symbols_data.keys())[:5]
                self.logger.info(f"前5个键: {keys}")
                if keys:
                    first_key = keys[0]
                    self.logger.info(f"第一个值: {symbols_data[first_key]}")
            
            return symbols_data
        except Exception as e:
            self.logger.error(f"分析symbols数据失败: {e}")
            return None
    
    def analyze_depth_data_format(self, symbol):
        """分析深度数据格式"""
        depth_file = os.path.join(self.cache_dir, f'depth_{symbol}.json')
        if not os.path.exists(depth_file):
            self.logger.warning(f"深度数据文件不存在: {depth_file}")
            return None
        
        try:
            with open(depth_file, 'r') as f:
                depth_data = json.load(f)
            
            self.logger.info(f"{symbol} 深度数据类型: {type(depth_data)}")
            
            if isinstance(depth_data, dict):
                self.logger.info(f"{symbol} 深度数据键: {list(depth_data.keys())}")
                if 'bids' in depth_data:
                    self.logger.info(f"{symbol} bids类型: {type(depth_data['bids'])}")
                    if depth_data['bids']:
                        self.logger.info(f"{symbol} 第一个bid: {depth_data['bids'][0]}")
                if 'asks' in depth_data:
                    self.logger.info(f"{symbol} asks类型: {type(depth_data['asks'])}")
                    if depth_data['asks']:
                        self.logger.info(f"{symbol} 第一个ask: {depth_data['asks'][0]}")
            else:
                self.logger.info(f"{symbol} 深度数据内容: {depth_data}")
            
            return depth_data
        except Exception as e:
            self.logger.error(f"分析深度数据失败 {symbol}: {e}")
            return None
    
    def analyze_kline_data_format(self, symbol):
        """分析K线数据格式"""
        # 尝试多种K线数据文件格式
        possible_files = [
            os.path.join(self.cache_dir, f'klines_{symbol}_15m_200.pkl'),
            os.path.join(self.cache_dir, f'klines_{symbol}_15m_50.pkl'),
            os.path.join(self.cache_dir, f'klines_{symbol}_1d_200.pkl'),
            os.path.join(self.cache_dir, f'klines_{symbol}.json')
        ]
        
        for kline_file in possible_files:
            if os.path.exists(kline_file):
                try:
                    if kline_file.endswith('.pkl'):
                        # 加载pickle格式
                        df = pd.read_pickle(kline_file)
                        self.logger.info(f"{symbol} K线数据 (来源: {os.path.basename(kline_file)}):")
                        self.logger.info(f"  数据类型: {type(df)}")
                        self.logger.info(f"  数据形状: {df.shape}")
                        self.logger.info(f"  列名: {list(df.columns)}")
                        self.logger.info(f"  数据类型: {df.dtypes.to_dict()}")
                        self.logger.info(f"  前3行:\n{df.head(3)}")
                        return df
                    else:
                        # 加载JSON格式
                        with open(kline_file, 'r') as f:
                            klines = json.load(f)
                        
                        self.logger.info(f"{symbol} K线数据 (来源: {os.path.basename(kline_file)}):")
                        self.logger.info(f"  数据类型: {type(klines)}")
                        self.logger.info(f"  数据长度: {len(klines)}")
                        if klines:
                            self.logger.info(f"  第一行: {klines[0]}")
                        return klines
                except Exception as e:
                    self.logger.warning(f"分析K线数据失败 {kline_file}: {e}")
                    continue
        
        self.logger.warning(f"未找到 {symbol} 的K线数据文件")
        return None
    
    def comprehensive_data_analysis(self):
        """综合数据分析"""
        self.logger.info("开始综合数据分析...")
        
        # 1. 分析候选池结构
        self.logger.info("\n=== 候选池结构分析 ===")
        candidates = self.analyze_candidate_cache_structure()
        
        # 2. 分析symbols数据
        self.logger.info("\n=== Symbols数据分析 ===")
        symbols_data = self.analyze_symbols_data()
        
        # 3. 选择几个币种进行详细分析
        if candidates:
            if isinstance(candidates, list):
                test_symbols = candidates[:3]
            elif isinstance(candidates, dict):
                test_symbols = list(candidates.keys())[:3]
            else:
                test_symbols = []
            
            for symbol in test_symbols:
                self.logger.info(f"\n=== {symbol} 详细数据分析 ===")
                
                # 分析K线数据
                self.logger.info(f"\n--- {symbol} K线数据分析 ---")
                kline_data = self.analyze_kline_data_format(symbol)
                
                # 分析深度数据
                self.logger.info(f"\n--- {symbol} 深度数据分析 ---")
                depth_data = self.analyze_depth_data_format(symbol)
        
        # 4. 生成分析报告
        self.generate_data_analysis_report(candidates, symbols_data)
    
    def generate_data_analysis_report(self, candidates, symbols_data):
        """生成数据分析报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'data_format_analysis_report_{timestamp}.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("数据格式分析报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 候选池分析
            f.write("候选池数据结构:\n")
            f.write("-" * 40 + "\n")
            if candidates:
                f.write(f"数据类型: {type(candidates)}\n")
                f.write(f"数据大小: {len(candidates)}\n")
                if isinstance(candidates, list):
                    f.write("格式: 列表\n")
                    if candidates:
                        f.write(f"元素示例: {candidates[0]}\n")
                elif isinstance(candidates, dict):
                    f.write("格式: 字典\n")
                    keys = list(candidates.keys())[:5]
                    f.write(f"键示例: {keys}\n")
            else:
                f.write("无法加载候选池数据\n")
            
            f.write("\n")
            
            # Symbols数据分析
            f.write("Symbols数据结构:\n")
            f.write("-" * 40 + "\n")
            if symbols_data:
                f.write(f"数据类型: {type(symbols_data)}\n")
                f.write(f"数据大小: {len(symbols_data)}\n")
                if isinstance(symbols_data, list):
                    f.write("格式: 列表\n")
                elif isinstance(symbols_data, dict):
                    f.write("格式: 字典\n")
            else:
                f.write("无法加载symbols数据\n")
            
            f.write("\n关键发现:\n")
            f.write("-" * 40 + "\n")
            f.write("1. 候选池数据格式可能与预期不符\n")
            f.write("2. 深度数据可能存在格式问题\n")
            f.write("3. K线数据存在多种格式（pkl/json）\n")
            f.write("4. 需要统一数据格式处理逻辑\n")
        
        self.logger.info(f"数据分析报告已保存: {report_file}")

def main():
    """主函数"""
    analyzer = DataFormatAnalyzer()
    analyzer.comprehensive_data_analysis()

if __name__ == "__main__":
    main()