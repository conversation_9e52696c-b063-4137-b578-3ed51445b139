network_params:
  api_key: "nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s"
  api_secret: "HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u"
  base_url: "https://fapi.binance.com"
  max_rate: 15          # 查询类限速
  trade_rate: 10        # 交易类限速
  proxy:
    enabled: true
    host: "127.0.0.1"
    port: 7897

leverage: 3           # 固定3倍
first_nominal: 10     # 首单名义(USDT)，调整为适合当前余额
max_add: 2            # 最多加仓次数，减少加仓次数
add_ratio: [1.5, 2.0, 999]   # 20%/50%/100% 浮盈加仓倍数
maker_only: true      # 只做Maker
daily_close: "14:55"  # UTC 每日强制平仓
min_equity: 900       # 子账户<900U自动停机

# 移动止损配置
trailing_stop:
  enabled: false                    # 是否启用移动止损（默认关闭）
  trigger_profit_pct: 0.08         # 基础触发条件：浮盈8%激活移动止损
  
  # 常规行情设置
  normal_market:
    profit_step_pct: 0.10          # 每盈利10%触发一次移动
    stop_move_pct: 0.07            # 止损线上移7%
  
  # 极端行情设置  
  extreme_market:
    enabled: false                  # 是否启用极端行情模式
    profit_step_pct: 0.05          # 每盈利5%触发一次移动
    stop_move_pct: 0.04            # 止损线上移4%
    volatility_threshold: 0.15     # 波动率阈值，超过此值启用极端行情模式
  
  # 风控规则
  risk_control:
    only_move_up: true             # 强制规则：止损线只允许上移
    min_stop_distance_pct: 0.02   # 最小止损距离2%，防止过于激进
    max_trailing_count: 10         # 单笔持仓最大移动次数限制