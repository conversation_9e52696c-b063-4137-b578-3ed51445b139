"""
增强版动态通道突破逻辑测试
包含详细的调试信息，用于诊断突破检测问题
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加strategy目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy'))

from dynamic_tf_helper import dynamic_tf_for_channel, dynamic_tf_for_scoring

class MockLogger:
    def debug(self, msg):
        print(f"DEBUG: {msg}")
    
    def info(self, msg):
        print(f"INFO: {msg}")

class TestChannelBreakthroughDebug:
    def __init__(self):
        self.log = MockLogger()
    
    def check_channel_breakthrough_debug(self, df, age_days):
        """
        检查通道突破条件 - 带详细调试信息
        """
        print(f"\n=== 通道突破检测详情 ===")
        print(f"币龄: {age_days}天")
        
        if df.empty or len(df) < 10:
            print(f"数据不足: 数据长度={len(df)}, 需要至少10根K线")
            return False
        
        # 根据币龄动态计算通道周期
        n = dynamic_tf_for_channel(age_days)
        print(f"动态通道周期: {n}根K线")
        
        # 确保有足够的数据
        if len(df) < n:
            n = len(df) - 1
            if n < 2:
                print(f"调整后数据仍不足: n={n}")
                return False
            print(f"数据不足，调整通道周期为: {n}根K线")
        
        # 计算通道上下轨
        high_n = df['high'].rolling(window=n, min_periods=1).max()
        low_n = df['low'].rolling(window=n, min_periods=1).min()
        
        # 当前价格
        current_close = df['close'].iloc[-1]
        current_high = df['high'].iloc[-1]
        
        # 通道上轨
        upper_band = high_n.iloc[-1]
        
        print(f"当前收盘价: {current_close:.6f}")
        print(f"当前最高价: {current_high:.6f}")
        print(f"通道上轨: {upper_band:.6f}")
        
        # 检查各个条件
        close_near_upper = current_close >= upper_band * 0.999
        high_break_upper = current_high >= upper_band * 1.001
        
        print(f"收盘价接近上轨 (>= {upper_band * 0.999:.6f}): {close_near_upper}")
        print(f"最高价突破上轨 (>= {upper_band * 1.001:.6f}): {high_break_upper}")
        
        # 突破幅度限制：不超过10%
        breakthrough_ratio = (current_close / upper_band - 1) * 100
        valid_breakthrough = breakthrough_ratio <= 10
        
        print(f"突破幅度: {breakthrough_ratio:.2f}% (限制: <=10%): {valid_breakthrough}")
        
        # 允许5根K线的突破时间
        recent_closes = df['close'].iloc[-5:]
        recent_breakthrough_checks = [close >= upper_band * 0.999 for close in recent_closes]
        any_recent_breakthrough = any(recent_breakthrough_checks)
        
        print(f"最近5根K线收盘价: {[f'{c:.6f}' for c in recent_closes]}")
        print(f"最近5根K线突破检查: {recent_breakthrough_checks}")
        print(f"任意最近K线突破: {any_recent_breakthrough}")
        
        # 最终条件
        breakthrough_condition = (close_near_upper or high_break_upper) and valid_breakthrough and any_recent_breakthrough
        
        print(f"最终突破条件:")
        print(f"  (收盘价接近上轨 OR 最高价突破上轨): {close_near_upper or high_break_upper}")
        print(f"  AND 突破幅度有效: {valid_breakthrough}")
        print(f"  AND 最近有突破: {any_recent_breakthrough}")
        print(f"  = {breakthrough_condition}")
        
        return breakthrough_condition

def generate_breakthrough_data(age_days, length=50):
    """生成明确会突破的测试数据"""
    np.random.seed(42)
    
    # 基础价格
    base_price = 100.0
    
    # 生成基础上升趋势数据
    trend_factor = np.linspace(0, 0.15, length)
    noise = np.random.normal(0, 0.008, length)
    prices = base_price * (1 + trend_factor + noise)
    
    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.003)))
        low = price * (1 - abs(np.random.normal(0, 0.003)))
        open_price = price * (1 + np.random.normal(0, 0.001))
        close = price
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': np.random.uniform(1000, 10000)
        })
    
    df = pd.DataFrame(data)
    
    # 获取通道周期
    channel_period = dynamic_tf_for_channel(age_days)
    
    if len(df) >= channel_period:
        # 重要：使用与检测逻辑完全相同的方式计算通道上轨
        # 先计算所有数据的rolling max，然后取倒数第二个值作为参考上轨
        temp_high_rolling = df['high'].rolling(window=channel_period, min_periods=1).max()
        # 取倒数第二个值作为参考，因为最后一个值会包含我们即将修改的数据
        if len(temp_high_rolling) >= 2:
            reference_upper_band = temp_high_rolling.iloc[-2]
        else:
            reference_upper_band = temp_high_rolling.iloc[-1]
        
        print(f"制造突破数据:")
        print(f"  参考通道上轨: {reference_upper_band:.6f}")
        
        # 最后一根K线明显突破
        breakthrough_high = reference_upper_band * 1.008  # 突破0.8%
        breakthrough_close = reference_upper_band * 1.002  # 收盘价突破0.2%
        
        df.loc[df.index[-1], 'high'] = breakthrough_high
        df.loc[df.index[-1], 'close'] = breakthrough_close
        
        print(f"  设置最后K线最高价: {breakthrough_high:.6f}")
        print(f"  设置最后K线收盘价: {breakthrough_close:.6f}")
        
        # 最后几根K线也接近突破，确保满足"最近5根K线"条件
        for i in range(2, min(6, len(df))):
            idx = df.index[-i]
            # 设置为刚好满足突破条件
            near_breakthrough_close = reference_upper_band * 1.0005  # 略微突破0.05%
            df.loc[idx, 'close'] = near_breakthrough_close
            print(f"  设置倒数第{i}根K线收盘价: {near_breakthrough_close:.6f}")
    
    return df

def test_breakthrough_scenarios():
    """测试各种突破场景"""
    print("=== 测试突破场景 ===")
    
    tester = TestChannelBreakthroughDebug()
    
    test_ages = [0.5, 2, 15, 100]
    
    for age_days in test_ages:
        print(f"\n{'='*60}")
        print(f"测试币龄: {age_days}天")
        print(f"{'='*60}")
        
        # 生成突破数据
        df = generate_breakthrough_data(age_days, length=50)
        
        # 测试突破检测
        result = tester.check_channel_breakthrough_debug(df, age_days)
        
        print(f"\n最终结果: {'✓ 检测到突破' if result else '✗ 未检测到突破'}")

if __name__ == "__main__":
    print("增强版动态通道突破逻辑测试")
    print("=" * 60)
    
    test_breakthrough_scenarios()
    
    print("\n测试完成！")