import yaml
import logging
import warnings
import urllib3
import time
import asyncio
import pickle
import os
from pathlib import Path
from binance_trader import BinanceTrader
from strategy.maker_channel import MakerChannelStrategy
from cache_manager import CacheManager

# 完全禁用所有警告
warnings.filterwarnings("ignore")
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置控制台编码为UTF-8
import sys
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 配置详细日志格式（控制台+文件）
console_handler = logging.StreamHandler()
console_handler.setStream(sys.stdout)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler('strategy.log', encoding='utf-8'),
        console_handler
    ]
)

# 设置ReferenceFolder相关日志器的级别为DEBUG以显示评分详情
logging.getLogger('ReferenceFolder.symbol_scorer').setLevel(logging.DEBUG)

def _initialize_cand_cache(strategy, cache_manager):
    """初始化 cand_cache，如果不存在则从 candidates.pkl 创建"""
    cand_cache_path = Path('cache/cand_cache.pkl')
    candidates_path = Path('cache/candidates.pkl')
    
    # 检查 cand_cache.pkl 是否存在
    if cand_cache_path.exists():
        try:
            with open(cand_cache_path, 'rb') as f:
                cand_cache = pickle.load(f)
            strategy.cand_cache = cand_cache
            logging.info(f"成功加载现有 cand_cache.pkl，包含 {len(cand_cache)} 个币种")
            return
        except Exception as e:
            logging.warning(f"加载 cand_cache.pkl 失败: {e}，将重新创建")
    
    # 如果 cand_cache.pkl 不存在，尝试从 candidates.pkl 创建
    if candidates_path.exists():
        try:
            candidates = cache_manager.load_candidates()
            if candidates:
                # 将列表格式的 candidates 转换为字典格式的 cand_cache
                cand_cache = {}
                for symbol in candidates:
                    cand_cache[symbol] = {
                        'symbol': symbol,
                        'score': 0.0,  # 初始评分为0，后续会重新计算
                        'last_update': time.time(),
                        'fail_count': 0,
                        'price': 0.0,
                        'depth': 0.0,
                        'age': 0,
                        'M': 0.0
                    }
                
                strategy.cand_cache = cand_cache
                
                # 保存到 cand_cache.pkl
                os.makedirs('cache', exist_ok=True)
                with open(cand_cache_path, 'wb') as f:
                    pickle.dump(cand_cache, f)
                
                logging.info(f"从 candidates.pkl 创建 cand_cache.pkl，包含 {len(cand_cache)} 个币种")
                return
        except Exception as e:
            logging.error(f"从 candidates.pkl 创建 cand_cache 失败: {e}")
    
    # 如果都不存在，初始化为空字典
    strategy.cand_cache = {}
    logging.warning("未找到候选池数据，初始化为空的 cand_cache")

def _save_cand_cache_to_file(strategy):
    """保存策略的 cand_cache 到文件"""
    try:
        cache_path = Path("cache/cand_cache.pkl")
        cache_path.parent.mkdir(exist_ok=True)
        
        with open(cache_path, 'wb') as f:
            pickle.dump(strategy.cand_cache, f)
        
        logging.info(f"已保存 cand_cache 到文件，包含 {len(strategy.cand_cache)} 个币种")
        
    except Exception as e:
        logging.error(f"保存 cand_cache 失败: {e}")

def _sync_candidate_data(strategy, cache_manager):
    """同步候选池数据，确保 candidates.pkl 和 cand_cache.pkl 一致"""
    try:
        # 使用 CacheManager 的同步方法
        success = cache_manager.sync_candidates_and_cache(cand_cache=strategy.cand_cache)
        
        if success:
            logging.info(f"候选池数据同步成功，包含 {len(strategy.cand_cache)} 个币种")
        else:
            logging.warning("候选池数据同步失败")
            
    except Exception as e:
        logging.error(f"同步候选池数据时出错: {e}")

def main():
    """主函数"""
    # 加载基础配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    # 动态检测网络环境并更新代理配置
    from network_environment_detector import get_proxy_config
    
    try:
        # 获取智能代理配置
        dynamic_proxy_config = get_proxy_config()
        
        # 更新配置中的代理设置
        if 'network_params' not in cfg:
            cfg['network_params'] = {}
        
        cfg['network_params']['proxy'] = dynamic_proxy_config['proxy']
        
        logging.info(f"动态代理配置: {dynamic_proxy_config['proxy']}")
        logging.info(f"检测到网络环境: {dynamic_proxy_config['environment']}")
        logging.info(f"检测置信度: {dynamic_proxy_config['confidence']:.2f}")
        
    except Exception as e:
        logging.error(f"动态代理配置失败: {e}")
        logging.info("使用配置文件中的静态代理设置")
    
    # 初始化缓存管理器
    cache_manager = CacheManager()
    
    # 初始化交易器
    trader = BinanceTrader(cfg)
    
    # 执行冷启动：加载全币种基础信息
    logging.info("执行冷启动：加载全币种基础信息...")
    symbols_info = trader.get_all_futures()
    logging.info(f"冷启动完成，加载 {len(symbols_info)} 个交易对信息")
    
    # 初始化策略
    strategy = MakerChannelStrategy(trader, cfg)
    
    # 检查并初始化 cand_cache
    _initialize_cand_cache(strategy, cache_manager)
    
    # 执行启动预热扫描
    logging.info("执行启动预热扫描（涨幅榜前25 + 成交量榜前25）...")
    strategy.warmup()
    
    # 保存预热后的候选池到 cand_cache.pkl
    _save_cand_cache_to_file(strategy)
    
    # 同步候选池数据，确保 candidates.pkl 和 cand_cache.pkl 一致
    _sync_candidate_data(strategy, cache_manager)
    
    # 主循环使用完整的策略循环
    logging.info("启动策略主循环...")
    asyncio.run(strategy.loop())

if __name__ == '__main__':
    main()