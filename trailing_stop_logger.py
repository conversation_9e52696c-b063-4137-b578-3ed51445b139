#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
移动止损日志记录和监控模块
提供详细的移动止损操作日志记录、性能监控和统计分析功能
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class TrailingStopLogEntry:
    """移动止损日志条目"""
    timestamp: str
    symbol: str
    action: str  # 'activated', 'updated', 'triggered', 'disabled'
    old_stop_price: Optional[float]
    new_stop_price: Optional[float]
    current_price: float
    unrealized_pnl_pct: float
    market_mode: str  # 'normal', 'extreme'
    profit_step: float
    stop_move: float
    reason: str
    move_count: int
    highest_stop_price: float


@dataclass
class TrailingStopStats:
    """移动止损统计信息"""
    total_activations: int = 0
    total_updates: int = 0
    total_triggers: int = 0
    successful_protections: int = 0  # 成功保护利润次数
    average_move_distance: float = 0.0
    max_move_count: int = 0
    total_profit_protected: float = 0.0  # 总保护利润
    normal_mode_updates: int = 0
    extreme_mode_updates: int = 0


class TrailingStopLogger:
    """移动止损日志记录器"""
    
    def __init__(self, log_dir: str = "logs", logger: Optional[logging.Logger] = None):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.logger = logger or logging.getLogger(__name__)
        
        # 日志文件路径
        self.daily_log_file = self.log_dir / f"trailing_stop_{datetime.now().strftime('%Y%m%d')}.json"
        self.stats_file = self.log_dir / "trailing_stop_stats.json"
        
        # 内存中的统计信息
        self.stats = self._load_stats()
        self.session_logs: List[TrailingStopLogEntry] = []
        
    def _load_stats(self) -> TrailingStopStats:
        """加载统计信息"""
        try:
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return TrailingStopStats(**data)
        except Exception as e:
            self.logger.error(f"加载移动止损统计信息失败: {e}")
        
        return TrailingStopStats()
    
    def _save_stats(self):
        """保存统计信息"""
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.stats), f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存移动止损统计信息失败: {e}")
    
    def log_activation(self, symbol: str, entry_price: float, current_price: float, 
                      unrealized_pnl_pct: float, initial_stop_price: float):
        """记录移动止损激活"""
        entry = TrailingStopLogEntry(
            timestamp=datetime.now().isoformat(),
            symbol=symbol,
            action='activated',
            old_stop_price=None,
            new_stop_price=initial_stop_price,
            current_price=current_price,
            unrealized_pnl_pct=unrealized_pnl_pct,
            market_mode='normal',
            profit_step=0.0,
            stop_move=0.0,
            reason=f"浮盈达到激活条件 {unrealized_pnl_pct:.2%}",
            move_count=0,
            highest_stop_price=initial_stop_price
        )
        
        self._write_log_entry(entry)
        self.stats.total_activations += 1
        self._save_stats()
        
        self.logger.info(f"移动止损激活: {symbol} @ {initial_stop_price:.6f}, "
                        f"浮盈 {unrealized_pnl_pct:.2%}")
    
    def log_update(self, symbol: str, old_stop_price: float, new_stop_price: float,
                   current_price: float, unrealized_pnl_pct: float, market_mode: str,
                   profit_step: float, stop_move: float, move_count: int,
                   highest_stop_price: float, reason: str):
        """记录移动止损更新"""
        entry = TrailingStopLogEntry(
            timestamp=datetime.now().isoformat(),
            symbol=symbol,
            action='updated',
            old_stop_price=old_stop_price,
            new_stop_price=new_stop_price,
            current_price=current_price,
            unrealized_pnl_pct=unrealized_pnl_pct,
            market_mode=market_mode,
            profit_step=profit_step,
            stop_move=stop_move,
            reason=reason,
            move_count=move_count,
            highest_stop_price=highest_stop_price
        )
        
        self._write_log_entry(entry)
        self.stats.total_updates += 1
        
        # 更新统计信息
        move_distance = abs(new_stop_price - old_stop_price) / old_stop_price
        self.stats.average_move_distance = (
            (self.stats.average_move_distance * (self.stats.total_updates - 1) + move_distance) 
            / self.stats.total_updates
        )
        self.stats.max_move_count = max(self.stats.max_move_count, move_count)
        
        if market_mode == 'normal':
            self.stats.normal_mode_updates += 1
        else:
            self.stats.extreme_mode_updates += 1
        
        self._save_stats()
        
        self.logger.info(f"移动止损更新: {symbol} {old_stop_price:.6f} -> {new_stop_price:.6f}, "
                        f"模式: {market_mode}, 移动次数: {move_count}")
    
    def log_trigger(self, symbol: str, trigger_price: float, protected_profit: float,
                   final_pnl_pct: float, total_moves: int):
        """记录移动止损触发（平仓）"""
        entry = TrailingStopLogEntry(
            timestamp=datetime.now().isoformat(),
            symbol=symbol,
            action='triggered',
            old_stop_price=trigger_price,
            new_stop_price=None,
            current_price=trigger_price,
            unrealized_pnl_pct=final_pnl_pct,
            market_mode='triggered',
            profit_step=0.0,
            stop_move=0.0,
            reason=f"止损触发，保护利润 {protected_profit:.2%}",
            move_count=total_moves,
            highest_stop_price=trigger_price
        )
        
        self._write_log_entry(entry)
        self.stats.total_triggers += 1
        self.stats.successful_protections += 1
        self.stats.total_profit_protected += protected_profit
        self._save_stats()
        
        self.logger.info(f"移动止损触发: {symbol} @ {trigger_price:.6f}, "
                        f"保护利润 {protected_profit:.2%}, 总移动 {total_moves} 次")
    
    def log_disable(self, symbol: str, reason: str):
        """记录移动止损禁用"""
        entry = TrailingStopLogEntry(
            timestamp=datetime.now().isoformat(),
            symbol=symbol,
            action='disabled',
            old_stop_price=None,
            new_stop_price=None,
            current_price=0.0,
            unrealized_pnl_pct=0.0,
            market_mode='disabled',
            profit_step=0.0,
            stop_move=0.0,
            reason=reason,
            move_count=0,
            highest_stop_price=0.0
        )
        
        self._write_log_entry(entry)
        self.logger.info(f"移动止损禁用: {symbol}, 原因: {reason}")
    
    def _write_log_entry(self, entry: TrailingStopLogEntry):
        """写入日志条目"""
        try:
            # 添加到会话日志
            self.session_logs.append(entry)
            
            # 写入日志文件
            with open(self.daily_log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(asdict(entry), ensure_ascii=False) + '\n')
                
        except Exception as e:
            self.logger.error(f"写入移动止损日志失败: {e}")
    
    def get_daily_summary(self, date: Optional[str] = None) -> Dict[str, Any]:
        """获取日度摘要"""
        target_date = date or datetime.now().strftime('%Y%m%d')
        log_file = self.log_dir / f"trailing_stop_{target_date}.json"
        
        summary = {
            'date': target_date,
            'total_entries': 0,
            'activations': 0,
            'updates': 0,
            'triggers': 0,
            'symbols': set(),
            'avg_profit_protected': 0.0,
            'mode_distribution': {'normal': 0, 'extreme': 0}
        }
        
        try:
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        entry_data = json.loads(line.strip())
                        summary['total_entries'] += 1
                        summary['symbols'].add(entry_data['symbol'])
                        
                        action = entry_data['action']
                        if action == 'activated':
                            summary['activations'] += 1
                        elif action == 'updated':
                            summary['updates'] += 1
                            mode = entry_data['market_mode']
                            if mode in summary['mode_distribution']:
                                summary['mode_distribution'][mode] += 1
                        elif action == 'triggered':
                            summary['triggers'] += 1
                
                summary['symbols'] = list(summary['symbols'])
                
        except Exception as e:
            self.logger.error(f"生成日度摘要失败: {e}")
        
        return summary
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            'total_stats': asdict(self.stats),
            'success_rate': (
                self.stats.successful_protections / max(self.stats.total_activations, 1)
            ),
            'avg_moves_per_position': (
                self.stats.total_updates / max(self.stats.total_activations, 1)
            ),
            'mode_preference': {
                'normal_ratio': (
                    self.stats.normal_mode_updates / 
                    max(self.stats.total_updates, 1)
                ),
                'extreme_ratio': (
                    self.stats.extreme_mode_updates / 
                    max(self.stats.total_updates, 1)
                )
            }
        }
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """清理旧日志文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            for log_file in self.log_dir.glob("trailing_stop_*.json"):
                # 提取日期
                date_str = log_file.stem.split('_')[-1]
                try:
                    file_date = datetime.strptime(date_str, '%Y%m%d')
                    if file_date < cutoff_date:
                        log_file.unlink()
                        self.logger.info(f"删除旧日志文件: {log_file}")
                except ValueError:
                    continue
                    
        except Exception as e:
            self.logger.error(f"清理旧日志失败: {e}")