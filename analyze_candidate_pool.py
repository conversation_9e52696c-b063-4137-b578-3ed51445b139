#!/usr/bin/env python3
"""
分析候选池状态的脚本
"""
import pickle
import os
import sys

def analyze_candidate_pool():
    """分析候选池状态"""
    cache_file = 'cache/candidates.pkl'
    
    if not os.path.exists(cache_file):
        print(f"缓存文件 {cache_file} 不存在")
        return
    
    try:
        with open(cache_file, 'rb') as f:
            candidates = pickle.load(f)
        
        print(f"候选池缓存文件类型: {type(candidates)}")
        print(f"候选池总数: {len(candidates)}")
        
        if isinstance(candidates, dict):
            # 如果是字典格式
            print("\n前10个币种:")
            for i, (symbol, data) in enumerate(list(candidates.items())[:10]):
                score = data.get('score', 0) if isinstance(data, dict) else 0
                print(f"{i+1}. {symbol}: 评分 {score}")
            
            # 筛选评分达到7分及以上的币种
            qualified = {symbol: data for symbol, data in candidates.items() 
                        if isinstance(data, dict) and data.get('score', 0) >= 7}
            
            print(f"\n达标币种(≥7分): {len(qualified)}")
            if qualified:
                print("达标币种详情:")
                for symbol, data in sorted(qualified.items(), 
                                         key=lambda x: x[1].get('score', 0), reverse=True):
                    score = data.get('score', 0)
                    print(f"  {symbol}: {score}分")
        
        elif isinstance(candidates, list):
            # 如果是列表格式
            print("\n前10个币种:")
            for i, item in enumerate(candidates[:10]):
                if isinstance(item, dict):
                    symbol = item.get('symbol', f'未知币种{i+1}')
                    score = item.get('score', 0)
                    print(f"{i+1}. {symbol}: 评分 {score}")
                elif isinstance(item, str):
                    print(f"{i+1}. {item}: 评分信息不可用")
                else:
                    print(f"{i+1}. {item}")
            
            # 筛选评分达到7分及以上的币种
            qualified = [item for item in candidates 
                        if isinstance(item, dict) and item.get('score', 0) >= 7]
            
            print(f"\n达标币种(≥7分): {len(qualified)}")
            if qualified:
                print("达标币种详情:")
                for item in sorted(qualified, key=lambda x: x.get('score', 0), reverse=True):
                    symbol = item.get('symbol', '未知币种')
                    score = item.get('score', 0)
                    print(f"  {symbol}: {score}分")
        
        else:
            print(f"未知的数据格式: {type(candidates)}")
            
    except Exception as e:
        print(f"分析候选池时出错: {e}")

def analyze_memory_cache():
    """分析内存中的候选池状态"""
    print("\n=== 分析内存中的候选池状态 ===")
    
    # 尝试导入策略模块并检查内存状态
    try:
        sys.path.append('.')
        from strategy.maker_channel import MakerChannelStrategy
        
        # 这里我们无法直接访问运行中的实例，但可以检查类定义
        print("策略类已加载，但无法直接访问运行中的实例")
        print("建议通过日志或其他方式检查内存状态")
        
    except Exception as e:
        print(f"无法导入策略模块: {e}")

if __name__ == "__main__":
    analyze_candidate_pool()
    analyze_memory_cache()