#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
候选池详细分析工具
分析当前候选池中所有币种的评分详情、通道位置和开仓条件
"""

import pickle
import json
import os
from datetime import datetime

def analyze_candidates_cache():
    """分析候选池缓存数据"""
    cache_file = 'cache/candidates.pkl'
    
    if not os.path.exists(cache_file):
        print(f"缓存文件不存在: {cache_file}")
        return
    
    try:
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        
        print("=" * 80)
        print("候选池缓存数据分析")
        print("=" * 80)
        print(f"数据类型: {type(data)}")
        print(f"数据长度: {len(data) if hasattr(data, '__len__') else 'N/A'}")
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        if isinstance(data, dict):
            print(f"候选池包含 {len(data)} 个币种:")
            print("-" * 80)
            
            # 按评分排序
            sorted_items = []
            for symbol, info in data.items():
                score = info.get('score', 0) if isinstance(info, dict) else 0
                sorted_items.append((symbol, info, score))
            
            sorted_items.sort(key=lambda x: x[2], reverse=True)
            
            # 统计信息
            high_score_count = sum(1 for _, _, score in sorted_items if score >= 7)
            medium_score_count = sum(1 for _, _, score in sorted_items if 5 <= score < 7)
            low_score_count = sum(1 for _, _, score in sorted_items if score < 5)
            
            print(f"评分统计:")
            print(f"  高评分 (≥7分): {high_score_count} 个")
            print(f"  中评分 (5-7分): {medium_score_count} 个")
            print(f"  低评分 (<5分): {low_score_count} 个")
            print()
            
            # 显示前10个样例
            print("前10个币种详细信息:")
            print("-" * 80)
            for i, (symbol, info, score) in enumerate(sorted_items[:10]):
                print(f"{i+1:2d}. {symbol}")
                if isinstance(info, dict):
                    print(f"    评分: {score}")
                    for key, value in info.items():
                        if key != 'score':
                            print(f"    {key}: {value}")
                else:
                    print(f"    数据: {info}")
                print()
                
            # 显示高评分币种
            if high_score_count > 0:
                print("高评分币种 (≥7分):")
                print("-" * 80)
                for symbol, info, score in sorted_items:
                    if score >= 7:
                        print(f"• {symbol}: {score}分")
                        if isinstance(info, dict):
                            # 显示关键信息
                            for key in ['channel_position', 'trend', 'volume_score', 'breakout_signal']:
                                if key in info:
                                    print(f"  {key}: {info[key]}")
                        print()
            else:
                print("当前没有高评分币种 (≥7分)")
                print()
                
        else:
            print("数据格式不是字典，无法详细分析")
            print(f"数据内容: {data}")
            
    except Exception as e:
        print(f"分析缓存文件时出错: {e}")

if __name__ == "__main__":
    analyze_candidates_cache()