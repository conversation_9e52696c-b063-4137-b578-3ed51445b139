#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AKEUSDT K线数据测试脚本
用于验证AKEUSDT币种是否存在K线数据不足的问题
"""

import json
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from binance_trader import BinanceTrader

def load_config():
    """加载配置文件"""
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 确保network_params存在且结构正确
        if 'network_params' not in config:
            config['network_params'] = {
                'api_key': config.get('api_key', ''),
                'api_secret': config.get('api_secret', ''),
                'base_url': config.get('base_url', 'https://fapi.binance.com'),
                'max_rate': config.get('max_rate', 15),
                'trade_rate': config.get('trade_rate', 10),
                'proxy': config.get('proxy', {
                    'enabled': False,
                    'host': '',
                    'port': 0
                })
            }
        
        return config
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None

def test_akeusdt_klines():
    """测试AKEUSDT的K线数据"""
    print("=" * 60)
    print("🔍 AKEUSDT K线数据测试")
    print("=" * 60)
    
    # 加载配置
    config = load_config()
    if not config:
        return
    
    # 初始化BinanceTrader
    try:
        trader = BinanceTrader(config)
        print("✅ BinanceTrader 初始化成功")
    except Exception as e:
        print(f"❌ BinanceTrader 初始化失败: {e}")
        return
    
    # 测试不同的limit值
    test_limits = [20, 50, 100, 200]
    
    for limit in test_limits:
        print(f"\n🔧 测试 AKEUSDT，limit={limit}")
        try:
            klines = trader.get_klines('AKEUSDT', interval='5m', limit=limit)
            if klines:
                print(f"✅ 成功获取 {len(klines)} 条K线数据")
                if len(klines) < limit:
                    print(f"⚠️  警告：请求 {limit} 条，实际获取 {len(klines)} 条")
            else:
                print("❌ 获取K线数据失败，返回空数据")
        except Exception as e:
            print(f"❌ 获取K线数据异常: {e}")
    
    # 测试其他币种作为对比
    print(f"\n📊 对比测试其他币种")
    compare_symbols = ['BCHUSDT', 'TRXUSDT', 'BTCUSDT']
    
    for symbol in compare_symbols:
        print(f"\n🔧 测试 {symbol}，limit=20")
        try:
            klines = trader.get_klines(symbol, interval='5m', limit=20)
            if klines:
                print(f"✅ {symbol}: 成功获取 {len(klines)} 条K线数据")
            else:
                print(f"❌ {symbol}: 获取K线数据失败")
        except Exception as e:
            print(f"❌ {symbol}: 获取K线数据异常: {e}")

if __name__ == "__main__":
    test_akeusdt_klines()