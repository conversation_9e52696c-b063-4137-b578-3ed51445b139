#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责加载、验证和管理所有配置文件
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config = {}
        self.logger = logging.getLogger(__name__)
        
        # 配置文件映射
        self.config_files = {
            'main': 'config.json',
            'trading': 'trading_config.json', 
            'risk': 'risk_config.json',
            'api': 'api_config.json'
        }
        
        # 默认配置
        self.default_config = self._get_default_config()
        
        # 加载配置
        self.load_all_configs()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'main': {
                'system': {
                    'name': '全币种均线通道策略',
                    'version': '1.0.0',
                    'debug': False,
                    'timezone': 'Asia/Shanghai'
                },
                'cache': {
                    'enabled': True,
                    'ttl': 300,
                    'max_size': 1000
                },
                'logging': {
                    'level': 'INFO',
                    'file': 'strategy.log',
                    'max_size': '10MB',
                    'backup_count': 5
                }
            },
            'trading': {
                'mode': 'futures',
                'base_currency': 'USDT',
                'max_positions': 3,
                'position_size': 100,
                'leverage': 10
            },
            'risk': {
                'max_daily_loss': 0.05,
                'max_drawdown': 0.10,
                'position_concentration': 0.33
            },
            'api': {
                'binance': {
                    'timeout': 30,
                    'retry_count': 3,
                    'retry_delay': 1
                }
            }
        }
    
    def load_all_configs(self):
        """加载所有配置文件"""
        self.logger.info("开始加载配置文件...")
        
        for config_type, filename in self.config_files.items():
            try:
                config_data = self.load_config_file(filename)
                if config_data:
                    self.config[config_type] = config_data
                    self.logger.info(f"成功加载配置: {filename}")
                else:
                    # 使用默认配置
                    self.config[config_type] = self.default_config.get(config_type, {})
                    self.logger.warning(f"使用默认配置: {config_type}")
            except Exception as e:
                self.logger.error(f"加载配置文件失败 {filename}: {e}")
                self.config[config_type] = self.default_config.get(config_type, {})
        
        # 验证配置
        self.validate_all_configs()
        
        self.logger.info("配置加载完成")
    
    def load_config_file(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        加载单个配置文件
        
        Args:
            filename: 配置文件名
            
        Returns:
            配置数据字典，如果文件不存在返回None
        """
        config_path = self.config_dir / filename
        
        if not config_path.exists():
            # 尝试加载模板文件
            template_path = self.config_dir / f"{filename}.template"
            if template_path.exists():
                self.logger.info(f"配置文件不存在，尝试从模板创建: {filename}")
                return self._create_from_template(template_path, config_path)
            else:
                self.logger.warning(f"配置文件不存在: {config_path}")
                return None
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return config_data
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件JSON格式错误 {filename}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"读取配置文件失败 {filename}: {e}")
            return None
    
    def _create_from_template(self, template_path: Path, config_path: Path) -> Optional[Dict[str, Any]]:
        """
        从模板创建配置文件
        
        Args:
            template_path: 模板文件路径
            config_path: 目标配置文件路径
            
        Returns:
            配置数据字典
        """
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 替换环境变量
            config_content = self._replace_env_variables(template_content)
            
            # 保存配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            # 解析配置
            config_data = json.loads(config_content)
            self.logger.info(f"从模板创建配置文件: {config_path}")
            return config_data
            
        except Exception as e:
            self.logger.error(f"从模板创建配置文件失败: {e}")
            return None
    
    def _replace_env_variables(self, content: str) -> str:
        """
        替换配置中的环境变量
        
        Args:
            content: 配置文件内容
            
        Returns:
            替换后的内容
        """
        import re
        
        # 查找所有 ${VAR_NAME} 格式的环境变量
        pattern = r'\$\{([^}]+)\}'
        
        def replace_var(match):
            var_name = match.group(1)
            env_value = os.getenv(var_name)
            if env_value is None:
                self.logger.warning(f"环境变量未设置: {var_name}")
                return f"${{{var_name}}}"  # 保持原样
            return env_value
        
        return re.sub(pattern, replace_var, content)
    
    def validate_all_configs(self):
        """验证所有配置"""
        self.logger.info("开始验证配置...")
        
        # 验证主配置
        self._validate_main_config()
        
        # 验证交易配置
        self._validate_trading_config()
        
        # 验证风险配置
        self._validate_risk_config()
        
        # 验证API配置
        self._validate_api_config()
        
        self.logger.info("配置验证完成")
    
    def _validate_main_config(self):
        """验证主配置"""
        main_config = self.config.get('main', {})
        
        # 检查必需字段
        required_fields = ['system', 'logging']
        for field in required_fields:
            if field not in main_config:
                self.logger.warning(f"主配置缺少字段: {field}")
                main_config[field] = self.default_config['main'][field]
    
    def _validate_trading_config(self):
        """验证交易配置"""
        trading_config = self.config.get('trading', {})
        
        # 检查仓位大小
        position_size = trading_config.get('position_size', 0)
        if position_size <= 0:
            self.logger.warning("仓位大小无效，使用默认值")
            trading_config['position_size'] = self.default_config['trading']['position_size']
        
        # 检查杠杆倍数
        leverage = trading_config.get('leverage', 0)
        if leverage <= 0 or leverage > 125:
            self.logger.warning("杠杆倍数无效，使用默认值")
            trading_config['leverage'] = self.default_config['trading']['leverage']
    
    def _validate_risk_config(self):
        """验证风险配置"""
        risk_config = self.config.get('risk', {})
        
        # 检查最大回撤
        max_drawdown = risk_config.get('max_drawdown', 0)
        if max_drawdown <= 0 or max_drawdown > 1:
            self.logger.warning("最大回撤设置无效，使用默认值")
            risk_config['max_drawdown'] = self.default_config['risk']['max_drawdown']
    
    def _validate_api_config(self):
        """验证API配置"""
        api_config = self.config.get('api', {})
        
        # 检查Binance配置
        binance_config = api_config.get('binance', {})
        if 'api_key' not in binance_config or not binance_config['api_key']:
            self.logger.warning("Binance API密钥未配置")
        
        if 'api_secret' not in binance_config or not binance_config['api_secret']:
            self.logger.warning("Binance API密钥未配置")
    
    def get_config(self, config_type: str, key: str = None, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            config_type: 配置类型 (main, trading, risk, api)
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        config_data = self.config.get(config_type, {})
        
        if key is None:
            return config_data
        
        # 支持嵌套键访问
        keys = key.split('.')
        value = config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set_config(self, config_type: str, key: str, value: Any):
        """
        设置配置值
        
        Args:
            config_type: 配置类型
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        if config_type not in self.config:
            self.config[config_type] = {}
        
        # 支持嵌套键设置
        keys = key.split('.')
        config_data = self.config[config_type]
        
        for k in keys[:-1]:
            if k not in config_data:
                config_data[k] = {}
            config_data = config_data[k]
        
        config_data[keys[-1]] = value
    
    def save_config(self, config_type: str):
        """
        保存配置到文件
        
        Args:
            config_type: 配置类型
        """
        if config_type not in self.config_files:
            self.logger.error(f"未知配置类型: {config_type}")
            return
        
        filename = self.config_files[config_type]
        config_path = self.config_dir / filename
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config[config_type], f, indent=2, ensure_ascii=False)
            self.logger.info(f"配置保存成功: {filename}")
        except Exception as e:
            self.logger.error(f"保存配置文件失败 {filename}: {e}")
    
    def reload_config(self, config_type: str = None):
        """
        重新加载配置
        
        Args:
            config_type: 配置类型，None表示重新加载所有配置
        """
        if config_type is None:
            self.load_all_configs()
        else:
            if config_type in self.config_files:
                filename = self.config_files[config_type]
                config_data = self.load_config_file(filename)
                if config_data:
                    self.config[config_type] = config_data
                    self.logger.info(f"重新加载配置: {filename}")
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config.copy()
    
    def is_valid(self) -> bool:
        """检查配置是否有效"""
        try:
            # 检查必需的配置类型
            required_types = ['main', 'trading', 'risk', 'api']
            for config_type in required_types:
                if config_type not in self.config:
                    return False
            
            # 检查API密钥
            api_key = self.get_config('api', 'binance.api_key')
            api_secret = self.get_config('api', 'binance.api_secret')
            
            if not api_key or not api_secret:
                return False
            
            return True
        except Exception:
            return False


# 全局配置管理器实例
config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager()
    return config_manager

def init_config_manager(config_dir: str = "config") -> ConfigManager:
    """初始化全局配置管理器"""
    global config_manager
    config_manager = ConfigManager(config_dir)
    return config_manager