#!/usr/bin/env python3
"""
直接调试API响应脚本
"""

import sys
import json
import requests
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from binance_trader import BinanceTrader

def debug_api_response():
    """直接调试API响应"""
    
    # 加载配置
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return
    
    # 初始化交易器
    trader = BinanceTrader(config)
    
    # 测试有问题的币种
    test_symbols = ['SSVUSDT', 'BCHUSDT', 'BTCUSDT']
    
    print("直接调试API响应...")
    print("=" * 60)
    
    for symbol in test_symbols:
        print(f"\n测试 {symbol}:")
        
        # 直接调用API
        try:
            response = trader.http.get('/fapi/v1/klines', {
                'symbol': symbol,
                'interval': '15m',
                'limit': 50
            })
            
            print(f"  API响应类型: {type(response)}")
            print(f"  API响应长度: {len(response) if response else 0}")
            
            if response:
                print(f"  前3条数据:")
                for i, kline in enumerate(response[:3]):
                    print(f"    [{i}]: {kline}")
                
                if len(response) > 3:
                    print(f"  ... (省略 {len(response) - 3} 条)")
                
                # 检查数据格式
                if len(response) > 0:
                    first_kline = response[0]
                    print(f"  第一条K线字段数: {len(first_kline)}")
                    print(f"  第一条K线内容: {first_kline}")
            else:
                print("  API响应为空")
                
        except Exception as e:
            print(f"  API调用异常: {e}")
        
        print("-" * 40)

def test_different_limits():
    """测试不同的limit参数"""
    
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return
    
    trader = BinanceTrader(config)
    
    symbol = 'SSVUSDT'
    limits = [1, 5, 10, 20, 50, 100]
    
    print(f"\n测试 {symbol} 不同limit参数:")
    print("=" * 60)
    
    for limit in limits:
        try:
            response = trader.http.get('/fapi/v1/klines', {
                'symbol': symbol,
                'interval': '15m',
                'limit': limit
            })
            
            actual_count = len(response) if response else 0
            print(f"  limit={limit:3d} -> 实际返回: {actual_count:3d} 条")
            
        except Exception as e:
            print(f"  limit={limit:3d} -> 异常: {e}")

def test_direct_requests():
    """使用requests直接测试API"""
    
    print("\n使用requests直接测试API:")
    print("=" * 60)
    
    base_url = "https://fapi.binance.com"
    
    test_cases = [
        {'symbol': 'BTCUSDT', 'interval': '15m', 'limit': 50},
        {'symbol': 'SSVUSDT', 'interval': '15m', 'limit': 50},
        {'symbol': 'BCHUSDT', 'interval': '15m', 'limit': 50},
    ]
    
    for case in test_cases:
        try:
            url = f"{base_url}/fapi/v1/klines"
            response = requests.get(url, params=case, timeout=10)
            
            print(f"\n{case['symbol']}:")
            print(f"  HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"  返回数据条数: {len(data)}")
                
                if len(data) > 0:
                    print(f"  第一条: {data[0]}")
                    print(f"  最后一条: {data[-1]}")
            else:
                print(f"  错误响应: {response.text}")
                
        except Exception as e:
            print(f"  请求异常: {e}")

if __name__ == "__main__":
    debug_api_response()
    test_different_limits()
    test_direct_requests()