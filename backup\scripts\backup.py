#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略版本备份管理系统
实现自动化备份、版本管理和完整性验证功能
"""

import os
import json
import shutil
import hashlib
import datetime
import logging
import zipfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class BackupManager:
    """备份管理器"""
    
    def __init__(self, source_dir: str, backup_dir: str):
        """
        初始化备份管理器
        
        Args:
            source_dir: 源代码目录
            backup_dir: 备份目录
        """
        self.source_dir = Path(source_dir)
        self.backup_dir = Path(backup_dir)
        self.versions_dir = self.backup_dir / "versions"
        self.logs_dir = self.backup_dir / "logs"
        
        # 确保目录存在
        self.versions_dir.mkdir(parents=True, exist_ok=True)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        self._setup_logging()
        
        # 定义备份文件清单
        self.backup_files = {
            "strategy_files": [
                "strategy/maker_channel.py",
                "strategy/maker_channel_backup.py",
                "strategy/__init__.py"
            ],
            "config_files": [
                "config/config.json",
                "config/trading_config.json", 
                "config/risk_config.json",
                "config/api_config.json"
            ],
            "core_files": [
                "binance_trader.py",
                "cache_manager.py", 
                "http_client.py",
                "main.py",
                "requirements.txt"
            ],
            "data_files": [
                "cache/candidates.pkl",
                "cache/symbols.json"
            ],
            "log_files": [
                "strategy.log"
            ],
            "doc_files": [
                "optimization_improvements_summary.md",
                "rollback_and_optimization_plan.md",
                "末位淘汰功能验证任务清单.md",
                "版本备份机制设计方案.md"
            ]
        }
    
    def _setup_logging(self):
        """设置日志配置"""
        log_file = self.logs_dir / "backup.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def generate_version_name(self, version: str = None) -> str:
        """
        生成版本名称
        
        Args:
            version: 版本号，如果为None则自动生成
            
        Returns:
            版本名称字符串
        """
        now = datetime.datetime.now()
        if version is None:
            # 自动生成版本号
            existing_versions = self.list_backups()
            if existing_versions:
                # 从最新版本号递增
                latest_version = existing_versions[0]['version']
                if latest_version.startswith('v'):
                    try:
                        version_num = float(latest_version[1:]) + 0.1
                        version = f"v{version_num:.1f}"
                    except:
                        version = "v1.0"
                else:
                    version = "v1.0"
            else:
                version = "v1.0"
        
        timestamp = now.strftime("%Y%m%d_%H%M")
        return f"{version}_{timestamp}"
    
    def calculate_file_checksum(self, file_path: Path) -> str:
        """
        计算文件SHA256校验和
        
        Args:
            file_path: 文件路径
            
        Returns:
            SHA256校验和字符串
        """
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件校验和失败 {file_path}: {e}")
            return ""
    
    def get_system_state(self) -> Dict:
        """
        获取当前系统状态
        
        Returns:
            系统状态字典
        """
        state = {
            "strategy_running": False,
            "active_positions": [],
            "active_orders": 0,
            "account_balance": 0.0,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        # 尝试读取策略日志获取状态信息
        try:
            log_file = self.source_dir / "strategy.log"
            if log_file.exists():
                # 读取最后几行日志判断运行状态
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        last_line = lines[-1]
                        # 简单判断：如果最后一行是最近的时间戳，认为在运行
                        if datetime.datetime.now().strftime("%Y-%m-%d") in last_line:
                            state["strategy_running"] = True
        except Exception as e:
            self.logger.warning(f"获取系统状态失败: {e}")
        
        return state
    
    def check_disk_space(self, required_mb: int = 1024) -> bool:
        """
        检查磁盘空间
        
        Args:
            required_mb: 所需空间（MB）
            
        Returns:
            是否有足够空间
        """
        try:
            stat = shutil.disk_usage(self.backup_dir)
            free_mb = stat.free / (1024 * 1024)
            
            if free_mb < required_mb:
                self.logger.error(f"磁盘空间不足: 需要{required_mb}MB，可用{free_mb:.1f}MB")
                return False
            
            self.logger.info(f"磁盘空间检查通过: 可用{free_mb:.1f}MB")
            return True
        except Exception as e:
            self.logger.error(f"磁盘空间检查失败: {e}")
            return False
    
    def backup_file(self, src_path: Path, dst_path: Path) -> bool:
        """
        备份单个文件
        
        Args:
            src_path: 源文件路径
            dst_path: 目标文件路径
            
        Returns:
            是否备份成功
        """
        try:
            if not src_path.exists():
                self.logger.warning(f"源文件不存在: {src_path}")
                return False
            
            # 确保目标目录存在
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(src_path, dst_path)
            
            # 验证复制结果
            if dst_path.exists() and dst_path.stat().st_size == src_path.stat().st_size:
                self.logger.debug(f"文件备份成功: {src_path} -> {dst_path}")
                return True
            else:
                self.logger.error(f"文件备份验证失败: {src_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"文件备份失败 {src_path}: {e}")
            return False
    
    def backup_directory(self, src_dir: Path, dst_dir: Path, exclude_patterns: List[str] = None) -> bool:
        """
        备份目录
        
        Args:
            src_dir: 源目录
            dst_dir: 目标目录
            exclude_patterns: 排除模式列表
            
        Returns:
            是否备份成功
        """
        if exclude_patterns is None:
            exclude_patterns = ['__pycache__', '*.pyc', '*.log']
        
        try:
            if not src_dir.exists():
                self.logger.warning(f"源目录不存在: {src_dir}")
                return False
            
            dst_dir.mkdir(parents=True, exist_ok=True)
            
            success_count = 0
            total_count = 0
            
            for item in src_dir.rglob('*'):
                if item.is_file():
                    # 检查是否需要排除
                    skip = False
                    for pattern in exclude_patterns:
                        if pattern in str(item):
                            skip = True
                            break
                    
                    if skip:
                        continue
                    
                    total_count += 1
                    relative_path = item.relative_to(src_dir)
                    dst_file = dst_dir / relative_path
                    
                    if self.backup_file(item, dst_file):
                        success_count += 1
            
            self.logger.info(f"目录备份完成: {src_dir} ({success_count}/{total_count} 文件)")
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"目录备份失败 {src_dir}: {e}")
            return False
    
    def create_backup(self, version: str = None, backup_type: str = "full", description: str = "") -> Optional[str]:
        """
        创建备份
        
        Args:
            version: 版本号
            backup_type: 备份类型 (full/incremental)
            description: 备份描述
            
        Returns:
            备份版本名称，失败返回None
        """
        self.logger.info(f"开始创建备份 - 类型: {backup_type}")
        
        # 检查磁盘空间
        if not self.check_disk_space():
            return None
        
        # 生成版本名称
        version_name = self.generate_version_name(version)
        backup_path = self.versions_dir / version_name
        
        try:
            # 创建备份目录
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # 获取系统状态
            system_state = self.get_system_state()
            
            # 备份文件统计
            backup_stats = {
                "files_backed_up": {},
                "checksums": {},
                "failed_files": [],
                "total_size": 0
            }
            
            # 按类别备份文件
            for category, file_list in self.backup_files.items():
                self.logger.info(f"备份 {category}...")
                backup_stats["files_backed_up"][category] = []
                
                for file_path in file_list:
                    src_file = self.source_dir / file_path
                    dst_file = backup_path / file_path
                    
                    if self.backup_file(src_file, dst_file):
                        backup_stats["files_backed_up"][category].append(file_path)
                        
                        # 计算校验和
                        checksum = self.calculate_file_checksum(dst_file)
                        if checksum:
                            backup_stats["checksums"][file_path] = f"sha256:{checksum}"
                        
                        # 累计大小
                        if dst_file.exists():
                            backup_stats["total_size"] += dst_file.stat().st_size
                    else:
                        backup_stats["failed_files"].append(file_path)
            
            # 特殊处理：备份cache目录（选择性）
            cache_src = self.source_dir / "cache"
            cache_dst = backup_path / "cache"
            if cache_src.exists():
                self.logger.info("备份缓存目录...")
                # 只备份重要的缓存文件，排除大量的深度数据
                important_cache_files = ["candidates.pkl", "symbols.json"]
                cache_dst.mkdir(parents=True, exist_ok=True)
                
                for cache_file in important_cache_files:
                    src_file = cache_src / cache_file
                    dst_file = cache_dst / cache_file
                    if src_file.exists():
                        self.backup_file(src_file, dst_file)
            
            # 创建元数据文件
            metadata = {
                "backup_info": {
                    "version": version_name.split('_')[0],
                    "timestamp": datetime.datetime.now().isoformat(),
                    "backup_type": backup_type,
                    "created_by": "auto_backup_system",
                    "description": description or f"{backup_type}备份"
                },
                "system_state": system_state,
                "files_backed_up": backup_stats["files_backed_up"],
                "checksums": backup_stats["checksums"],
                "failed_files": backup_stats["failed_files"],
                "backup_size": {
                    "total_size_mb": round(backup_stats["total_size"] / (1024 * 1024), 2),
                    "file_count": sum(len(files) for files in backup_stats["files_backed_up"].values())
                }
            }
            
            # 保存元数据
            metadata_file = backup_path / "metadata.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            # 验证备份
            if self.verify_backup(backup_path):
                self.logger.info(f"备份创建成功: {version_name}")
                self.logger.info(f"备份大小: {metadata['backup_size']['total_size_mb']}MB")
                self.logger.info(f"备份文件数: {metadata['backup_size']['file_count']}")
                
                # 清理旧备份
                self.cleanup_old_backups()
                
                return version_name
            else:
                self.logger.error(f"备份验证失败: {version_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            return None
    
    def verify_backup(self, backup_path: Path) -> bool:
        """
        验证备份完整性
        
        Args:
            backup_path: 备份路径
            
        Returns:
            是否验证通过
        """
        try:
            self.logger.info(f"验证备份: {backup_path}")
            
            # 检查元数据文件
            metadata_file = backup_path / "metadata.json"
            if not metadata_file.exists():
                self.logger.error("元数据文件不存在")
                return False
            
            # 读取元数据
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 验证文件完整性
            checksums = metadata.get("checksums", {})
            failed_verifications = []
            
            for file_path, expected_checksum in checksums.items():
                file_full_path = backup_path / file_path
                if file_full_path.exists():
                    actual_checksum = f"sha256:{self.calculate_file_checksum(file_full_path)}"
                    if actual_checksum != expected_checksum:
                        failed_verifications.append(file_path)
                        self.logger.error(f"校验和不匹配: {file_path}")
                else:
                    failed_verifications.append(file_path)
                    self.logger.error(f"备份文件不存在: {file_path}")
            
            if failed_verifications:
                self.logger.error(f"备份验证失败，{len(failed_verifications)}个文件有问题")
                return False
            
            # 验证配置文件语法
            config_files = [
                backup_path / "config" / "config.json",
                backup_path / "config" / "trading_config.json"
            ]
            
            for config_file in config_files:
                if config_file.exists():
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            json.load(f)
                    except json.JSONDecodeError as e:
                        self.logger.error(f"配置文件语法错误 {config_file}: {e}")
                        return False
            
            self.logger.info("备份验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"备份验证失败: {e}")
            return False
    
    def list_backups(self) -> List[Dict]:
        """
        列出所有备份版本
        
        Returns:
            备份版本列表
        """
        backups = []
        
        try:
            for backup_dir in self.versions_dir.iterdir():
                if backup_dir.is_dir():
                    metadata_file = backup_dir / "metadata.json"
                    if metadata_file.exists():
                        try:
                            with open(metadata_file, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                            
                            backup_info = {
                                "version_name": backup_dir.name,
                                "version": metadata["backup_info"]["version"],
                                "timestamp": metadata["backup_info"]["timestamp"],
                                "backup_type": metadata["backup_info"]["backup_type"],
                                "description": metadata["backup_info"]["description"],
                                "size_mb": metadata["backup_size"]["total_size_mb"],
                                "file_count": metadata["backup_size"]["file_count"],
                                "path": str(backup_dir)
                            }
                            backups.append(backup_info)
                        except Exception as e:
                            self.logger.warning(f"读取备份元数据失败 {backup_dir}: {e}")
            
            # 按时间戳排序（最新的在前）
            backups.sort(key=lambda x: x["timestamp"], reverse=True)
            
        except Exception as e:
            self.logger.error(f"列出备份失败: {e}")
        
        return backups
    
    def cleanup_old_backups(self, keep_count: int = 5):
        """
        清理旧备份
        
        Args:
            keep_count: 保留的备份数量
        """
        try:
            backups = self.list_backups()
            
            if len(backups) > keep_count:
                to_delete = backups[keep_count:]
                
                for backup in to_delete:
                    backup_path = Path(backup["path"])
                    if backup_path.exists():
                        shutil.rmtree(backup_path)
                        self.logger.info(f"删除旧备份: {backup['version_name']}")
                
                self.logger.info(f"清理完成，保留最新{keep_count}个备份")
            
        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")
    
    def get_backup_info(self, version_name: str) -> Optional[Dict]:
        """
        获取指定备份的详细信息
        
        Args:
            version_name: 版本名称
            
        Returns:
            备份信息字典
        """
        backup_path = self.versions_dir / version_name
        metadata_file = backup_path / "metadata.json"
        
        if not metadata_file.exists():
            return None
        
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"读取备份信息失败: {e}")
            return None


def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="策略版本备份管理系统")
    parser.add_argument("--source", default="e:/allmace", help="源代码目录")
    parser.add_argument("--backup", default="e:/allmace/backup", help="备份目录")
    parser.add_argument("--action", choices=["backup", "list", "verify"], default="backup", help="操作类型")
    parser.add_argument("--version", help="版本号")
    parser.add_argument("--type", choices=["full", "incremental"], default="full", help="备份类型")
    parser.add_argument("--description", help="备份描述")
    
    args = parser.parse_args()
    
    # 创建备份管理器
    backup_manager = BackupManager(args.source, args.backup)
    
    if args.action == "backup":
        # 创建备份
        version_name = backup_manager.create_backup(
            version=args.version,
            backup_type=args.type,
            description=args.description
        )
        if version_name:
            print(f"备份创建成功: {version_name}")
        else:
            print("备份创建失败")
            exit(1)
    
    elif args.action == "list":
        # 列出备份
        backups = backup_manager.list_backups()
        if backups:
            print(f"{'版本名称':<25} {'版本':<8} {'类型':<12} {'大小(MB)':<10} {'文件数':<8} {'时间'}")
            print("-" * 80)
            for backup in backups:
                print(f"{backup['version_name']:<25} {backup['version']:<8} {backup['backup_type']:<12} "
                      f"{backup['size_mb']:<10.1f} {backup['file_count']:<8} {backup['timestamp'][:19]}")
        else:
            print("没有找到备份")
    
    elif args.action == "verify":
        # 验证备份
        if args.version:
            backup_path = backup_manager.versions_dir / args.version
            if backup_manager.verify_backup(backup_path):
                print(f"备份验证通过: {args.version}")
            else:
                print(f"备份验证失败: {args.version}")
                exit(1)
        else:
            print("请指定要验证的版本")
            exit(1)


if __name__ == "__main__":
    main()