#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
候选池详细分析工具
提供候选池中所有币对的完整评分详情、通道位置、开仓条件等信息
"""

import pandas as pd
import numpy as np
import pickle
import logging
import time
from datetime import datetime
from pathlib import Path
import sys
import os
import requests
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, as_completed
from cache_manager import CacheManager

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from binance_trader import BinanceTrader
from enhanced_score_calculator import EnhancedScoreCalculator
from strategy.dynamic_tf_helper import dynamic_tf_for_channel

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('CandidatePoolAnalyzer')

class CandidatePoolAnalyzer:
    """候选池详细分析器"""
    
    def __init__(self, trader: BinanceTrader = None):
        self.trader = trader
        self.log = logging.getLogger('CandidateAnalyzer')
        self.score_calculator = EnhancedScoreCalculator()
        self.cache_manager = CacheManager()
        
        # Binance API配置
        self.base_url = "https://fapi.binance.com"
        
        # 设置日志格式
        if not self.log.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.log.addHandler(handler)
            self.log.setLevel(logging.INFO)
    
    def load_candidate_cache(self):
        """加载候选池缓存数据"""
        try:
            cache_file = Path('cache/candidates.pkl')
            if not cache_file.exists():
                self.log.error("候选池缓存文件不存在")
                return []
            
            with open(cache_file, 'rb') as f:
                candidates = pickle.load(f)
            
            self.log.info(f"成功加载候选池缓存，包含 {len(candidates)} 个币种")
            return candidates if isinstance(candidates, list) else []
            
        except Exception as e:
            self.log.error(f"加载候选池缓存失败: {e}")
            return []
    
    def get_klines_data(self, symbol, interval='15m', limit=200):
        """获取K线数据，优先使用缓存"""
        try:
            # 首先尝试从缓存加载
            cached_data = self.cache_manager.load_klines(symbol, interval, limit)
            if cached_data is not None and not cached_data.empty:
                self.log.info(f"从缓存加载K线数据成功: {symbol} {interval} {limit}条")
                return cached_data
            
            # 缓存未命中，从API获取
            self.log.info(f"缓存未命中，从API获取K线数据: {symbol} {interval} {limit}条")
            
            max_retries = 3
            retry_delay = 2  # 秒
            
            for attempt in range(max_retries):
                try:
                    if self.trader:
                        # 使用trader获取数据
                        klines = self.trader.http.get('/fapi/v1/klines', {
                            'symbol': symbol,
                            'interval': interval,
                            'limit': limit
                        })
                    else:
                        # 直接使用Binance API
                        url = f"{self.base_url}/fapi/v1/klines"
                        params = {
                            'symbol': symbol,
                            'interval': interval,
                            'limit': limit
                        }
                        response = requests.get(url, params=params, timeout=30)
                        if response.status_code == 200:
                            klines = response.json()
                            if klines:
                                logger.info(f"{symbol} K线数据获取成功 - 时间框架: {interval}, 数据量: {len(klines)}")
                            else:
                                logger.warning(f"{symbol} K线数据为空 - 时间框架: {interval}")
                        else:
                            logger.error(f"API请求失败: {response.status_code} - {response.text}")
                            if attempt < max_retries - 1:
                                logger.info(f"{symbol} 第{attempt + 1}次重试...")
                                time.sleep(retry_delay)
                                continue
                            return None
                    
                    if klines:
                        df = pd.DataFrame(klines, columns=[
                            'timestamp', 'o', 'h', 'l', 'c', 'v', 'close_time',
                            'quote_volume', 'count', 'taker_buy_volume', 'taker_buy_quote_volume', 'ignore'
                        ])
                        
                        # 转换数据类型
                        for col in ['o', 'h', 'l', 'c', 'v']:
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                        
                        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                        df.set_index('timestamp', inplace=True)
                        df.name = symbol
                        
                        # 保存到缓存
                        self.cache_manager.save_klines(symbol, interval, limit, df)
                        
                        return df
                    
                    return None
                    
                except requests.exceptions.Timeout:
                    logger.error(f"{symbol} K线数据获取超时 - 时间框架: {interval} (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        logger.info(f"{symbol} 等待{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    return None
                except requests.exceptions.ConnectionError:
                    logger.error(f"{symbol} 网络连接错误 - 时间框架: {interval} (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        logger.info(f"{symbol} 等待{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    return None
                except Exception as e:
                    logger.error(f"获取{symbol}的K线数据失败: {e} (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    return None
            
            return None
            
        except Exception as e:
            self.log.error(f"获取K线数据失败: {symbol} - {e}")
            return None
    
    def get_symbol_age_days(self, symbol):
        """获取币种上线天数"""
        try:
            if self.trader:
                # 使用trader获取交易对信息
                exchange_info = self.trader.http.get('/fapi/v1/exchangeInfo')
            else:
                # 直接使用Binance API
                url = f"{self.base_url}/fapi/v1/exchangeInfo"
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    exchange_info = response.json()
                else:
                    self.log.error(f"获取交易对信息失败: {response.status_code}")
                    return 30  # 默认值
            
            if exchange_info and 'symbols' in exchange_info:
                for sym_info in exchange_info['symbols']:
                    if sym_info['symbol'] == symbol:
                        onboard_date = sym_info.get('onBoardDate')
                        if onboard_date:
                            onboard_time = pd.Timestamp(int(onboard_date), unit='ms')
                            age_days = (pd.Timestamp.utcnow() - onboard_time).days
                            return max(1, age_days)
            
            # 默认返回30天
            return 30
            
        except Exception as e:
            self.log.error(f"获取{symbol}币龄失败: {e}")
            return 30
    
    def get_depth_info(self, symbol):
        """获取深度信息，优先使用缓存"""
        try:
            # 首先尝试从缓存加载
            cached_depth = self.cache_manager.load_depth(symbol)
            if cached_depth is not None:
                logger.info(f"从缓存加载深度数据成功: {symbol}")
                return cached_depth
            
            # 缓存未命中，从API获取
            logger.info(f"缓存未命中，从API获取深度数据: {symbol}")
            
            if self.trader and hasattr(self.trader, 'http'):
                # 使用现有的trader获取数据
                response = self.trader.http.get(
                    '/fapi/v1/depth',
                    params={'symbol': symbol, 'limit': 500}
                )
                if response and response.get('data'):
                    depth_data = response['data']
                else:
                    return None
            else:
                # 直接通过Binance API获取数据
                url = f"{self.base_url}/fapi/v1/depth"
                params = {'symbol': symbol, 'limit': 500}
                
                response = requests.get(url, params=params, timeout=10)
                if response.status_code == 200:
                    depth_data = response.json()
                else:
                    logger.error(f"{symbol} 深度数据获取失败 - HTTP状态码: {response.status_code}")
                    return None
            
            # 计算0.1%深度
            bids = depth_data.get('bids', [])
            asks = depth_data.get('asks', [])
            
            if not bids or not asks:
                logger.warning(f"{symbol} 深度数据为空")
                return None
            
            # 获取最佳买卖价
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            mid_price = (best_bid + best_ask) / 2
            
            # 计算0.1%价格范围
            price_range = mid_price * 0.001  # 0.1%
            
            # 计算买单深度（0.1%范围内）
            bid_depth = 0
            for price_str, qty_str in bids:
                price = float(price_str)
                if price >= (mid_price - price_range):
                    bid_depth += float(qty_str) * price
                else:
                    break
            
            # 计算卖单深度（0.1%范围内）
            ask_depth = 0
            for price_str, qty_str in asks:
                price = float(price_str)
                if price <= (mid_price + price_range):
                    ask_depth += float(qty_str) * price
                else:
                    break
            
            # 总深度
            total_depth = bid_depth + ask_depth
            
            result = {
                'depth_0_1_percent': total_depth,
                'bid_depth': bid_depth,
                'ask_depth': ask_depth,
                'mid_price': mid_price
            }
            
            # 保存到缓存
            self.cache_manager.save_depth(symbol, result)
            
            logger.info(f"{symbol} 深度信息获取成功 - 0.1%深度: ${total_depth:,.0f}")
            
            return result
            
        except requests.exceptions.Timeout:
            logger.error(f"{symbol} 深度数据获取超时")
            return None
        except requests.exceptions.ConnectionError:
            logger.error(f"{symbol} 深度数据网络连接错误")
            return None
        except Exception as e:
            logger.error(f"{symbol} 深度信息获取异常: {str(e)}")
            return None
    
    def calculate_channel_position(self, df, age_days):
        """计算币种在通道中的位置"""
        try:
            if df is None or len(df) < 50:
                return "数据不足", 0, 0, 0
            
            # 使用动态时间框架
            tf_minutes = dynamic_tf_for_channel(age_days)
            n = max(2, int(tf_minutes * 60 / 60))  # 转换为K线根数
            
            # 计算通道
            upper_band = df['h'].rolling(n).max().iloc[-1]
            lower_band = df['l'].rolling(n).min().iloc[-1]
            middle_band = (upper_band + lower_band) / 2
            
            current_price = df['c'].iloc[-1]
            
            # 判断位置
            if current_price >= upper_band * 0.95:  # 接近或突破上轨
                if current_price >= upper_band:
                    position = "突破上轨"
                else:
                    position = "接近上轨"
            elif current_price <= lower_band * 1.05:  # 接近或跌破下轨
                if current_price <= lower_band:
                    position = "跌破下轨"
                else:
                    position = "接近下轨"
            elif current_price >= middle_band:
                position = "中轨上方"
            else:
                position = "中轨下方"
            
            return position, upper_band, middle_band, lower_band
            
        except Exception as e:
            self.log.error(f"计算通道位置失败: {e}")
            return "计算失败", 0, 0, 0
    
    def check_breakthrough_conditions(self, df, age_days):
        """检查通道突破条件"""
        try:
            if df is None or len(df) < 50:
                return False, "数据不足"
            
            # 使用动态时间框架
            tf_minutes = dynamic_tf_for_channel(age_days)
            n = max(2, int(tf_minutes * 60 / 60))
            
            # 计算通道上轨
            upper_band = df['h'].rolling(n).max().iloc[-1]
            current_price = df['c'].iloc[-1]
            high_price = df['h'].iloc[-1]
            
            # 条件1：收盘价接近上轨（95%以上）或最高价突破上轨
            close_ratio = current_price / upper_band
            high_breakthrough = high_price >= upper_band
            
            if close_ratio < 0.95 and not high_breakthrough:
                return False, f"未接近/突破上轨: 收盘比例={close_ratio:.3f}"
            
            # 条件2：突破幅度限制（不超过5%）
            if high_breakthrough:
                breakthrough_pct = (high_price - upper_band) / upper_band * 100
                if breakthrough_pct > 5.0:
                    return False, f"突破幅度过大: {breakthrough_pct:.2f}% > 5%"
            
            # 条件3：检查最近5根K线是否有突破（避免连续突破）
            recent_above_count = 0
            for i in range(-5, 0):
                if i >= -len(df):
                    historical_upper = df['h'].rolling(n).max().iloc[i]
                    if df['h'].iloc[i] >= historical_upper:
                        recent_above_count += 1
            
            if recent_above_count >= 5:
                return False, f"突破时间过长: 连续{recent_above_count}根K线突破"
            
            return True, "满足突破条件"
            
        except Exception as e:
            return False, f"检查失败: {e}"
    
    def calculate_dynamic_timeframe(self, age_days):
        """根据币龄计算动态时间周期"""
        if age_days >= 1000:
            return "6h"
        elif age_days >= 365:
            return "4h"
        elif age_days >= 180:
            return "2h"
        elif age_days >= 90:
            return "1h"
        elif age_days >= 30:
            return "30m"
        else:
            return "15m"
    
    def calculate_score(self, symbol, klines, age_days):
        """计算币种评分 - 基于旧版策略的M、L、V、A、T评分系统"""
        try:
            # 转换为DataFrame格式
            df = pd.DataFrame(klines, columns=['timestamp', 'o', 'h', 'l', 'c', 'v', 'close_time', 'quote_volume', 'count', 'taker_buy_volume', 'taker_buy_quote_volume', 'ignore'])
            df = df.astype({
                'o': float, 'h': float, 'l': float, 'c': float, 'v': float,
                'quote_volume': float, 'taker_buy_volume': float, 'taker_buy_quote_volume': float
            })
            
            # 1. 深度评分 L (0-2分)
            depth = self.get_depth_info(symbol)
            depth_value = depth.get('depth_0_1_percent', 0) if depth else 0
            score_l = 2 if depth_value >= 2e6 else 1 if depth_value >= 2e5 else 0
            
            # 2. 成交量变化评分 V (0-1分)
            if len(df) >= 50:
                vol24_chg = df['v'].iloc[-25:].mean() / df['v'].iloc[-50:-25].mean() - 1
                score_v = 1 if vol24_chg >= 0.0 else 0
            else:
                score_v = 0
            
            # 3. 币龄评分 A (0-2分) - 使用分钟级年龄，对新币友好
            age_min = age_days * 24 * 60  # 转换为分钟
            if age_min <= 60*24*2:                # ≤2天（新币黄金期）
                score_a = 2
            elif 60*24*2 < age_min <= 60*24*7:    # 2-7天
                score_a = 2  
            elif 60*24*7 < age_min <= 60*24*30:   # 7-30天
                score_a = 1
            elif 60*24*30 < age_min <= 60*24*365: # 30天-1年
                score_a = 1
            else:                                  # >1年
                score_a = 0
            
            # 4. 通道评分 T (0-2分) - 能到这里说明已经突破上轨
            score_t = 2
            
            # 5. 动量评分 M (0-2分)
            if len(df) < 5:
                score_m = 0
            elif len(df) < 25:
                # 新币数据不足25根，使用现有数据计算短期动量
                available_periods = len(df) - 1
                chg_short = (df['c'].iloc[-1] / df['c'].iloc[-available_periods] - 1) * 100
                high_short = df['h'].iloc[-available_periods:].max()
                low_short = df['l'].iloc[-available_periods:].min()
                retrace = (high_short - low_short) / df['c'].iloc[-available_periods] * 100
                
                if retrace < 0.1:  # 避免除零
                    score_m = 1 if chg_short > 0 else 0
                else:
                    ratio = chg_short / retrace
                    if chg_short > 0:
                        score_m = 2 if ratio >= 1.0 else 1 if ratio >= 0.3 else 0
                    else:
                        score_m = 0
            else:
                # 正常24小时动量计算
                chg24 = (df['c'].iloc[-1] / df['c'].iloc[-25] - 1) * 100
                high_24h = df['h'].iloc[-25:].max()
                low_24h = df['l'].iloc[-25:].min()
                retrace = (high_24h - low_24h) / df['c'].iloc[-25] * 100
                
                if retrace < 0.1:  # 避免除零
                    score_m = 0
                else:
                    ratio = chg24 / retrace
                    if chg24 > 0:
                        score_m = 2 if ratio >= 1.5 else 1 if ratio >= 0.5 else 0
                    else:
                        score_m = 0
            
            # 6. 计算总分
            total_score = score_m * 3 + score_l * 2 + score_v * 1 + score_a * 2 + score_t * 2
            
            # 7. 基础保护：只要深度 ≥ 200k$ 且总分为 0 → 保底 1 分
            if total_score == 0 and depth_value >= 2e5:
                total_score = 1
            
            # 返回详细评分信息
            return {
                'M': f"{score_m}*3={score_m*3}",
                'L': f"{score_l}*2={score_l*2}", 
                'V': f"{score_v}*1={score_v*1}",
                'A': f"{score_a}*2={score_a*2}",
                'T': f"{score_t}*2={score_t*2}",
                'total': total_score
            }
            
        except Exception as e:
            logger.error(f"{symbol} 评分计算失败: {str(e)}")
            return {'T': '0*2=0', 'total': 0}
    
    def get_score_breakdown(self, df, age_days):
        """获取评分详细分解（模仿旧版策略格式）"""
        try:
            # 这里应该实现具体的评分逻辑，模仿旧版策略的M、L、V、A、T评分
            # M: 动量分数 (0-3分)
            # L: 流动性分数 (0-2分) 
            # V: 成交量分数 (0-1分)
            # A: ATR分数 (0-2分)
            # T: 技术分析分数 (0-2分)
            
            momentum_score = min(3, max(0, int((df['c'].iloc[-1] / df['c'].iloc[-20] - 1) * 100)))  # 简化动量计算
            liquidity_score = min(2, max(0, int(df['v'].tail(5).mean() / df['v'].mean())))  # 简化流动性计算
            volume_score = 1 if df['v'].iloc[-1] > df['v'].tail(10).mean() else 0  # 成交量比较
            atr_score = 2  # 默认ATR分数
            technical_score = 2  # 默认技术分析分数
            
            return {
                'M': f"{momentum_score}*3={momentum_score*3}",
                'L': f"{liquidity_score}*2={liquidity_score*2}", 
                'V': f"{volume_score}*1={volume_score*1}",
                'A': f"{atr_score}*2={atr_score*2}",
                'T': f"{technical_score}*2={technical_score*2}"
            }
            
        except Exception as e:
            logger.error(f"评分分解计算失败: {str(e)}")
            return {'T': '0*2=0'}

    def analyze_single_symbol(self, symbol):
        """分析单个币种的详细信息"""
        try:
            logger.info(f"开始分析 {symbol}")
            
            # 获取币龄
            age_days = self.get_symbol_age_days(symbol)
            if age_days is None:
                logger.warning(f"{symbol}: 无法获取币龄")
                return None
            
            logger.info(f"{symbol} 使用onboardDate计算币龄: {age_days}天")
            
            # 计算动态时间周期 - 修复：使用固定的15分钟间隔
            interval = '15m'  # 修复：不再使用dynamic_tf_for_channel的返回值作为时间间隔
            logger.info(f"{symbol}配置:上市{age_days}天->时间周期:{interval}")
            
            # 获取K线数据
            df = self.get_klines_data(symbol, interval, 200)
            if df is None or df.empty or len(df) < 50:
                logger.warning(f"{symbol}: 无法获取K线数据 - 时间框架: {interval}")
                return None
            
            # 计算通道位置
            position, upper, middle, lower = self.calculate_channel_position(df, age_days)
            current_price = df['c'].iloc[-1]
            logger.info(f"{symbol}|价格:{current_price:.4f}|周期:{interval}|状态:{position}|上轨:{upper:.4f}|下轨:{lower:.4f}")
            
            # 检查通道突破条件
            breakthrough_ok, breakthrough_reason = self.check_breakthrough_conditions(df, age_days)
            breakthrough_status = "通过" if breakthrough_ok else "未通过"
            
            if not breakthrough_ok:
                logger.info(f"{symbol}: 未通过通道突破检查，跳过")
                return {
                    'symbol': symbol,
                    'age_days': age_days,
                    'timeframe': interval,
                    'current_price': current_price,
                    'channel_position': position,
                    'upper_band': upper,
                    'middle_band': middle,
                    'lower_band': lower,
                    'breakthrough_status': breakthrough_status,
                    'score': 0,
                    'score_details': {},
                    'ready_to_open': False
                }
            
            logger.info(f"{symbol}: 通过通道突破检查，继续评分")
            
            # 计算评分
            score = self.score_calculator.calculate_comprehensive_score(df, age_days)
            
            # 获取深度信息
            depth = self.get_depth_info(symbol)
            
            # 计算24小时涨跌幅
            if len(df) >= 25:
                price_24h_ago = df['c'].iloc[-25]
                change_24h = (current_price - price_24h_ago) / price_24h_ago * 100
            else:
                change_24h = 0
            
            # 计算成交量变化
            if len(df) >= 50:
                vol_recent = df['v'].iloc[-25:].mean()
                vol_previous = df['v'].iloc[-50:-25].mean()
                vol_change = (vol_recent - vol_previous) / vol_previous * 100 if vol_previous > 0 else 0
            else:
                vol_change = 0
            
            # 判断是否准备开仓
            ready_to_open = score >= 7 and breakthrough_ok
            
            result = {
                'symbol': symbol,
                'score': score,
                'age_days': age_days,
                'current_price': current_price,
                'change_24h': change_24h,
                'channel_position': position,
                'upper_band': upper,
                'middle_band': middle,
                'lower_band': lower,
                'breakthrough_ok': breakthrough_ok,
                'breakthrough_reason': breakthrough_reason,
                'depth_01pct': depth,
                'volume_change': vol_change,
                'timeframe': interval,
                'meets_entry_conditions': ready_to_open,
                'ready_to_open': ready_to_open
            }
            
            logger.info(f"{symbol} 分析完成: 评分={score:.1f}, 深度={depth:.0f}$")
            return result
            
        except Exception as e:
            logger.error(f"{symbol} 分析异常: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def analyze_candidate_pool(self):
        """分析整个候选池"""
        try:
            # 加载候选池
            candidates = self.load_candidate_cache()
            if not candidates:
                return {
                    'error': '候选池为空或加载失败',
                    'candidates': []
                }
            
            self.log.info(f"开始分析 {len(candidates)} 个候选币种...")
            
            # 使用并行处理分析币种
            analyzed_candidates = []
            
            # 创建线程池进行并行处理
            with ThreadPoolExecutor(max_workers=10) as executor:
                # 提交所有任务
                future_to_symbol = {
                    executor.submit(self.analyze_single_symbol, symbol): symbol 
                    for symbol in candidates
                }
                
                # 收集结果
                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        result = future.result(timeout=30)  # 30秒超时
                        if result:
                            analyzed_candidates.append(result)
                            
                            self.log.info(f"{symbol}: 评分={result.get('score', 0):.1f}, "
                                        f"通道位置={result.get('channel_position', 'N/A')}, "
                                        f"突破状态={result.get('breakthrough_ok', False)}")
                        else:
                            self.log.warning(f"{symbol}: 分析失败")
                            
                    except Exception as e:
                        self.log.error(f"{symbol}: 分析异常 - {str(e)}")
            
            # 统计信息
            valid_candidates = [c for c in analyzed_candidates if 'error' not in c]
            high_score_candidates = [c for c in valid_candidates if c.get('score', 0) >= 7]
            breakthrough_candidates = [c for c in valid_candidates if c.get('breakthrough_ok', False)]
            entry_ready_candidates = [c for c in valid_candidates if c.get('meets_entry_conditions', False)]
            
            # 按评分排序
            valid_candidates.sort(key=lambda x: x.get('score', 0), reverse=True)
            
            self.log.info(f"并行分析完成: {len(valid_candidates)}个成功/{len(candidates)}个处理")
            
            return {
                'total_candidates': len(candidates),
                'valid_analyses': len(valid_candidates),
                'high_score_count': len(high_score_candidates),
                'breakthrough_count': len(breakthrough_candidates),
                'entry_ready_count': len(entry_ready_candidates),
                'candidates': valid_candidates,
                'high_score_candidates': high_score_candidates,
                'breakthrough_candidates': breakthrough_candidates,
                'entry_ready_candidates': entry_ready_candidates
            }
            
        except Exception as e:
            self.log.error(f"分析候选池失败: {e}")
            return {
                'error': str(e),
                'candidates': []
            }
    
    def generate_detailed_report(self, analysis_result):
        """生成详细分析报告"""
        try:
            if 'error' in analysis_result:
                return f"分析失败: {analysis_result['error']}"
            
            report = []
            report.append("=" * 80)
            report.append("候选池详细分析报告")
            report.append("=" * 80)
            report.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("")
            
            # 总体统计
            report.append("📊 总体统计:")
            report.append(f"  候选池总数: {analysis_result['total_candidates']}")
            report.append(f"  成功分析: {analysis_result['valid_analyses']}")
            report.append(f"  高评分币种(≥7分): {analysis_result['high_score_count']}")
            report.append(f"  通道突破币种: {analysis_result['breakthrough_count']}")
            report.append(f"  符合开仓条件: {analysis_result['entry_ready_count']}")
            report.append("")
            
            # 符合开仓条件的币种
            if analysis_result['entry_ready_candidates']:
                report.append("🚀 符合开仓条件的币种:")
                report.append("-" * 60)
                for candidate in analysis_result['entry_ready_candidates']:
                    report.append(f"  {candidate['symbol']:<12} | 评分: {candidate['score']:.1f} | "
                                f"位置: {candidate['channel_position']:<8} | "
                                f"24h涨幅: {candidate['change_24h']:+6.2f}% | "
                                f"深度: ${candidate['depth_01pct']:,.0f}")
                report.append("")
            
            # 所有候选币种详细信息
            report.append("📋 所有候选币种详细信息:")
            report.append("-" * 120)
            report.append(f"{'币种':<12} | {'评分':<6} | {'通道位置':<10} | {'突破条件':<8} | "
                         f"{'当前价格':<12} | {'24h涨幅':<8} | {'深度($)':<12} | {'币龄(天)':<8} | {'时间框架':<8}")
            report.append("-" * 120)
            
            for candidate in analysis_result['candidates']:
                if 'error' in candidate:
                    report.append(f"{candidate['symbol']:<12} | 错误: {candidate['error']}")
                    continue
                
                breakthrough_status = "✓" if candidate.get('breakthrough_ok', False) else "✗"
                report.append(f"{candidate['symbol']:<12} | "
                             f"{candidate['score']:>6.1f} | "
                             f"{candidate['channel_position']:<10} | "
                             f"{breakthrough_status:>8} | "
                             f"{candidate['current_price']:>12.6f} | "
                             f"{candidate['change_24h']:>+7.2f}% | "
                             f"{candidate['depth_01pct']:>12,.0f} | "
                             f"{candidate['age_days']:>8} | "
                             f"{candidate['timeframe']:>8}")
            
            report.append("")
            
            # 通道位置分布
            position_stats = {}
            for candidate in analysis_result['candidates']:
                if 'error' not in candidate:
                    pos = candidate.get('channel_position', '未知')
                    position_stats[pos] = position_stats.get(pos, 0) + 1
            
            if position_stats:
                report.append("📍 通道位置分布:")
                for position, count in sorted(position_stats.items()):
                    report.append(f"  {position}: {count} 个币种")
                report.append("")
            
            # 评分分布
            score_ranges = {'0-3分': 0, '3-5分': 0, '5-7分': 0, '7-9分': 0, '9分以上': 0}
            for candidate in analysis_result['candidates']:
                if 'error' not in candidate:
                    score = candidate.get('score', 0)
                    if score < 3:
                        score_ranges['0-3分'] += 1
                    elif score < 5:
                        score_ranges['3-5分'] += 1
                    elif score < 7:
                        score_ranges['5-7分'] += 1
                    elif score < 9:
                        score_ranges['7-9分'] += 1
                    else:
                        score_ranges['9分以上'] += 1
            
            report.append("📈 评分分布:")
            for range_name, count in score_ranges.items():
                report.append(f"  {range_name}: {count} 个币种")
            
            report.append("")
            report.append("=" * 80)
            
            return "\n".join(report)
            
        except Exception as e:
            return f"生成报告失败: {e}"

def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 初始化分析器（不需要trader也可以运行基础分析）
        analyzer = CandidatePoolAnalyzer()
        
        print("开始分析候选池...")
        
        # 分析候选池
        result = analyzer.analyze_candidate_pool()
        
        # 生成报告
        report = analyzer.generate_detailed_report(result)
        
        # 输出报告
        print(report)
        
        # 保存报告到文件
        report_file = f"candidate_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == "__main__":
    main()