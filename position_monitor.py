#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
持仓监控模块
实现持仓期间的关键指标监控，包括：
1. 持仓盈亏状态监控（开仓价、当前市价、浮动盈亏百分比）
2. 基础止损到保本止损的自动调整机制
3. 移动止损功能的持续跟踪监控
4. 末位淘汰规则的实时检测功能
"""

import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from trailing_stop_manager import TrailingStopManager, TrailingStopState
from config_validator import ConfigValidator


@dataclass
class PositionInfo:
    """持仓信息"""
    symbol: str
    side: str  # 'LONG' or 'SHORT'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None
    trailing_stop_price: Optional[float] = None
    is_breakeven_adjusted: bool = False
    add_count: int = 0  # 加仓次数
    entry_time: Optional[datetime] = None
    last_update_time: Optional[datetime] = None
    # 新增移动止损状态
    trailing_stop_state: Optional[TrailingStopState] = None


@dataclass
class MonitoringConfig:
    """监控配置"""
    # 保本止损配置
    breakeven_trigger_pct: float = 0.05  # 浮盈5%触发保本止损
    breakeven_stop_loss_pct: float = 0.004  # 保本止损设在成本+0.4%
    
    # 移动止损配置
    trailing_stop_enabled: bool = True
    trailing_stop_ratio: float = 0.01  # 1%移动止损
    trailing_stop_trigger_pct: float = 0.02  # 浮盈2%开始启用移动止损
    
    # 末位淘汰配置
    max_positions: int = 3
    elimination_check_interval: int = 3600  # 1小时检查一次
    
    # 监控频率配置
    position_check_interval: int = 60  # 1分钟检查一次持仓状态
    price_update_interval: int = 30  # 30秒更新一次价格


class PositionMonitor:
    """持仓监控器"""
    
    def __init__(self, trader, config: MonitoringConfig, logger: Optional[logging.Logger] = None):
        self.trader = trader
        self.config = config
        self.log = logger or logging.getLogger(__name__)
        
        # 持仓信息缓存
        self.positions: Dict[str, PositionInfo] = {}
        
        # 监控状态
        self.last_position_check = 0
        self.last_elimination_check = 0
        self.last_price_update = 0
        
        # 统计信息
        self.monitoring_stats = {
            'total_checks': 0,
            'breakeven_adjustments': 0,
            'trailing_stop_updates': 0,
            'elimination_actions': 0,
            'errors': 0,
            'advanced_trailing_stop_updates': 0  # 新增高级移动止损统计
        }
        
        # 初始化移动止损管理器
        try:
            self.trailing_stop_manager = TrailingStopManager()
            self.log.info("移动止损管理器初始化成功")
        except Exception as e:
            self.log.error(f"移动止损管理器初始化失败: {e}")
            self.trailing_stop_manager = None
    
    def update_positions(self) -> bool:
        """更新持仓信息"""
        try:
            current_time = time.time()
            
            # 检查是否需要更新
            if current_time - self.last_position_check < self.config.position_check_interval:
                return True
            
            # 获取当前持仓
            positions = self.trader.http.get('/fapi/v2/positionRisk')
            if not positions:
                self.log.warning("获取持仓信息失败或为空")
                return False
            
            # 更新持仓信息
            active_positions = {}
            for pos in positions:
                if isinstance(pos, dict):
                    size = float(pos.get('positionAmt', 0))
                    if abs(size) > 0.001:  # 有实际持仓
                        symbol = pos.get('symbol', '')
                        entry_price = float(pos.get('entryPrice', 0))
                        unrealized_pnl = float(pos.get('unRealizedProfit', 0))
                        
                        # 获取当前价格
                        current_price = self._get_current_price(symbol)
                        if not current_price:
                            continue
                        
                        # 计算盈亏百分比
                        if entry_price > 0:
                            if size > 0:  # 多头
                                unrealized_pnl_pct = (current_price - entry_price) / entry_price
                            else:  # 空头
                                unrealized_pnl_pct = (entry_price - current_price) / entry_price
                        else:
                            unrealized_pnl_pct = 0
                        
                        # 创建或更新持仓信息
                        if symbol in self.positions:
                            position_info = self.positions[symbol]
                            position_info.current_price = current_price
                            position_info.unrealized_pnl = unrealized_pnl
                            position_info.unrealized_pnl_pct = unrealized_pnl_pct
                            position_info.last_update_time = datetime.now()
                        else:
                            position_info = PositionInfo(
                                symbol=symbol,
                                side='LONG' if size > 0 else 'SHORT',
                                size=abs(size),
                                entry_price=entry_price,
                                current_price=current_price,
                                unrealized_pnl=unrealized_pnl,
                                unrealized_pnl_pct=unrealized_pnl_pct,
                                entry_time=datetime.now(),
                                last_update_time=datetime.now()
                            )
                            
                            # 为新持仓初始化移动止损状态
                            if self.trailing_stop_manager:
                                try:
                                    trailing_state = self.trailing_stop_manager.initialize_position(
                                        symbol=symbol,
                                        side=position_info.side,
                                        entry_price=entry_price,
                                        current_price=current_price
                                    )
                                    position_info.trailing_stop_state = trailing_state
                                    if trailing_state:
                                        self.log.info(f"{symbol} 移动止损状态初始化成功")
                                    else:
                                        self.log.debug(f"{symbol} 移动止损未启用或初始化失败")
                                except Exception as e:
                                    self.log.error(f"{symbol} 移动止损状态初始化失败: {e}")
                        
                        active_positions[symbol] = position_info
            
            # 移除已平仓的持仓
            closed_positions = set(self.positions.keys()) - set(active_positions.keys())
            for symbol in closed_positions:
                self.log.info(f"持仓已平仓: {symbol}")
                del self.positions[symbol]
            
            # 更新持仓缓存
            self.positions = active_positions
            self.last_position_check = current_time
            self.monitoring_stats['total_checks'] += 1
            
            return True
            
        except Exception as e:
            self.log.error(f"更新持仓信息失败: {e}")
            self.monitoring_stats['errors'] += 1
            return False
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            ticker = self.trader.http.get('/fapi/v1/ticker/price', {'symbol': symbol})
            if ticker and isinstance(ticker, dict):
                return float(ticker.get('price', 0))
        except Exception as e:
            self.log.error(f"获取 {symbol} 价格失败: {e}")
        return None
    
    def check_breakeven_stop_adjustment(self) -> List[str]:
        """检查并执行保本止损调整"""
        adjusted_symbols = []
        
        try:
            for symbol, position in self.positions.items():
                # 检查是否已经调整过保本止损
                if position.is_breakeven_adjusted:
                    continue
                
                # 检查是否达到保本止损触发条件（浮盈≥5%）
                if position.unrealized_pnl_pct >= self.config.breakeven_trigger_pct:
                    # 计算保本止损价格
                    if position.side == 'LONG':
                        breakeven_price = position.entry_price * (1 + self.config.breakeven_stop_loss_pct)
                    else:  # SHORT
                        breakeven_price = position.entry_price * (1 - self.config.breakeven_stop_loss_pct)
                    
                    # 执行保本止损调整
                    if self._adjust_stop_loss(symbol, breakeven_price, "保本止损"):
                        position.is_breakeven_adjusted = True
                        position.stop_loss_price = breakeven_price
                        adjusted_symbols.append(symbol)
                        self.monitoring_stats['breakeven_adjustments'] += 1
                        
                        self.log.info(f"{symbol} 保本止损调整: 浮盈 {position.unrealized_pnl_pct:.2%}, "
                                    f"止损价调整至 {breakeven_price:.6f}")
        
        except Exception as e:
            self.log.error(f"保本止损调整检查失败: {e}")
            self.monitoring_stats['errors'] += 1
        
        return adjusted_symbols
    
    def check_trailing_stop_update(self) -> List[str]:
        """检查并更新移动止损（保留原有简单逻辑）"""
        updated_symbols = []
        
        if not self.config.trailing_stop_enabled:
            return updated_symbols
        
        try:
            for symbol, position in self.positions.items():
                # 检查是否达到移动止损启用条件（浮盈≥2%）
                if position.unrealized_pnl_pct < self.config.trailing_stop_trigger_pct:
                    continue
                
                # 计算移动止损价格
                if position.side == 'LONG':
                    new_trailing_price = position.current_price * (1 - self.config.trailing_stop_ratio)
                else:  # SHORT
                    new_trailing_price = position.current_price * (1 + self.config.trailing_stop_ratio)
                
                # 检查是否需要更新移动止损
                should_update = False
                if position.trailing_stop_price is None:
                    should_update = True
                elif position.side == 'LONG' and new_trailing_price > position.trailing_stop_price:
                    should_update = True
                elif position.side == 'SHORT' and new_trailing_price < position.trailing_stop_price:
                    should_update = True
                
                if should_update:
                    # 更新移动止损
                    if self._adjust_stop_loss(symbol, new_trailing_price, "移动止损"):
                        old_price = position.trailing_stop_price
                        position.trailing_stop_price = new_trailing_price
                        position.stop_loss_price = new_trailing_price
                        updated_symbols.append(symbol)
                        self.monitoring_stats['trailing_stop_updates'] += 1
                        
                        self.log.info(f"{symbol} 移动止损更新: {old_price:.6f} -> {new_trailing_price:.6f}, "
                                    f"当前价格 {position.current_price:.6f}")
        
        except Exception as e:
            self.log.error(f"移动止损更新检查失败: {e}")
            self.monitoring_stats['errors'] += 1
        
        return updated_symbols
    
    def check_advanced_trailing_stop_update(self) -> List[str]:
        """检查并更新高级移动止损（新增功能）"""
        updated_symbols = []
        
        # 检查移动止损管理器是否可用
        if not self.trailing_stop_manager:
            return updated_symbols
        
        try:
            for symbol, position in self.positions.items():
                # 跳过没有移动止损状态的持仓
                if not position.trailing_stop_state:
                    continue
                
                # 更新移动止损
                try:
                    # 获取价格历史（简化版本，实际应从缓存获取）
                    price_history = [position.current_price] * 10  # 临时实现
                    
                    updated, new_stop_price = self.trailing_stop_manager.update_trailing_stop(
                        state=position.trailing_stop_state,
                        current_price=position.current_price,
                        price_history=price_history
                    )
                    
                    if updated and new_stop_price:
                        # 执行止损价格调整
                        if self._adjust_stop_loss(symbol, new_stop_price, "高级移动止损"):
                            position.stop_loss_price = new_stop_price
                            updated_symbols.append(symbol)
                            self.monitoring_stats['advanced_trailing_stop_updates'] += 1
                            
                            self.log.info(f"{symbol} 高级移动止损更新成功: {new_stop_price:.6f}")
                
                except Exception as e:
                    self.log.error(f"{symbol} 高级移动止损更新失败: {e}")
        
        except Exception as e:
            self.log.error(f"高级移动止损检查失败: {e}")
            self.monitoring_stats['errors'] += 1
        
        return updated_symbols
    
    def check_elimination_rule(self) -> List[str]:
        """检查末位淘汰规则"""
        eliminated_symbols = []
        
        try:
            current_time = time.time()
            
            # 检查是否需要执行末位淘汰检查
            if current_time - self.last_elimination_check < self.config.elimination_check_interval:
                return eliminated_symbols
            
            # 如果持仓数量超过限制，执行末位淘汰
            if len(self.positions) > self.config.max_positions:
                # 按盈亏百分比排序，淘汰表现最差的
                sorted_positions = sorted(
                    self.positions.items(),
                    key=lambda x: x[1].unrealized_pnl_pct,
                    reverse=True
                )
                
                # 保留前N名，淘汰其余
                positions_to_eliminate = sorted_positions[self.config.max_positions:]
                
                for symbol, position in positions_to_eliminate:
                    if self._close_position(symbol, "末位淘汰"):
                        eliminated_symbols.append(symbol)
                        self.monitoring_stats['elimination_actions'] += 1
                        
                        self.log.warning(f"末位淘汰: {symbol} (盈亏: {position.unrealized_pnl_pct:.2%})")
            
            self.last_elimination_check = current_time
        
        except Exception as e:
            self.log.error(f"末位淘汰检查失败: {e}")
            self.monitoring_stats['errors'] += 1
        
        return eliminated_symbols
    
    def _adjust_stop_loss(self, symbol: str, stop_price: float, reason: str) -> bool:
        """调整止损价格"""
        try:
            # 获取当前挂单
            open_orders = self.trader.http.get('/fapi/v1/openOrders', {'symbol': symbol})
            if not open_orders:
                return False
            
            # 取消现有止损单
            stop_orders = [o for o in open_orders if o.get('type') in ['STOP_MARKET', 'TAKE_PROFIT_MARKET']]
            for order in stop_orders:
                try:
                    self.trader.http.delete('/fapi/v1/order', {
                        'symbol': symbol,
                        'orderId': order['orderId']
                    })
                except Exception as e:
                    self.log.warning(f"取消止损单失败: {symbol} {order['orderId']}, {e}")
            
            # 挂新的止损单
            position = self.positions.get(symbol)
            if not position:
                return False
            
            side = 'SELL' if position.side == 'LONG' else 'BUY'
            
            order_params = {
                'symbol': symbol,
                'side': side,
                'type': 'STOP_MARKET',
                'quantity': str(position.size),
                'stopPrice': str(stop_price),
                'timeInForce': 'GTC',
                'reduceOnly': 'true'
            }
            
            result = self.trader.http.post('/fapi/v1/order', order_params)
            if result and result.get('orderId'):
                self.log.info(f"{reason}止损单已挂: {symbol} @ {stop_price:.6f}")
                return True
            
        except Exception as e:
            self.log.error(f"调整止损失败: {symbol}, {e}")
        
        return False
    
    def _close_position(self, symbol: str, reason: str) -> bool:
        """平仓"""
        try:
            position = self.positions.get(symbol)
            if not position:
                return False
            
            side = 'SELL' if position.side == 'LONG' else 'BUY'
            
            order_params = {
                'symbol': symbol,
                'side': side,
                'type': 'MARKET',
                'quantity': str(position.size),
                'reduceOnly': 'true'
            }
            
            result = self.trader.http.post('/fapi/v1/order', order_params)
            if result and result.get('orderId'):
                self.log.info(f"{reason}平仓: {symbol}, 数量: {position.size}")
                return True
            
        except Exception as e:
            self.log.error(f"平仓失败: {symbol}, {e}")
        
        return False
    
    def get_position_summary(self) -> Dict:
        """获取持仓摘要信息"""
        if not self.positions:
            return {
                'total_positions': 0,
                'total_unrealized_pnl': 0,
                'avg_pnl_pct': 0,
                'positions': []
            }
        
        total_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        avg_pnl_pct = sum(pos.unrealized_pnl_pct for pos in self.positions.values()) / len(self.positions)
        
        positions_info = []
        for symbol, pos in self.positions.items():
            positions_info.append({
                'symbol': symbol,
                'side': pos.side,
                'size': pos.size,
                'entry_price': pos.entry_price,
                'current_price': pos.current_price,
                'unrealized_pnl': pos.unrealized_pnl,
                'unrealized_pnl_pct': pos.unrealized_pnl_pct,
                'is_breakeven_adjusted': pos.is_breakeven_adjusted,
                'has_trailing_stop': pos.trailing_stop_price is not None
            })
        
        return {
            'total_positions': len(self.positions),
            'total_unrealized_pnl': total_pnl,
            'avg_pnl_pct': avg_pnl_pct,
            'positions': positions_info,
            'monitoring_stats': self.monitoring_stats
        }
    
    def run_monitoring_cycle(self) -> Dict:
        """执行一次完整的监控周期"""
        results = {
            'position_updated': False,
            'breakeven_adjusted': [],
            'trailing_stop_updated': [],
            'advanced_trailing_stop_updated': [],  # 新增高级移动止损结果
            'eliminated': [],
            'errors': []
        }
        
        try:
            # 1. 更新持仓信息
            if self.update_positions():
                results['position_updated'] = True
                
                # 2. 检查保本止损调整
                results['breakeven_adjusted'] = self.check_breakeven_stop_adjustment()
                
                # 3. 检查移动止损更新（原有简单逻辑）
                results['trailing_stop_updated'] = self.check_trailing_stop_update()
                
                # 4. 检查高级移动止损更新（新增功能）
                results['advanced_trailing_stop_updated'] = self.check_advanced_trailing_stop_update()
                
                # 5. 检查末位淘汰规则
                results['eliminated'] = self.check_elimination_rule()
            
        except Exception as e:
            error_msg = f"监控周期执行失败: {e}"
            self.log.error(error_msg)
            results['errors'].append(error_msg)
            self.monitoring_stats['errors'] += 1
        
        return results