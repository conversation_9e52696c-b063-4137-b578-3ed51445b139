"""
批次处理集成模块
整合监控、可视化和配置管理功能
"""

import os
import sys
import time
import logging
import threading
from typing import Optional, List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from batch_config import BatchConfig, ConfigTemplates
from batch_monitor import BatchMonitor
from batch_visualizer import BatchVisualizer

class BatchProcessingSystem:
    """批次处理系统集成类"""
    
    def __init__(self, config_file: str = "batch_config.json"):
        # 加载配置
        self.config = BatchConfig.load_from_file(config_file)
        
        # 设置日志
        self._setup_logging()
        
        # 初始化组件
        self.monitor = BatchMonitor(self.config)
        self.visualizer = BatchVisualizer(self.monitor, self.config.display_update_interval)
        
        # 系统状态
        self.system_active = False
        self.integration_thread = None
        
        self.log = logging.getLogger("BatchSystem")
        self.log.info("批次处理系统初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def start_system(self):
        """启动批次处理系统"""
        if self.system_active:
            self.log.warning("系统已经在运行中")
            return
        
        self.system_active = True
        
        # 启动监控器（监控线程已在构造函数中启动）
        # self.monitor.start_monitoring()  # 监控已在构造函数中启动
        
        # 启动可视化（如果启用）
        if self.config.enable_console_display:
            self.visualizer.start_display()
        
        # 启动集成监控线程
        self.integration_thread = threading.Thread(target=self._integration_loop, daemon=True)
        self.integration_thread.start()
        
        self.log.info("批次处理系统已启动")
        
        # 打印启动信息
        self._print_startup_info()
    
    def stop_system(self):
        """停止批次处理系统"""
        if not self.system_active:
            return
        
        self.system_active = False
        
        # 停止可视化
        self.visualizer.stop_display()
        
        # 停止监控器
        self.monitor.stop_monitoring()
        
        # 等待集成线程结束
        if self.integration_thread and self.integration_thread.is_alive():
            self.integration_thread.join(timeout=5)
        
        self.log.info("批次处理系统已停止")
    
    def _integration_loop(self):
        """集成监控循环"""
        while self.system_active:
            try:
                # 检查系统健康状态
                self._check_system_health()
                
                # 生成HTML报告（如果启用）
                if self.config.enable_html_report:
                    self._generate_html_report()
                
                # 等待下次检查
                time.sleep(self.config.html_refresh_interval)
                
            except Exception as e:
                self.log.error(f"集成监控循环异常: {e}")
                time.sleep(10)
    
    def _check_system_health(self):
        """检查系统健康状态"""
        try:
            # 检查内存使用
            import psutil
            memory_percent = psutil.virtual_memory().percent / 100
            if memory_percent > self.config.memory_threshold:
                self.log.warning(f"内存使用率过高: {memory_percent:.1%}")
            
            # 检查CPU使用
            cpu_percent = psutil.cpu_percent(interval=1) / 100
            if cpu_percent > self.config.cpu_threshold:
                self.log.warning(f"CPU使用率过高: {cpu_percent:.1%}")
            
        except ImportError:
            # psutil未安装，跳过系统资源检查
            pass
        except Exception as e:
            self.log.error(f"系统健康检查异常: {e}")
    
    def _generate_html_report(self):
        """生成HTML报告"""
        try:
            html_file = self.visualizer.generate_html_report()
            self.log.debug(f"HTML报告已生成: {html_file}")
        except Exception as e:
            self.log.error(f"HTML报告生成失败: {e}")
    
    def _print_startup_info(self):
        """打印启动信息"""
        print("\n" + "="*80)
        print("🚀 批次处理系统启动成功")
        print("="*80)
        print(f"📋 配置文件: {self.config.log_file}")
        print(f"📊 监控状态: {'✅ 启用' if self.config.enable_console_display else '❌ 禁用'}")
        print(f"📈 HTML报告: {'✅ 启用' if self.config.enable_html_report else '❌ 禁用'}")
        print(f"🔍 详细日志: {'✅ 启用' if self.config.enable_detailed_logging else '❌ 禁用'}")
        print("="*80)
        print("💡 使用说明:")
        print("   • 调用 start_batch_scan(symbols) 开始批次扫描")
        print("   • 调用 get_progress_status() 查看进度状态")
        print("   • 调用 stop_system() 停止系统")
        print("   • 查看 batch_progress.html 获取详细报告")
        print("="*80)
    
    # 批次处理接口方法
    def start_batch_scan(self, symbols: List[str]) -> str:
        """开始批次扫描"""
        if not self.system_active:
            raise RuntimeError("系统未启动，请先调用 start_system()")
        
        cycle_id = self.monitor.start_scan_cycle(symbols)
        self.log.info(f"开始批次扫描: {cycle_id}, 币种数量: {len(symbols)}")
        return cycle_id
    
    def start_batch_processing(self, cycle_id: str, batch_index: int, symbols: List[str]) -> str:
        """开始批次处理"""
        batch_id = self.monitor.start_batch(batch_index)
        if batch_id:
            self.log.debug(f"开始批次处理: {batch_id}")
        return batch_id
    
    def complete_batch_processing(self, batch_id: str, processed_symbols: List[str], 
                                 failed_symbols: List[str] = None):
        """完成批次处理"""
        if not batch_id:
            self.log.error("批次ID为空，无法完成批次处理")
            return
            
        # 从batch_id中提取batch_index
        try:
            batch_index = int(batch_id.split('_')[-1]) - 1  # 批次ID从1开始，索引从0开始
            self.monitor.complete_batch(batch_index, processed_symbols, failed_symbols or [])
            self.log.debug(f"完成批次处理: {batch_id}")
        except (ValueError, IndexError):
            self.log.error(f"无效的批次ID格式: {batch_id}")
    
    def complete_scan_cycle(self, cycle_id: str):
        """完成扫描周期"""
        # 直接调用监控器的周期完成检查
        self.monitor._check_cycle_completion()
        self.log.debug(f"完成扫描周期: {cycle_id}")
    
    def get_progress_status(self) -> Dict[str, Any]:
        """获取进度状态"""
        return self.monitor.get_progress_status()
    
    def get_scan_statistics(self) -> Dict[str, Any]:
        """获取扫描统计"""
        return self.monitor.get_scan_statistics()
    
    def export_progress_report(self, filepath: str = None) -> str:
        """导出进度报告"""
        return self.monitor.export_progress_report(filepath)
    
    # 配置管理方法
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                self.log.info(f"配置已更新: {key} = {value}")
            else:
                self.log.warning(f"未知配置项: {key}")
        
        # 验证配置
        if not self.config.validate():
            self.log.error("配置验证失败")
            return False
        
        # 保存配置
        self.config.save_to_file("batch_config.json")
        return True
    
    def reload_config(self, config_file: str = "batch_config.json"):
        """重新加载配置"""
        try:
            new_config = BatchConfig.load_from_file(config_file)
            if new_config.validate():
                self.config = new_config
                self.log.info(f"配置已重新加载: {config_file}")
                return True
            else:
                self.log.error("新配置验证失败")
                return False
        except Exception as e:
            self.log.error(f"配置重新加载失败: {e}")
            return False
    
    def get_config_templates(self) -> Dict[str, BatchConfig]:
        """获取配置模板"""
        return {
            "fast_scan": ConfigTemplates.get_fast_scan_config(),
            "stable_scan": ConfigTemplates.get_stable_scan_config(),
            "conservative_scan": ConfigTemplates.get_conservative_scan_config()
        }
    
    def apply_config_template(self, template_name: str) -> bool:
        """应用配置模板"""
        templates = self.get_config_templates()
        if template_name not in templates:
            self.log.error(f"未知配置模板: {template_name}")
            return False
        
        self.config = templates[template_name]
        self.config.save_to_file("batch_config.json")
        self.log.info(f"已应用配置模板: {template_name}")
        return True


# 便捷函数
def create_batch_system(config_template: str = "stable_scan") -> BatchProcessingSystem:
    """创建批次处理系统"""
    system = BatchProcessingSystem()
    
    # 应用配置模板
    if config_template != "default":
        system.apply_config_template(config_template)
    
    return system

def quick_start_monitoring():
    """快速启动监控系统"""
    system = create_batch_system("stable_scan")
    system.start_system()
    
    print("\n批次处理监控系统已启动！")
    print("按 Ctrl+C 停止系统")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止系统...")
        system.stop_system()
        print("系统已停止")


if __name__ == "__main__":
    # 测试系统
    import argparse
    
    parser = argparse.ArgumentParser(description="批次处理系统")
    parser.add_argument("--template", default="stable_scan", 
                       choices=["fast_scan", "stable_scan", "conservative_scan"],
                       help="配置模板")
    parser.add_argument("--quick-start", action="store_true", help="快速启动监控")
    
    args = parser.parse_args()
    
    if args.quick_start:
        quick_start_monitoring()
    else:
        # 创建并测试系统
        system = create_batch_system(args.template)
        system.start_system()
        
        # 模拟批次处理
        test_symbols = [f"SYMBOL{i}USDT" for i in range(1, 101)]
        cycle_id = system.start_batch_scan(test_symbols)
        
        print(f"测试扫描周期已启动: {cycle_id}")
        print("查看 batch_progress.html 获取详细进度")
        
        # 运行一段时间后停止
        time.sleep(30)
        system.stop_system()