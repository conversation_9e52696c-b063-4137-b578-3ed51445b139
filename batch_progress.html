
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次处理进度报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .status-card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
        .batch-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .batch-table th, .batch-table td { padding: 10px; text-align: left; border-bottom: 1px solid #dee2e6; }
        .batch-table th { background-color: #007bff; color: white; }
        .status-badge { padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; }
        .status-completed { background-color: #28a745; }
        .status-processing { background-color: #ffc107; color: #212529; }
        .status-failed { background-color: #dc3545; }
        .status-pending { background-color: #6c757d; }
        .alert { padding: 15px; margin: 10px 0; border-radius: 4px; }
        .alert-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .timestamp { text-align: center; color: #6c757d; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 批次处理进度监控</h1>
            <p>实时监控批次扫描状态和处理进度</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>📊 当前状态</h3>
                <p><strong>扫描周期:</strong> cycle_1758859871</p>
                <p><strong>运行状态:</strong> active</p>
                <p><strong>运行时间:</strong> 1秒</p>
            </div>
            
            <div class="status-card">
                <h3>🔢 处理统计</h3>
                <p><strong>总币种:</strong> 20</p>
                <p><strong>已处理:</strong> 0</p>
                <p><strong>达标币种:</strong> 0</p>
            </div>
            
            <div class="status-card">
                <h3>📈 批次进度</h3>
                <p><strong>总批次:</strong> 1</p>
                <p><strong>已完成:</strong> 0</p>
                <p><strong>处理中:</strong> 0</p>
                <p><strong>失败:</strong> 0</p>
            </div>
            
            <div class="status-card">
                <h3>📊 历史统计</h3>
                <p><strong>总周期:</strong> 1</p>
                <p><strong>完成周期:</strong> 0</p>
                <p><strong>平均时间:</strong> 0秒</p>
            </div>
        </div>
        
        <div class="progress-section">
            <h3>📈 总体进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0.0%"></div>
            </div>
            <p style="text-align: center;">0.0% 完成</p>
        </div>
        
        
        
        
        
        <div class="timestamp">
            <p>报告生成时间: 2025-09-26 12:11:12</p>
            <p>自动刷新间隔: 10秒</p>
        </div>
    </div>
    
    <script>
        // 自动刷新页面
        setTimeout(function() {
            location.reload();
        }, 10000);
    </script>
</body>
</html>
        