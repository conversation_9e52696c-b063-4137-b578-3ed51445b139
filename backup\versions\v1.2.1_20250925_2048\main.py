import yaml
import logging
import warnings
import urllib3
import time
from binance_trader import BinanceTrader
from strategy.maker_channel import MakerChannelStrategy
from cache_manager import CacheManager

# 完全禁用所有警告
warnings.filterwarnings("ignore")
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置详细日志格式（控制台+文件）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler('strategy.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 设置ReferenceFolder相关日志器的级别为DEBUG以显示评分详情
logging.getLogger('ReferenceFolder.symbol_scorer').setLevel(logging.DEBUG)

def main():
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    trader = BinanceTrader(cfg, strategy_instance=None)
    cache = CacheManager()
    
    # 冷启动：初始化全币种基础信息
    logging.info("执行冷启动：加载全币种基础信息...")
    symbols_info = cache.load_symbols(trader)
    logging.info(f"冷启动完成，加载 {len(symbols_info)} 个交易对信息")
    
    # 初始化策略
    strategy = MakerChannelStrategy(trader, cfg)
    
    # 执行启动预热扫描
    logging.info("执行启动预热扫描（涨幅榜前25 + 成交量榜前25）...")
    strategy.warmup()
    
    # 主循环使用完整的策略循环
    logging.info("启动策略主循环...")
    strategy.loop()

if __name__ == '__main__':
    main()