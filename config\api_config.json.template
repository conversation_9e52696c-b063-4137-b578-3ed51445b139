{"binance": {"api_key": "${BINANCE_API_KEY}", "api_secret": "${BINANCE_API_SECRET}", "testnet": false, "base_url": "https://fapi.binance.com", "testnet_url": "https://testnet.binancefuture.com", "timeout": 30, "retry_count": 3, "retry_delay": 1, "rate_limit": {"requests_per_minute": 1200, "orders_per_second": 10, "orders_per_day": 200000}}, "endpoints": {"account_info": "/fapi/v2/account", "balance": "/fapi/v2/balance", "positions": "/fapi/v2/positionRisk", "orders": "/fapi/v1/order", "open_orders": "/fapi/v1/openOrders", "order_history": "/fapi/v1/allOrders", "trades": "/fapi/v1/userTrades", "klines": "/fapi/v1/klines", "ticker": "/fapi/v1/ticker/24hr", "depth": "/fapi/v1/depth", "exchange_info": "/fapi/v1/exchangeInfo"}, "websocket": {"enabled": true, "base_url": "wss://fstream.binance.com", "testnet_url": "wss://stream.binancefuture.com", "reconnect": true, "reconnect_delay": 5, "max_reconnect_attempts": 10, "ping_interval": 30, "ping_timeout": 10}, "security": {"signature_required": true, "timestamp_window": 5000, "recv_window": 5000, "ip_whitelist": [], "permissions": ["spot", "futures", "margin"]}, "error_handling": {"retry_on_errors": [-1001, -1003, -1021, -2013, -2014, -2015], "fatal_errors": [-1022, -2010, -2011, -2019], "backoff_strategy": "exponential", "max_backoff_time": 60}, "logging": {"log_requests": true, "log_responses": false, "log_errors": true, "mask_sensitive": true, "sensitive_fields": ["api_key", "api_secret", "signature"]}, "monitoring": {"enabled": true, "metrics": ["request_count", "response_time", "error_rate", "rate_limit_usage"], "alert_thresholds": {"response_time": 5000, "error_rate": 0.05, "rate_limit_usage": 0.8}}}