#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的K线数据测试脚本
"""

import sys
import json
import time

def simple_test():
    """简化测试"""
    print("开始简化测试...")
    
    try:
        # 导入模块
        from binance_trader import BinanceTrader
        
        # 加载配置
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 初始化交易器
        trader = BinanceTrader(config)
        print("BinanceTrader初始化成功")
        
        # 测试几个币种
        symbols = ['BTCUSDT', 'ETHUSDT', 'ALPINEUSDT', 'BCHUSDT']
        
        for symbol in symbols:
            try:
                klines = trader.get_klines(symbol, '15m', 10)
                if klines:
                    print(f"{symbol}: 成功获取 {len(klines)} 条K线数据")
                else:
                    print(f"{symbol}: 获取失败")
            except Exception as e:
                print(f"{symbol}: 异常 - {e}")
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()