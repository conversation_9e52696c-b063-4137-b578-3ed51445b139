#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试递归修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from binance_trader import BinanceTrader
from config_manager import ConfigManager
import time

def test_recursion_fix():
    """测试递归修复效果"""
    print("开始测试递归修复效果...")
    
    try:
        # 加载配置
        print("1. 加载配置...")
        config_manager = ConfigManager()
        config = config_manager.get_config('main')  # 获取主配置
        
        # 初始化交易器
        print("2. 初始化BinanceTrader...")
        trader = BinanceTrader(config)
        print(f"   成功加载 {len(trader.symbols)} 个永续交易对")
        
        # 测试公共API - K线数据获取（不需要签名）
        print("3. 测试公共API - K线数据获取...")
        klines = trader.get_klines("BTCUSDT", "1h", limit=5)
        if klines and len(klines) > 0:
            print(f"   成功获取 {len(klines)} 条K线数据")
        else:
            print("   警告：未获取到K线数据")
        
        # 测试私有API - 设置杠杆（需要签名）
        print("4. 测试私有API - 设置杠杆...")
        try:
            result = trader.set_leverage("BTCUSDT", 1)
            print(f"   成功设置杠杆: {result}")
        except Exception as e:
            print(f"   设置杠杆失败（可能是权限问题）: {e}")
        
        # 测试私有API - 获取账户余额（需要签名）
        print("5. 测试私有API - 获取账户余额...")
        try:
            balance = trader.get_account_balance()
            print(f"   成功获取账户余额: {len(balance) if balance else 0} 个币种")
        except Exception as e:
            print(f"   获取账户余额失败（可能是权限问题）: {e}")
        
        print("\n✅ 测试完成！没有发生递归错误。")
        return True
        
    except RecursionError as e:
        print(f"\n❌ 递归错误仍然存在: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = test_recursion_fix()
    if success:
        print("递归修复验证成功！")
    else:
        print("递归修复验证失败！")
        sys.exit(1)