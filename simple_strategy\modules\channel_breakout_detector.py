import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, <PERSON>ple

logger = logging.getLogger(__name__)

class ChannelBreakoutDetector:
    def __init__(self, config: dict):
        self.config = config
        self.channel_period = config['strategy']['channel_period']
        self.breakout_threshold = config['strategy']['breakout_threshold']
    
    def detect_breakout(self, df: pd.DataFrame) -> Tu<PERSON>[bool, Dict[str, Any]]:
        """检测通道突破信号"""
        try:
            if len(df) < self.channel_period:
                return False, {}
            
            # 计算通道上轨（历史最高价）
            upper_band = df['high'].rolling(self.channel_period).max().iloc[-2]  # 使用前一根K线的上轨
            current_close = df['close'].iloc[-1]
            current_high = df['high'].iloc[-1]
            
            # 计算突破比例
            close_ratio = current_close / upper_band if upper_band > 0 else 0
            high_ratio = current_high / upper_band if upper_band > 0 else 0
            
            # 检查是否突破
            is_breakout = (
                close_ratio >= self.breakout_threshold or 
                high_ratio >= self.breakout_threshold
            )
            
            signal_info = {
                'upper_band': upper_band,
                'current_close': current_close,
                'current_high': current_high,
                'close_ratio': close_ratio,
                'high_ratio': high_ratio,
                'is_breakout': is_breakout
            }
            
            if is_breakout:
                logger.info(f"检测到突破信号: close_ratio={close_ratio:.4f}, high_ratio={high_ratio:.4f}")
            
            return is_breakout, signal_info
            
        except Exception as e:
            logger.error(f"检测突破信号失败: {e}")
            return False, {}
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            # 计算通道
            df['upper_band'] = df['high'].rolling(self.channel_period).max()
            df['lower_band'] = df['low'].rolling(self.channel_period).min()
            df['middle_band'] = (df['upper_band'] + df['lower_band']) / 2
            
            # 计算ATR
            df['tr'] = np.maximum(
                np.maximum(df['high'] - df['low'], abs(df['high'] - df['close'].shift(1))),
                abs(df['low'] - df['close'].shift(1))
            )
            df['atr'] = df['tr'].rolling(14).mean()
            
            # 计算RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            return df
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return df