{"backup_info": {"version": "v1.2.2", "timestamp": "2025-09-25T20:56:59.211491", "backup_type": "full", "created_by": "auto_backup_system", "description": "修复异步调用问题，策略优化器接口调用正常"}, "system_state": {"strategy_running": true, "active_positions": [], "active_orders": 0, "account_balance": 0.0, "timestamp": "2025-09-25T20:56:59.036312"}, "files_backed_up": {"strategy_files": ["strategy/maker_channel.py", "strategy/maker_channel_backup.py", "strategy/__init__.py"], "config_files": ["config/config.json", "config/trading_config.json", "config/risk_config.json", "config/api_config.json"], "core_files": ["binance_trader.py", "cache_manager.py", "http_client.py", "main.py", "requirements.txt"], "data_files": ["cache/candidates.pkl", "cache/symbols.json"], "log_files": ["strategy.log"], "doc_files": ["optimization_improvements_summary.md", "rollback_and_optimization_plan.md", "末位淘汰功能验证任务清单.md", "版本备份机制设计方案.md"]}, "checksums": {"strategy/maker_channel.py": "sha256:d0837748d53efbc0014af1950aed931af97982ce2f000c9dc29eff121013c4a7", "strategy/maker_channel_backup.py": "sha256:964e70eeea5cbdf39e7b7f40229ec64c110c2fefd5c3e9e902f70cc75631dcb4", "strategy/__init__.py": "sha256:c9127234394625de99dad0d6642486b5760c20781b9dc1cbcafdb3a986447201", "config/config.json": "sha256:fcfb6cb621c58f453d8f66f73c9fd65b705daa72b24d6945c0023f4a714a9565", "config/trading_config.json": "sha256:5b9103dc3e4d650ef08d65bfece82d6d8cd402423d637c312edcfa8a7341cf61", "config/risk_config.json": "sha256:659891b11899a5a4474c90034c5c66bdfb6dcce9e562bd8ecfda78251421eb7a", "config/api_config.json": "sha256:963b7ac30b4725667d7a00208a97bdf4290fe253acc8171570f84593413118b3", "binance_trader.py": "sha256:f55fdd1099de0e67bdca669b7749a15990355f5ffe55ac02edb8e770c3e60116", "cache_manager.py": "sha256:b94b50abe59023293de40f2c91d866fc4d2746c47ea0f9f4a792a1d1b9db00a0", "http_client.py": "sha256:0f99715119c3d8e87a0442cb67c5015834aca3eee973bea93c69ed0deb4779db", "main.py": "sha256:aad8fb7821569841139a5e9d707383007c0f8130ee3dcc3207acd998de4ddb86", "requirements.txt": "sha256:510dee7eeb1ae9b11ad1e5ce903b440d9d9c49ece82182cc5a827fea911e9c73", "cache/candidates.pkl": "sha256:49b55667672937b8492b2332c63e7f1f4242697959576ee0777543bbaee8c212", "cache/symbols.json": "sha256:361907a72a219da1678d61175248314fc9b214f0f9a1ce240307282db58afe2e", "strategy.log": "sha256:bf1cd77f8eb7f325aded58fe4adf033db004a3c48ff65371074ac076b28d86a5", "optimization_improvements_summary.md": "sha256:9c006902e110b4bcc12fa15e6606e2497d96bdbcdbdeb1cded5be90551b2d29e", "rollback_and_optimization_plan.md": "sha256:d8f457fdbed1ee93829d0c18daf7c36874c4b57270555122f14515a4d8b1610e", "末位淘汰功能验证任务清单.md": "sha256:62c2eafd26688748ab80149f83345038dd6965ce9b825e3b47201e6403ab7745", "版本备份机制设计方案.md": "sha256:102eed14bedc013a0fe36342b9df41444e25e623796477410bea594a91d54185"}, "failed_files": [], "backup_size": {"total_size_mb": 0.34, "file_count": 19}}