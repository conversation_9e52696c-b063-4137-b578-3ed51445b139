import requests
import hmac
import hashlib
import time
import logging
from requests.adapters import HTTPAdapter

class HttpClient:
    """Binance API HTTP客户端
    
    处理API请求、签名和重试逻辑
    """
    def __init__(self, api_key, api_secret, base_url='https://fapi.binance.com', timeout=30, proxy_cfg=None, verify_ssl=True):
        self.key = api_key
        self.secret = api_secret
        self.base = base_url.rstrip('/') 
        self.timeout = timeout
        self.session = requests.Session()
        self.verify_ssl = verify_ssl
        
        # 设置更高的日志级别来完全禁用SSL警告
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.ERROR)
        
        # 禁用urllib3的警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 智能代理选择逻辑
        self._setup_proxy(proxy_cfg)
        
    def _setup_proxy(self, proxy_cfg):
        """智能代理配置：远程服务器直连，本地和其他环境走代理"""
        import socket
        
        # 获取本机IP地址
        try:
            # 尝试连接到一个外部服务来获取本机出口IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            
            # 检查是否为远程服务器IP段
            is_remote_server = local_ip.startswith("47.250.") or local_ip == "*************"
            
            if is_remote_server:
                self.logger.info(f"检测到远程服务器环境 {local_ip}，使用直连模式")
                # 清空代理配置
                self.session.proxies = {}
            elif proxy_cfg and proxy_cfg.get('enabled'):
                # 本地环境使用代理
                proxy_url = f"http://{proxy_cfg['host']}:{proxy_cfg['port']}"
                self.session.proxies.update({
                    'http': proxy_url,
                    'https': proxy_url
                })
                self.logger.info(f"检测到本地环境 {local_ip}，使用代理模式: {proxy_url}")
            else:
                self.logger.info(f"检测到环境 {local_ip}，代理配置未启用，使用直连模式")
                self.session.proxies = {}
                
        except Exception as e:
            self.logger.warning(f"获取本机IP失败: {e}，使用配置的代理设置")
            # 失败时回退到原始逻辑
            if proxy_cfg and proxy_cfg.get('enabled'):
                proxy_url = f"http://{proxy_cfg['host']}:{proxy_cfg['port']}"
                self.session.proxies.update({
                    'http': proxy_url,
                    'https': proxy_url
                })
            
        # 配置重试
        retry = HTTPAdapter(max_retries=3)
        self.session.mount('http://', retry)
        self.session.mount('https://', retry)
            
        # 测试连接
        try:
            self.session.get(f"{self.base}/fapi/v1/ping", timeout=5, verify=self.verify_ssl)
        except Exception as e:
            self.logger.error(f"Failed to connect to Binance API: {str(e)}")

        # 配置重试
        self.session.mount('http://', HTTPAdapter(max_retries=3))
        self.session.mount('https://', HTTPAdapter(max_retries=3))

    def _sign(self, params):
        """使用HMAC SHA256生成签名
        params: dict - 参数字典 
        """
        try:
            # 获取服务器时间
            server_time = self._get_server_time()
            
            # 添加时间戳
            params['timestamp'] = str(server_time)
            
            # 按字母序排序键名并构建查询字符串
            sorted_params = sorted(params.items(), key=lambda x: x[0])
            # 确保数值类型转为字符串
            query_string = '&'.join([f"{k}={str(v)}" for k, v in sorted_params])
            
            # 生成签名
            signature = hmac.new(
                self.secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            self.logger.debug(f"参数: {params}")
            self.logger.debug(f"签名: {signature}")
            
            return signature, query_string
        except Exception as e:
            self.logger.error(f"生成签名失败: {e}")
            raise

    def _get_server_time(self):
        """获取服务器时间"""
        try:
            resp = self.session.get(f"{self.base}/fapi/v1/time", timeout=self.timeout, verify=self.verify_ssl)
            if resp.status_code == 200:
                return resp.json()['serverTime']
            self.logger.warning(f"获取服务器时间HTTP错误: {resp.status_code}")
        except Exception as e:
            self.logger.error(f"获取服务器时间失败: {e}")
        
        # 如果获取失败则使用本地时间
        return int(time.time() * 1000)

    def _handle_response(self, response, path):
        try:
            # 打印响应内容
            response_text = response.text
            self.logger.debug(f"响应内容: {response_text}")
            
            # 检查响应状态码
            if response.status_code != 200:
                self.logger.error(f"HTTP状态码错误: {response.status_code}, 响应内容: {response_text}")
                return {'error': f'HTTP错误: {response.status_code}, {response_text}'}
                
            # 尝试解析JSON
            try:
                data = response.json()
            except Exception as e:
                self.logger.error(f"JSON解析失败: {response_text}")
                return {'error': f'解析响应失败: {str(e)}'}
            
            # 成功响应且没有错误码
            if not isinstance(data, dict) or 'code' not in data:
                return data
                
            # 处理特殊情况
            if path == '/fapi/v1/marginType':
                if data.get('code') == 200 or data.get('msg', '').startswith('No need to change'):
                    return {'success': True}
            
            if path == '/fapi/v1/leverage':
                if data.get('code') == 200 or data.get('leverage'):
                    return {'success': True, 'leverage': data.get('leverage')}
            
            # 处理签名错误
            if data.get('code') == -1022:  # 签名错误的代码
                self.logger.error("签名验证失败，请检查时间同步")
                return {'error': '签名验证失败，请检查时间同步'}
            
            # 处理"无需更改保证金类型"的情况
            if data.get('code') == -4046 and 'No need to change margin type' in data.get('msg', ''):
                self.logger.info("保证金类型已正确设置，无需更改")
                return {'success': True}
            
            # 处理"未知订单"的情况
            if data.get('code') == -2011 and 'Unknown order sent' in data.get('msg', ''):
                self.logger.warning("尝试操作不存在的订单")
                return {'error': '订单不存在或已成交'}
            
            # 处理一般情况
            if data.get('code') == 200:
                return data
            
            # 处理API错误
            error_msg = data.get('msg', 'Unknown error')
            self.logger.error(f"API错误: {error_msg}")
            return {'error': f"API错误: {error_msg}"}
            
        except Exception as e:
            self.logger.error(f"解析响应失败: {str(e)}")
            return {'error': f"解析响应失败: {str(e)}"}



    def get(self, path, params=None):
        """GET请求"""
        headers = {'X-MBX-APIKEY': self.key}
        params = params or {}
        
        # 所有参数值转为字符串
        params = {k: str(v) for k, v in params.items()}
        
        # 生成签名和最终查询字符串
        signature, query = self._sign(params)
        
        # 添加签名到查询字符串
        final_query = f"{query}&signature={signature}"
        self.logger.debug(f"最终查询字符串: {final_query}")
        
        try:
            url = f"{self.base}{path}?{final_query}"
            self.logger.debug(f"发送GET请求: URL={url}")
            response = self.session.get(
                url,
                headers=headers,
                timeout=self.timeout,
                verify=self.verify_ssl
            )
            return self._handle_response(response, path)
        except Exception as e:
            self.logger.error(f"GET请求失败 {path}: {str(e)}")
            return {'error': f"GET请求失败: {str(e)}"}

    def post(self, path, params=None):
        """POST请求"""
        headers = {'X-MBX-APIKEY': self.key}
        params = params or {}
        
        # 所有参数值转为字符串
        params = {k: str(v) for k, v in params.items()}
        
        # 生成签名和查询字符串
        signature, query = self._sign(params)
        
        # 添加签名到查询字符串
        final_query = f"{query}&signature={signature}"
        
        try:
            url = f"{self.base}{path}?{final_query}"
            self.logger.debug(f"发送POST请求: URL={url}")
            response = self.session.post(
                url,
                headers=headers,
                timeout=self.timeout,
                verify=self.verify_ssl
            )
            return self._handle_response(response, path)
        except Exception as e:
            self.logger.error(f"POST请求失败 {path}: {str(e)}")
            return {'error': f"POST请求失败: {str(e)}"}
            
    def delete(self, path, params=None):
        """DELETE请求"""
        headers = {'X-MBX-APIKEY': self.key}
        params = params or {}
        
        # 所有参数值转为字符串
        params = {k: str(v) for k, v in params.items()}
        
        # 生成签名和查询字符串
        signature, query = self._sign(params)
        
        # 添加签名到查询字符串
        final_query = f"{query}&signature={signature}"
        
        try:
            url = f"{self.base}{path}?{final_query}"
            self.logger.debug(f"发送DELETE请求: URL={url}")
            response = self.session.delete(
                url,
                headers=headers,
                timeout=self.timeout,
                verify=self.verify_ssl
            )
            return self._handle_response(response, path)
        except Exception as e:
            self.logger.error(f"DELETE请求失败 {path}: {str(e)}")
            return {'error': f"DELETE请求失败: {str(e)}"}

    def get_margin_type(self, symbol):
        """获取保证金模式"""
        # 先检查交易对是否存在
        info = self.get('/fapi/v1/exchangeInfo')
        if not info or not info.get('symbols'):
            self.logger.error("获取交易对信息失败")
            return 'CROSS'
            
        found = False
        symbols = info.get('symbols', [])
        if not isinstance(symbols, list):
            symbols = []
        for s in symbols:
            if isinstance(s, dict) and s.get('symbol') == symbol:
                found = True
                break
                
        if not found:
            self.logger.error(f"交易对 {symbol} 不存在")
            return 'CROSS'
            
        # 获取仓位信息
        result = self.get('/fapi/v2/positionRisk', {'symbol': symbol})
        if isinstance(result, list) and len(result) > 0:
            margin_type = result[0].get('marginType', '').upper()
            if margin_type in ('ISOLATED', 'CROSS'):
                return margin_type
        return 'CROSS'  # 如果获取失败，默认返回全仓

    def get_leverage(self, symbol):
        """获取当前杠杆倍数"""
        # 先检查交易对是否存在
        info = self.get('/fapi/v1/exchangeInfo')
        if not info or not info.get('symbols'):
            self.logger.error("获取交易对信息失败")
            return 1
            
        found = False
        symbols = info.get('symbols', [])
        if not isinstance(symbols, list):
            symbols = []
        for s in symbols:
            if isinstance(s, dict) and s.get('symbol') == symbol:
                found = True
                break
                
        if not found:
            self.logger.error(f"交易对 {symbol} 不存在")
            return 1
            
        # 获取仓位信息
        result = self.get('/fapi/v2/positionRisk', {'symbol': symbol})
        if isinstance(result, list) and len(result) > 0:
            leverage = result[0].get('leverage', '1')
            try:
                return int(float(leverage))
            except (ValueError, TypeError):
                self.logger.warning(f"无法解析杠杆倍数: {leverage}")
                return 1
        return 1  # 如果获取失败，默认返回1倍

    def set_margin_type(self, symbol, margin_type='ISOLATED'):
        """设置保证金模式"""
        # 先检查交易对是否存在
        info = self.get('/fapi/v1/exchangeInfo')
        if not info or not info.get('symbols'):
            self.logger.error("获取交易对信息失败")
            return {'error': '获取交易对信息失败'}
            
        found = False
        symbols = info.get('symbols', [])
        if not isinstance(symbols, list):
            symbols = []
        for s in symbols:
            if isinstance(s, dict) and s.get('symbol') == symbol:
                found = True
                break
                
        if not found:
            self.logger.error(f"交易对 {symbol} 不存在")
            return {'error': f"交易对 {symbol} 不存在"}
            
        response = self.post('/fapi/v1/marginType', {
            'symbol': symbol,
            'marginType': margin_type.upper()  # 确保大写
        })
        if response and ('success' in response or (isinstance(response, dict) and response.get('code') == 200) or "No need to change margin type" in str(response)):
            self.logger.info(f"成功设置{symbol}为{margin_type}模式")
            return {'success': True}
        self.logger.error(f"设置{symbol}保证金模式失败: {response.get('error', '未知错误') if response else '未知错误'}")
        return response

    def set_leverage(self, symbol, leverage):
        """设置杠杆倍数"""
        # 先检查交易对是否存在
        info = self.get('/fapi/v1/exchangeInfo')
        if not info or not info.get('symbols'):
            self.logger.error("获取交易对信息失败")
            return {'error': '获取交易对信息失败'}
            
        found = False
        symbols = info.get('symbols', [])
        if not isinstance(symbols, list):
            symbols = []
        for s in symbols:
            if isinstance(s, dict) and s.get('symbol') == symbol:
                found = True
                break
                
        if not found:
            self.logger.error(f"交易对 {symbol} 不存在")
            return {'error': f"交易对 {symbol} 不存在"}
        
        try:
            lev = int(leverage)
            response = self.post('/fapi/v1/leverage', {
                'symbol': symbol,
                'leverage': lev  # 确保是整数
            })
            if response and ('leverage' in response or 'success' in response or (isinstance(response, dict) and response.get('code') == 200)):
                self.logger.info(f"成功设置{symbol}杠杆为{lev}倍")
                return {'success': True, 'leverage': lev}
            self.logger.error(f"设置{symbol}杠杆失败: {response.get('error', '未知错误') if response else '未知错误'}")
            return response
        except Exception as e:
            error = f"设置杠杆发生异常: {str(e)}"
            self.logger.error(error)
            return {'error': error}
