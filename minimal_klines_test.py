#!/usr/bin/env python3
"""
最小化K线测试脚本 - 避免复杂依赖
"""

import sys
import json
import pandas as pd
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from binance_trader import BinanceTrader

def minimal_klines_test():
    """最小化K线获取测试"""
    
    # 加载配置
    with open('config/config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 添加缺失的配置项
    config['first_nominal'] = 100
    config['max_nominal'] = 1000
    
    # 初始化交易器
    trader = BinanceTrader(config)
    
    # 测试符号
    test_symbols = ['SSVUSDT', 'BCHUSDT', 'BTCUSDT']
    
    print("最小化K线获取测试:")
    print("=" * 60)
    
    for symbol in test_symbols:
        print(f"\n测试 {symbol}:")
        
        try:
            # 直接调用API获取K线数据
            klines = trader.http.get('/fapi/v1/klines', {
                'symbol': symbol,
                'interval': '15m',
                'limit': 200
            })
            
            if not klines:
                print(f"  ❌ API返回空数据")
                continue
                
            print(f"  ✓ API返回 {len(klines)} 条原始数据")
            
            # 模拟maker_channel.py中的数据处理逻辑
            df = pd.DataFrame(klines, columns=['t', 'o', 'h', 'l', 'c', 'v', 'ct', 'qv', 'n', 'tbbav', 'tbqav', 'ignore'])
            df = df[['t', 'o', 'h', 'l', 'c', 'v']].astype(float)
            
            # 重命名列
            df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            
            # 设置时间索引
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            print(f"  ✓ DataFrame处理完成，{len(df)} 条数据")
            print(f"  - 时间范围: {df.index[0]} 到 {df.index[-1]}")
            
            # 检查数据质量
            min_klines = 20  # 假设是老币
            if len(df) >= min_klines and not df.empty:
                print(f"  ✓ 数据质量验证通过")
            else:
                print(f"  ❌ 数据质量验证失败: 长度{len(df)}, 空值{df.empty}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
            import traceback
            print(f"  详细错误: {traceback.format_exc()}")
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    minimal_klines_test()