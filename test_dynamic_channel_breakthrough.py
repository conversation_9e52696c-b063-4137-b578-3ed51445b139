"""
测试动态通道突破逻辑
验证基于币龄的动态时间周期是否正常工作
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加strategy目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy'))

from dynamic_tf_helper import dynamic_tf_for_channel, dynamic_tf_for_scoring

class MockLogger:
    def debug(self, msg):
        print(f"DEBUG: {msg}")
    
    def info(self, msg):
        print(f"INFO: {msg}")

class TestChannelBreakthrough:
    def __init__(self):
        self.log = MockLogger()
    
    def check_channel_breakthrough(self, df, age_days):
        """
        检查通道突破条件 - 使用动态时间周期
        
        Args:
            df: K线数据DataFrame
            age_days: 币龄（天数）
            
        Returns:
            bool: 是否满足通道突破条件
        """
        if df.empty or len(df) < 10:
            return False
        
        # 根据币龄动态计算通道周期
        n = dynamic_tf_for_channel(age_days)
        
        # 确保有足够的数据
        if len(df) < n:
            n = len(df) - 1
            if n < 2:
                return False
        
        # 计算通道上下轨
        high_n = df['high'].rolling(window=n, min_periods=1).max()
        low_n = df['low'].rolling(window=n, min_periods=1).min()
        
        # 当前价格
        current_close = df['close'].iloc[-1]
        current_high = df['high'].iloc[-1]
        
        # 通道上轨
        upper_band = high_n.iloc[-1]
        
        # 放宽条件：收盘价接近上轨或最高价突破上轨即可
        close_near_upper = current_close >= upper_band * 0.999  # 收盘价接近上轨
        high_break_upper = current_high >= upper_band * 1.001   # 最高价突破上轨
        
        # 突破幅度限制：不超过10%
        breakthrough_ratio = (current_close / upper_band - 1) * 100
        valid_breakthrough = breakthrough_ratio <= 10
        
        # 允许5根K线的突破时间
        recent_closes = df['close'].iloc[-5:]
        any_recent_breakthrough = any(close >= upper_band * 0.999 for close in recent_closes)
        
        breakthrough_condition = (close_near_upper or high_break_upper) and valid_breakthrough and any_recent_breakthrough
        
        if breakthrough_condition:
            self.log.debug(f"通道突破: 币龄={age_days:.1f}天, 周期={n}根K线, 当前价={current_close:.6f}, 上轨={upper_band:.6f}, 突破幅度={breakthrough_ratio:.2f}%")
        
        return breakthrough_condition

def generate_test_data(length=50, trend='up'):
    """生成测试K线数据"""
    np.random.seed(42)
    
    # 基础价格
    base_price = 100.0
    
    # 生成价格序列
    if trend == 'up':
        # 上升趋势
        trend_factor = np.linspace(0, 0.2, length)
        noise = np.random.normal(0, 0.01, length)
        prices = base_price * (1 + trend_factor + noise)
    elif trend == 'down':
        # 下降趋势
        trend_factor = np.linspace(0, -0.2, length)
        noise = np.random.normal(0, 0.01, length)
        prices = base_price * (1 + trend_factor + noise)
    else:
        # 横盘
        noise = np.random.normal(0, 0.005, length)
        prices = base_price * (1 + noise)
    
    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.005)))
        low = price * (1 - abs(np.random.normal(0, 0.005)))
        open_price = price * (1 + np.random.normal(0, 0.002))
        close = price
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': np.random.uniform(1000, 10000)
        })
    
    return pd.DataFrame(data)

def test_dynamic_tf_functions():
    """测试动态时间框架函数"""
    print("=== 测试动态时间框架函数 ===")
    
    test_ages = [0.5, 2, 5, 15, 60, 200, 500]
    
    for age in test_ages:
        channel_period = dynamic_tf_for_channel(age)
        scoring_tf = dynamic_tf_for_scoring(age)
        print(f"币龄 {age:4.1f} 天: 通道周期={channel_period:2d}根K线, 评分周期={scoring_tf}")

def test_channel_breakthrough():
    """测试通道突破逻辑"""
    print("\n=== 测试通道突破逻辑 ===")
    
    tester = TestChannelBreakthrough()
    
    # 测试不同币龄和趋势组合
    test_cases = [
        (0.5, 'up', '新币上升趋势'),
        (2, 'up', '较新币上升趋势'),
        (15, 'up', '中等币龄上升趋势'),
        (100, 'up', '老币上升趋势'),
        (15, 'sideways', '中等币龄横盘'),
        (15, 'down', '中等币龄下降趋势')
    ]
    
    for age_days, trend, description in test_cases:
        print(f"\n--- {description} ---")
        
        # 生成测试数据
        df = generate_test_data(length=50, trend=trend)
        
        # 如果是上升趋势，在最后几根K线制造突破
        if trend == 'up':
            # 计算通道周期
            channel_period = dynamic_tf_for_channel(age_days)
            if len(df) >= channel_period:
                # 获取前n根K线的最高价作为通道上轨
                upper_band = df['high'].iloc[:-1].rolling(window=channel_period-1, min_periods=1).max().iloc[-1]
                
                # 最后一根K线明显突破上轨
                df.loc[df.index[-1], 'high'] = upper_band * 1.008  # 突破0.8%
                df.loc[df.index[-1], 'close'] = upper_band * 1.005  # 收盘价也突破0.5%
                
                # 最后几根K线也接近突破
                for i in range(2, min(6, len(df))):
                    idx = df.index[-i]
                    df.loc[idx, 'close'] = max(df.loc[idx, 'close'], upper_band * 0.9995)
        
        # 测试突破检测
        result = tester.check_channel_breakthrough(df, age_days)
        
        channel_period = dynamic_tf_for_channel(age_days)
        print(f"币龄: {age_days}天, 通道周期: {channel_period}根K线")
        print(f"突破检测结果: {'✓ 突破' if result else '✗ 未突破'}")
        
        # 显示关键数据
        current_close = df['close'].iloc[-1]
        upper_band = df['high'].rolling(window=channel_period, min_periods=1).max().iloc[-1]
        print(f"当前收盘价: {current_close:.6f}, 通道上轨: {upper_band:.6f}")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    tester = TestChannelBreakthrough()
    
    # 测试数据不足的情况
    print("--- 数据不足测试 ---")
    small_df = generate_test_data(length=5, trend='up')
    result = tester.check_channel_breakthrough(small_df, 15)
    print(f"5根K线数据，币龄15天: {'✓ 突破' if result else '✗ 未突破'}")
    
    # 测试空数据
    print("--- 空数据测试 ---")
    empty_df = pd.DataFrame()
    result = tester.check_channel_breakthrough(empty_df, 15)
    print(f"空数据: {'✓ 突破' if result else '✗ 未突破'}")
    
    # 测试极端币龄
    print("--- 极端币龄测试 ---")
    df = generate_test_data(length=50, trend='up')
    for extreme_age in [0.1, 1000]:
        channel_period = dynamic_tf_for_channel(extreme_age)
        result = tester.check_channel_breakthrough(df, extreme_age)
        print(f"币龄 {extreme_age} 天，通道周期 {channel_period} 根K线: {'✓ 突破' if result else '✗ 未突破'}")

if __name__ == "__main__":
    print("动态通道突破逻辑测试")
    print("=" * 50)
    
    # 运行所有测试
    test_dynamic_tf_functions()
    test_channel_breakthrough()
    test_edge_cases()
    
    print("\n测试完成！")