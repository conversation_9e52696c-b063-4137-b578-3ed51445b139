"""
新币种处理器 - 专门处理历史数据不足的新币种
解决AKEUSDT等新上市币种K线数据获取异常问题
"""

import pandas as pd
import time
import logging
import traceback
import yaml
import os
from datetime import datetime, timedelta
from enum import Enum

class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network"
    DATA_ERROR = "data"
    API_ERROR = "api"
    VALIDATION_ERROR = "validation"
    UNKNOWN_ERROR = "unknown"

class NewCoinHandler:
    def __init__(self, trader, logger=None, config_path=None):
        self.trader = trader
        self.log = logger or logging.getLogger(__name__)
        
        # 配置文件路径
        self.config_path = config_path or os.path.join(os.path.dirname(__file__), 'config', 'new_coin_config.yaml')
        
        # 加载配置
        self.config = self._load_config()
        
        # 错误统计
        self.error_stats = {
            'total_errors': 0,
            'error_by_type': {error_type.value: 0 for error_type in ErrorType},
            'error_by_symbol': {},
            'last_error_time': None,
            'consecutive_errors': 0
        }
        
        # 操作统计
        self.operation_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'retry_count': 0,
            'cache_hits': 0,
            'optimization_count': 0
        }
        
        # 从配置文件加载新币种白名单
        self.new_coin_whitelist = self.config.get('whitelist', {})
        
        # 如果配置文件中没有白名单，使用默认配置
        if not self.new_coin_whitelist:
            self.new_coin_whitelist = {
                'AKEUSDT': {
                    'onboard_date': '2025-09-26',
                    'max_available_klines': 82,  # 实际可获取的最大K线数量
                    'recommended_intervals': ['1m', '5m', '15m', '1h'],  # 推荐的时间间隔
                'min_klines_required': 5,  # 最低K线要求
                'max_limit': 50  # 最大请求限制
            }
        }
        
        # 新币种识别阈值（天数）
        self.new_coin_threshold_days = 7
        
    def is_new_coin(self, symbol, age_days=None):
        """判断是否为新币种"""
        try:
            # 首先检查白名单
            if symbol in self.new_coin_whitelist:
                self.log.debug(f"{symbol}: 在新币种白名单中")
                return True
                
            # 如果提供了币龄，使用币龄判断
            if age_days is not None:
                threshold_days = self.get_config_value('basic.new_coin_threshold_days', 7)
                is_new = age_days < threshold_days
                self.log.debug(f"{symbol}: 币龄{age_days}天，阈值{threshold_days}天，{'是' if is_new else '不是'}新币种")
                return is_new
                
            return False
            
        except Exception as e:
            self._record_error(symbol, ErrorType.UNKNOWN_ERROR, f"判断新币种异常: {e}")
            return False
    
    def get_new_coin_config(self, symbol):
        """获取新币种的特殊配置"""
        try:
            if symbol in self.new_coin_whitelist:
                config = self.new_coin_whitelist[symbol]
                self.log.debug(f"{symbol}: 使用白名单配置 - 最大K线:{config['max_available_klines']}")
                return config
            
            # 从配置文件获取默认新币种配置
            default_config = self.get_config_value('basic.default_config', {
                'max_available_klines': 50,
                'recommended_intervals': ['1m', '5m', '15m'],
                'min_klines_required': 5,
                'max_limit': 30
            })
            self.log.debug(f"{symbol}: 使用默认新币种配置")
            return default_config
            
        except Exception as e:
            self._record_error(symbol, ErrorType.UNKNOWN_ERROR, f"获取配置异常: {e}")
            # 返回最基本的配置
            return {
                'max_available_klines': 20,
                'recommended_intervals': ['15m'],
                'min_klines_required': 3,
                'max_limit': 20
            }
    
    def optimize_kline_request(self, symbol, interval, limit, age_days=None):
        """优化新币种的K线请求参数"""
        try:
            if not self.is_new_coin(symbol, age_days):
                return interval, limit  # 非新币种，不做调整
            
            self.operation_stats['optimization_count'] += 1
            config = self.get_new_coin_config(symbol)
            
            # 调整请求限制
            optimized_limit = min(limit, config['max_limit'])
            
            # 调整时间间隔（如果当前间隔不在推荐列表中）
            optimized_interval = interval
            if interval not in config['recommended_intervals']:
                # 选择推荐间隔中最接近的
                if interval in ['30m', '1h', '2h', '4h']:
                    optimized_interval = '15m'  # 降级到15分钟
                elif interval in ['6h', '8h', '12h', '1d']:
                    optimized_interval = '1h'   # 降级到1小时
            
            if optimized_interval != interval or optimized_limit != limit:
                self.log.info(f"{symbol}: 新币种优化 {interval}->{optimized_interval}, limit {limit}->{optimized_limit}")
            
            return optimized_interval, optimized_limit
            
        except Exception as e:
            self._record_error(symbol, ErrorType.UNKNOWN_ERROR, f"参数优化异常: {e}")
            # 返回保守的参数
            return '15m', min(limit, 20)
    
    def get_klines_for_new_coin(self, symbol, interval='15m', limit=200, age_days=None):
        """专门为新币种获取K线数据"""
        start_time = time.time()
        self.operation_stats['total_requests'] += 1
        
        try:
            # 优化请求参数
            opt_interval, opt_limit = self.optimize_kline_request(symbol, interval, limit, age_days)
            
            config = self.get_new_coin_config(symbol)
            
            # 从配置文件获取重试参数
            max_retries = self.get_config_value('basic.default_config.retry_count', 3)
            retry_delay = self.get_config_value('basic.default_config.retry_delay', 1.5)
            
            self.log.info(f"{symbol}: 新币种K线请求开始 - 间隔:{opt_interval}, 限制:{opt_limit}")
            
            for attempt in range(max_retries):
                try:
                    self.operation_stats['retry_count'] += attempt
                    
                    klines = self.trader.http.get('/fapi/v1/klines', {
                        'symbol': symbol,
                        'interval': opt_interval,
                        'limit': opt_limit
                    })
                    
                    if not klines:
                        error_msg = f"K线数据为空，第{attempt+1}次重试"
                        self.log.warning(f"{symbol}: {error_msg}")
                        self._record_error(symbol, ErrorType.DATA_ERROR, error_msg)
                        time.sleep(retry_delay)
                        continue
                    
                    # 检查数据有效性 - 使用新币种的宽松要求
                    min_required = config['min_klines_required']
                    if len(klines) < min_required:
                        error_msg = f"K线数据不足 {len(klines)}<{min_required}，第{attempt+1}次重试"
                        self.log.warning(f"{symbol}: {error_msg}")
                        self._record_error(symbol, ErrorType.DATA_ERROR, error_msg)
                        time.sleep(retry_delay)
                        continue
                    
                    # 转换为DataFrame
                    df = pd.DataFrame(klines, columns=['t', 'o', 'h', 'l', 'c', 'v', 'ct', 'qv', 'n', 'tbbav', 'tbqav', 'ignore'])
                    df = df[['t', 'o', 'h', 'l', 'c', 'v']].astype(float)
                    df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 数据质量验证
                    is_valid, validation_msg = self.validate_new_coin_data(symbol, df)
                    if not is_valid:
                        error_msg = f"数据验证失败: {validation_msg}，第{attempt+1}次重试"
                        self.log.warning(f"{symbol}: {error_msg}")
                        self._record_error(symbol, ErrorType.VALIDATION_ERROR, error_msg)
                        time.sleep(retry_delay)
                        continue
                    
                    # 成功获取数据
                    elapsed_time = time.time() - start_time
                    self.operation_stats['successful_requests'] += 1
                    self.error_stats['consecutive_errors'] = 0  # 重置连续错误计数
                    
                    self.log.info(f"{symbol}: 新币种K线获取成功，共{len(df)}条数据，耗时{elapsed_time:.2f}秒")
                    return df
                    
                except Exception as retry_e:
                    error_msg = f"K线获取异常，第{attempt+1}次重试: {retry_e}"
                    self.log.warning(f"{symbol}: {error_msg}")
                    
                    # 分类错误类型
                    error_type = self._classify_error(retry_e)
                    self._record_error(symbol, error_type, error_msg, retry_e)
                    
                    time.sleep(retry_delay)
                    continue
            
            # 所有重试都失败
            self.operation_stats['failed_requests'] += 1
            error_msg = f"新币种K线获取失败，已重试{max_retries}次"
            self.log.error(f"{symbol}: {error_msg}")
            self._record_error(symbol, ErrorType.API_ERROR, error_msg)
            return None
            
        except Exception as e:
            self.operation_stats['failed_requests'] += 1
            error_msg = f"新币种K线处理异常: {e}"
            self.log.error(f"{symbol}: {error_msg}")
            self._record_error(symbol, ErrorType.UNKNOWN_ERROR, error_msg, e)
            return None
    
    def add_new_coin_to_whitelist(self, symbol, onboard_date, max_klines=50):
        """动态添加新币种到白名单"""
        try:
            self.new_coin_whitelist[symbol] = {
                'onboard_date': onboard_date,
                'max_available_klines': max_klines,
                'recommended_intervals': ['1m', '5m', '15m', '1h'],
                'min_klines_required': 5,
                'max_limit': min(max_klines, 50)
            }
            self.log.info(f"已添加新币种到白名单: {symbol}, 上市日期: {onboard_date}, 最大K线: {max_klines}")
            
        except Exception as e:
            self._record_error(symbol, ErrorType.UNKNOWN_ERROR, f"添加白名单异常: {e}")
    
    def validate_new_coin_data(self, symbol, df):
        """验证新币种数据的有效性"""
        try:
            if df is None or df.empty:
                return False, "数据为空"
            
            config = self.get_new_coin_config(symbol)
            min_required = config['min_klines_required']
            
            if len(df) < min_required:
                return False, f"数据不足，仅有{len(df)}条，需要至少{min_required}条"
            
            # 检查数据质量
            if df['close'].isna().any():
                return False, "数据质量异常，存在NaN价格"
            
            if (df['close'] <= 0).any():
                return False, "数据质量异常，存在无效价格(<=0)"
            
            # 从配置文件获取验证参数
            check_continuity = self.get_config_value('validation.check_data_continuity', True)
            time_std_threshold = self.get_config_value('validation.time_interval_std_threshold', 30)
            price_volatility_threshold = self.get_config_value('validation.price_volatility_threshold', 10.0)
            
            # 检查数据时间连续性
            if check_continuity and len(df) > 1:
                time_diff = df.index.to_series().diff().dropna()
                if time_diff.std() > pd.Timedelta(minutes=time_std_threshold):
                    self.log.warning(f"{symbol}: 数据时间间隔不规律，标准差: {time_diff.std()}")
            
            # 检查价格合理性
            price_range = df['high'].max() / df['low'].min()
            if price_range > price_volatility_threshold:
                self.log.warning(f"{symbol}: 价格波动异常，最高/最低比值: {price_range:.2f}")
            
            return True, f"数据有效，共{len(df)}条，时间范围: {df.index[0]} 到 {df.index[-1]}"
            
        except Exception as e:
            self._record_error(symbol, ErrorType.VALIDATION_ERROR, f"数据验证异常: {e}")
            return False, f"验证异常: {e}"
    
    def get_new_coin_stats(self):
        """获取新币种处理统计信息"""
        try:
            return {
                'whitelist_count': len(self.new_coin_whitelist),
                'whitelist_symbols': list(self.new_coin_whitelist.keys()),
                'threshold_days': self.new_coin_threshold_days,
                'error_stats': self.error_stats.copy(),
                'operation_stats': self.operation_stats.copy(),
                'success_rate': (self.operation_stats['successful_requests'] / 
                               max(self.operation_stats['total_requests'], 1) * 100)
            }
        except Exception as e:
            self.log.error(f"获取统计信息异常: {e}")
            return {}
    
    def _record_error(self, symbol, error_type, message, exception=None):
        """记录错误信息"""
        try:
            current_time = time.time()
            
            # 更新错误统计
            self.error_stats['total_errors'] += 1
            self.error_stats['error_by_type'][error_type.value] += 1
            self.error_stats['last_error_time'] = current_time
            self.error_stats['consecutive_errors'] += 1
            
            # 按币种统计错误
            if symbol not in self.error_stats['error_by_symbol']:
                self.error_stats['error_by_symbol'][symbol] = {
                    'count': 0,
                    'last_error': None,
                    'error_types': {error_type.value: 0 for error_type in ErrorType}
                }
            
            symbol_stats = self.error_stats['error_by_symbol'][symbol]
            symbol_stats['count'] += 1
            symbol_stats['last_error'] = current_time
            symbol_stats['error_types'][error_type.value] += 1
            
            # 记录详细错误信息
            error_detail = {
                'timestamp': current_time,
                'symbol': symbol,
                'error_type': error_type.value,
                'message': message,
                'traceback': traceback.format_exc() if exception else None
            }
            
            # 根据错误严重程度选择日志级别
            if error_type in [ErrorType.NETWORK_ERROR, ErrorType.API_ERROR]:
                self.log.error(f"[{error_type.value.upper()}] {symbol}: {message}")
            else:
                self.log.warning(f"[{error_type.value.upper()}] {symbol}: {message}")
            
            # 如果连续错误过多，记录警告
            if self.error_stats['consecutive_errors'] >= 5:
                self.log.error(f"连续错误已达到 {self.error_stats['consecutive_errors']} 次，请检查系统状态")
                
        except Exception as e:
            # 避免错误记录本身出错导致的无限循环
            self.log.error(f"记录错误信息时异常: {e}")
    
    def _classify_error(self, exception):
        """分类错误类型"""
        try:
            error_str = str(exception).lower()
            
            # 网络相关错误
            if any(keyword in error_str for keyword in ['timeout', 'connection', 'network', 'dns']):
                return ErrorType.NETWORK_ERROR
            
            # API相关错误
            if any(keyword in error_str for keyword in ['api', 'rate limit', 'forbidden', 'unauthorized']):
                return ErrorType.API_ERROR
            
            # 数据相关错误
            if any(keyword in error_str for keyword in ['json', 'parse', 'decode', 'format']):
                return ErrorType.DATA_ERROR
            
            # 验证相关错误
            if any(keyword in error_str for keyword in ['validation', 'invalid', 'missing']):
                return ErrorType.VALIDATION_ERROR
            
            return ErrorType.UNKNOWN_ERROR
            
        except Exception:
            return ErrorType.UNKNOWN_ERROR
    
    def reset_error_stats(self):
        """重置错误统计"""
        try:
            self.error_stats = {
                'total_errors': 0,
                'error_by_type': {error_type.value: 0 for error_type in ErrorType},
                'error_by_symbol': {},
                'last_error_time': None,
                'consecutive_errors': 0
            }
            self.log.info("错误统计已重置")
            
        except Exception as e:
            self.log.error(f"重置错误统计异常: {e}")
    
    def get_error_summary(self):
        """获取错误摘要报告"""
        try:
            if self.error_stats['total_errors'] == 0:
                return "无错误记录"
            
            summary = []
            summary.append(f"总错误数: {self.error_stats['total_errors']}")
            summary.append(f"连续错误: {self.error_stats['consecutive_errors']}")
            
            # 按类型统计
            summary.append("错误类型分布:")
            for error_type, count in self.error_stats['error_by_type'].items():
                if count > 0:
                    summary.append(f"  {error_type}: {count}")
            
            # 错误最多的币种
            if self.error_stats['error_by_symbol']:
                top_error_symbol = max(self.error_stats['error_by_symbol'].items(), 
                                     key=lambda x: x[1]['count'])
                summary.append(f"错误最多币种: {top_error_symbol[0]} ({top_error_symbol[1]['count']}次)")
            
            return "\n".join(summary)
            
        except Exception as e:
            return f"生成错误摘要异常: {e}"
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.log.info(f"成功加载配置文件: {self.config_path}")
                return config
            else:
                self.log.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                return self._get_default_config()
                
        except Exception as e:
            self.log.error(f"加载配置文件异常: {e}")
            return self._get_default_config()
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'basic': {
                'new_coin_threshold_days': 7,
                'default_config': {
                    'max_available_klines': 50,
                    'recommended_intervals': ['1m', '5m', '15m', '1h'],
                    'min_klines_required': 5,
                    'max_limit': 30,
                    'retry_count': 3,
                    'retry_delay': 1.5
                }
            },
            'error_handling': {
                'max_consecutive_errors': 10,
                'error_cooldown_time': 300,
                'enable_error_stats': True,
                'error_log_level': 'WARNING'
            },
            'performance': {
                'enable_cache': True,
                'cache_ttl': 3600,
                'max_cache_size': 1000,
                'enable_concurrent': False,
                'concurrent_limit': 3
            },
            'validation': {
                'strict_validation': False,
                'price_volatility_threshold': 10.0,
                'time_interval_std_threshold': 30,
                'check_data_continuity': True
            },
            'logging': {
                'level': 'INFO',
                'verbose': True,
                'log_performance': True
            },
            'monitoring': {
                'enable_monitoring': True,
                'stats_report_interval': 3600,
                'enable_health_check': True,
                'health_check_interval': 300
            }
        }
    
    def reload_config(self):
        """重新加载配置文件"""
        try:
            old_config = self.config.copy()
            self.config = self._load_config()
            
            # 更新白名单
            self.new_coin_whitelist = self.config.get('whitelist', {})
            
            self.log.info("配置文件重新加载成功")
            return True
            
        except Exception as e:
            self.log.error(f"重新加载配置文件异常: {e}")
            self.config = old_config  # 恢复旧配置
            return False
    
    def get_config_value(self, key_path, default=None):
        """获取配置值，支持嵌套键路径，如 'basic.new_coin_threshold_days'"""
        try:
            keys = key_path.split('.')
            value = self.config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
            
        except Exception as e:
            self.log.error(f"获取配置值异常 {key_path}: {e}")
            return default
    
    def update_config_value(self, key_path, new_value):
        """更新配置值"""
        try:
            keys = key_path.split('.')
            config = self.config
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = new_value
            
            self.log.info(f"配置值已更新 {key_path}: {new_value}")
            return True
            
        except Exception as e:
            self.log.error(f"更新配置值异常 {key_path}: {e}")
            return False
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.log.info(f"配置已保存到: {self.config_path}")
            return True
            
        except Exception as e:
            self.log.error(f"保存配置文件异常: {e}")
            return False
    
    def get_config_summary(self):
        """获取配置摘要"""
        try:
            summary = []
            summary.append(f"配置文件路径: {self.config_path}")
            summary.append(f"配置文件存在: {os.path.exists(self.config_path)}")
            
            # 基础配置
            basic_config = self.config.get('basic', {})
            summary.append(f"新币种阈值: {basic_config.get('new_coin_threshold_days', 'N/A')} 天")
            
            # 白名单数量
            whitelist_count = len(self.new_coin_whitelist)
            summary.append(f"白名单币种数量: {whitelist_count}")
            
            # 错误处理配置
            error_config = self.config.get('error_handling', {})
            summary.append(f"最大连续错误: {error_config.get('max_consecutive_errors', 'N/A')}")
            
            # 性能配置
            perf_config = self.config.get('performance', {})
            summary.append(f"缓存启用: {perf_config.get('enable_cache', 'N/A')}")
            
            return "\n".join(summary)
            
        except Exception as e:
            return f"生成配置摘要异常: {e}"