"""
最终版动态通道突破逻辑测试
正确处理通道上轨计算，确保测试数据能被正确检测
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加strategy目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy'))

from dynamic_tf_helper import dynamic_tf_for_channel, dynamic_tf_for_scoring

class TestChannelBreakthroughFinal:
    def __init__(self):
        pass
    
    def check_channel_breakthrough_debug(self, df, age_days):
        """
        检查通道突破条件 - 带详细调试信息
        """
        print(f"\n=== 通道突破检测详情 ===")
        print(f"币龄: {age_days}天")
        
        if df.empty or len(df) < 10:
            print(f"数据不足: 数据长度={len(df)}, 需要至少10根K线")
            return False
        
        # 根据币龄动态计算通道周期
        n = dynamic_tf_for_channel(age_days)
        print(f"动态通道周期: {n}根K线")
        
        # 确保有足够的数据
        if len(df) < n:
            n = len(df) - 1
            if n < 2:
                print(f"调整后数据仍不足: n={n}")
                return False
            print(f"数据不足，调整通道周期为: {n}根K线")
        
        # 计算通道上下轨
        high_n = df['high'].rolling(window=n, min_periods=1).max()
        low_n = df['low'].rolling(window=n, min_periods=1).min()
        
        # 当前价格
        current_close = df['close'].iloc[-1]
        current_high = df['high'].iloc[-1]
        
        # 通道上轨
        upper_band = high_n.iloc[-1]
        
        print(f"当前收盘价: {current_close:.6f}")
        print(f"当前最高价: {current_high:.6f}")
        print(f"通道上轨: {upper_band:.6f}")
        
        # 检查各个条件
        close_near_upper = current_close >= upper_band * 0.999
        high_break_upper = current_high >= upper_band * 1.001
        
        print(f"收盘价接近上轨 (>= {upper_band * 0.999:.6f}): {close_near_upper}")
        print(f"最高价突破上轨 (>= {upper_band * 1.001:.6f}): {high_break_upper}")
        
        # 突破幅度限制：不超过10%
        breakthrough_ratio = (current_close / upper_band - 1) * 100
        valid_breakthrough = breakthrough_ratio <= 10
        
        print(f"突破幅度: {breakthrough_ratio:.2f}% (限制: <=10%): {valid_breakthrough}")
        
        # 允许5根K线的突破时间
        recent_closes = df['close'].iloc[-5:]
        recent_breakthrough_checks = [close >= upper_band * 0.999 for close in recent_closes]
        any_recent_breakthrough = any(recent_breakthrough_checks)
        
        print(f"最近5根K线收盘价: {[f'{c:.6f}' for c in recent_closes]}")
        print(f"最近5根K线突破检查: {recent_breakthrough_checks}")
        print(f"任意最近K线突破: {any_recent_breakthrough}")
        
        # 最终条件
        breakthrough_condition = (close_near_upper or high_break_upper) and valid_breakthrough and any_recent_breakthrough
        
        print(f"最终突破条件:")
        print(f"  (收盘价接近上轨 OR 最高价突破上轨): {close_near_upper or high_break_upper}")
        print(f"  AND 突破幅度有效: {valid_breakthrough}")
        print(f"  AND 最近有突破: {any_recent_breakthrough}")
        print(f"  = {breakthrough_condition}")
        
        return breakthrough_condition

def create_perfect_breakthrough_data(age_days, length=50):
    """创建完美的突破数据"""
    np.random.seed(42)
    
    # 基础价格
    base_price = 100.0
    
    # 生成稳定的价格数据，避免随机性影响
    data = []
    for i in range(length):
        price = base_price + i * 0.1  # 缓慢上升
        data.append({
            'open': price,
            'high': price + 0.05,
            'low': price - 0.05,
            'close': price,
            'volume': 1000
        })
    
    df = pd.DataFrame(data)
    
    # 获取通道周期
    channel_period = dynamic_tf_for_channel(age_days)
    
    print(f"创建突破数据:")
    print(f"  通道周期: {channel_period}根K线")
    
    if len(df) >= channel_period:
        # 计算前n根K线的最高价（不包括最后一根）
        historical_highs = df['high'].iloc[:-1]
        if len(historical_highs) >= channel_period:
            upper_band = historical_highs.rolling(window=channel_period, min_periods=1).max().iloc[-1]
        else:
            upper_band = historical_highs.max()
        
        print(f"  历史通道上轨: {upper_band:.6f}")
        
        # 设置最后一根K线为明显突破
        breakthrough_close = upper_band * 1.001  # 收盘价突破0.1%
        breakthrough_high = upper_band * 1.002   # 最高价突破0.2%
        
        df.loc[df.index[-1], 'close'] = breakthrough_close
        df.loc[df.index[-1], 'high'] = breakthrough_high
        
        print(f"  设置最后K线收盘价: {breakthrough_close:.6f}")
        print(f"  设置最后K线最高价: {breakthrough_high:.6f}")
        
        # 设置最近几根K线也接近突破
        for i in range(2, min(6, len(df))):
            idx = df.index[-i]
            near_breakthrough = upper_band * 1.0001  # 略微突破
            df.loc[idx, 'close'] = near_breakthrough
            print(f"  设置倒数第{i}根K线收盘价: {near_breakthrough:.6f}")
    
    return df

def test_perfect_breakthrough():
    """测试完美突破场景"""
    print("=== 测试完美突破场景 ===")
    
    tester = TestChannelBreakthroughFinal()
    
    test_ages = [0.5, 2, 15, 100]
    
    for age_days in test_ages:
        print(f"\n{'='*60}")
        print(f"测试币龄: {age_days}天")
        print(f"{'='*60}")
        
        # 生成完美突破数据
        df = create_perfect_breakthrough_data(age_days, length=50)
        
        # 测试突破检测
        result = tester.check_channel_breakthrough_debug(df, age_days)
        
        print(f"\n最终结果: {'✓ 检测到突破' if result else '✗ 未检测到突破'}")
        
        if result:
            print("🎉 成功！动态通道突破逻辑工作正常")
        else:
            print("❌ 失败！需要进一步调试")

def test_dynamic_tf_ranges():
    """测试动态时间框架的范围"""
    print("\n=== 动态时间框架测试 ===")
    
    test_cases = [
        (0.1, "极新币"),
        (0.5, "新币"),
        (2, "较新币"),
        (5, "一周内"),
        (15, "半月"),
        (30, "一月"),
        (60, "两月"),
        (180, "半年"),
        (365, "一年"),
        (500, "老币"),
        (1000, "极老币")
    ]
    
    for age_days, description in test_cases:
        channel_period = dynamic_tf_for_channel(age_days)
        scoring_tf = dynamic_tf_for_scoring(age_days)
        print(f"{description:8s} ({age_days:4.1f}天): 通道={channel_period:2d}根K线, 评分={scoring_tf}")

if __name__ == "__main__":
    print("最终版动态通道突破逻辑测试")
    print("=" * 60)
    
    # 测试动态时间框架
    test_dynamic_tf_ranges()
    
    # 测试完美突破场景
    test_perfect_breakthrough()
    
    print("\n测试完成！")