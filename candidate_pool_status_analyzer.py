#!/usr/bin/env python3
"""
候选池状态分析器 - 分析为什么实时系统显示"候选池为0"但实际有高评分币种
"""

import os
import sys
import pickle
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_score_calculator import EnhancedScoreCalculator

class CandidatePoolStatusAnalyzer:
    def __init__(self):
        self.cache_dir = 'cache'
        self.logger = self._setup_logger()
        self.score_calculator = EnhancedScoreCalculator()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('CandidatePoolStatusAnalyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_candidate_cache(self):
        """加载候选池缓存"""
        cache_file = os.path.join(self.cache_dir, 'candidates.pkl')
        if not os.path.exists(cache_file):
            self.logger.error(f"候选池缓存文件不存在: {cache_file}")
            return None
        
        try:
            with open(cache_file, 'rb') as f:
                candidates = pickle.load(f)
            self.logger.info(f"成功加载候选池缓存: {len(candidates)} 个币种")
            return candidates
        except Exception as e:
            self.logger.error(f"加载候选池缓存失败: {e}")
            return None
    
    def load_cand_cache(self):
        """加载实时系统的cand_cache（字典格式）"""
        cache_file = os.path.join(self.cache_dir, 'cand_cache.pkl')
        if not os.path.exists(cache_file):
            self.logger.warning(f"cand_cache文件不存在: {cache_file}")
            return {}
        
        try:
            with open(cache_file, 'rb') as f:
                cand_cache = pickle.load(f)
            self.logger.info(f"成功加载cand_cache: {len(cand_cache)} 个币种")
            return cand_cache
        except Exception as e:
            self.logger.error(f"加载cand_cache失败: {e}")
            return {}
    
    def simulate_realtime_candidate_count(self, cand_cache):
        """模拟实时系统的候选池计数逻辑"""
        if not cand_cache:
            return 0, 0, "cand_cache为空"
        
        # 模拟实时系统的计数逻辑
        candidate_count = len(cand_cache)
        high_score_count = sum(1 for data in cand_cache.values() if data.get('score', 0) >= 7)
        
        # 检查是否有评分数据
        scored_count = sum(1 for data in cand_cache.values() if data.get('score', 0) > 0)
        unscored_count = candidate_count - scored_count
        
        status_info = {
            'total_count': candidate_count,
            'high_score_count': high_score_count,
            'scored_count': scored_count,
            'unscored_count': unscored_count,
            'avg_score': sum(data.get('score', 0) for data in cand_cache.values()) / candidate_count if candidate_count > 0 else 0
        }
        
        return candidate_count, high_score_count, status_info
    
    def analyze_candidate_pool_discrepancy(self):
        """分析候选池数量显示差异的原因"""
        self.logger.info("开始分析候选池状态显示问题...")
        
        # 1. 加载候选池缓存（列表格式）
        candidates_list = self.load_candidate_cache()
        
        # 2. 加载cand_cache（字典格式）
        cand_cache = self.load_cand_cache()
        
        # 3. 分析数据结构差异
        analysis_results = {
            'candidates_list': {
                'exists': candidates_list is not None,
                'count': len(candidates_list) if candidates_list else 0,
                'type': type(candidates_list).__name__,
                'sample': candidates_list[:5] if candidates_list else []
            },
            'cand_cache': {
                'exists': bool(cand_cache),
                'count': len(cand_cache) if cand_cache else 0,
                'type': type(cand_cache).__name__,
                'sample_keys': list(cand_cache.keys())[:5] if cand_cache else []
            }
        }
        
        # 4. 模拟实时系统状态显示
        if cand_cache:
            candidate_count, high_score_count, status_info = self.simulate_realtime_candidate_count(cand_cache)
            analysis_results['realtime_simulation'] = {
                'candidate_count': candidate_count,
                'high_score_count': high_score_count,
                'status_info': status_info
            }
        else:
            analysis_results['realtime_simulation'] = {
                'candidate_count': 0,
                'high_score_count': 0,
                'status_info': "cand_cache不存在或为空"
            }
        
        # 5. 检查数据一致性
        consistency_check = self.check_data_consistency(candidates_list, cand_cache)
        analysis_results['consistency_check'] = consistency_check
        
        # 6. 生成分析报告
        self.generate_status_analysis_report(analysis_results)
        
        return analysis_results
    
    def check_data_consistency(self, candidates_list, cand_cache):
        """检查候选池数据一致性"""
        consistency_results = {
            'list_vs_cache_count': False,
            'missing_in_cache': [],
            'extra_in_cache': [],
            'score_distribution': {},
            'potential_issues': []
        }
        
        if not candidates_list or not cand_cache:
            consistency_results['potential_issues'].append("候选池数据缺失")
            return consistency_results
        
        # 转换为集合进行比较
        list_symbols = set(candidates_list) if isinstance(candidates_list, list) else set()
        cache_symbols = set(cand_cache.keys()) if isinstance(cand_cache, dict) else set()
        
        # 检查数量一致性
        consistency_results['list_vs_cache_count'] = len(list_symbols) == len(cache_symbols)
        
        # 检查缺失和多余的币种
        consistency_results['missing_in_cache'] = list(list_symbols - cache_symbols)
        consistency_results['extra_in_cache'] = list(cache_symbols - list_symbols)
        
        # 分析评分分布
        if cand_cache:
            scores = [data.get('score', 0) for data in cand_cache.values()]
            consistency_results['score_distribution'] = {
                'total_count': len(scores),
                'scored_count': sum(1 for s in scores if s > 0),
                'unscored_count': sum(1 for s in scores if s == 0),
                'high_score_count': sum(1 for s in scores if s >= 7),
                'avg_score': sum(scores) / len(scores) if scores else 0,
                'max_score': max(scores) if scores else 0,
                'min_score': min(scores) if scores else 0
            }
        
        # 识别潜在问题
        if consistency_results['missing_in_cache']:
            consistency_results['potential_issues'].append(f"cand_cache中缺失 {len(consistency_results['missing_in_cache'])} 个币种")
        
        if consistency_results['extra_in_cache']:
            consistency_results['potential_issues'].append(f"cand_cache中多出 {len(consistency_results['extra_in_cache'])} 个币种")
        
        if consistency_results['score_distribution'].get('unscored_count', 0) > 0:
            consistency_results['potential_issues'].append(f"有 {consistency_results['score_distribution']['unscored_count']} 个币种未评分")
        
        return consistency_results
    
    def generate_status_analysis_report(self, analysis_results):
        """生成状态分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"candidate_pool_status_analysis_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("候选池状态显示问题分析报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 数据结构分析
            f.write("数据结构分析:\n")
            f.write("-" * 40 + "\n")
            
            candidates_info = analysis_results['candidates_list']
            f.write(f"候选池列表 (candidates.pkl):\n")
            f.write(f"  存在: {candidates_info['exists']}\n")
            f.write(f"  数量: {candidates_info['count']}\n")
            f.write(f"  类型: {candidates_info['type']}\n")
            f.write(f"  样本: {candidates_info['sample']}\n\n")
            
            cand_cache_info = analysis_results['cand_cache']
            f.write(f"候选池缓存 (cand_cache.pkl):\n")
            f.write(f"  存在: {cand_cache_info['exists']}\n")
            f.write(f"  数量: {cand_cache_info['count']}\n")
            f.write(f"  类型: {cand_cache_info['type']}\n")
            f.write(f"  样本键: {cand_cache_info['sample_keys']}\n\n")
            
            # 实时系统模拟结果
            f.write("实时系统状态模拟:\n")
            f.write("-" * 40 + "\n")
            realtime_sim = analysis_results['realtime_simulation']
            f.write(f"候选池数量: {realtime_sim['candidate_count']}\n")
            f.write(f"高评分币种(≥7分): {realtime_sim['high_score_count']}\n")
            
            if isinstance(realtime_sim['status_info'], dict):
                status = realtime_sim['status_info']
                f.write(f"已评分币种: {status['scored_count']}\n")
                f.write(f"未评分币种: {status['unscored_count']}\n")
                f.write(f"平均评分: {status['avg_score']:.2f}\n")
            else:
                f.write(f"状态信息: {realtime_sim['status_info']}\n")
            f.write("\n")
            
            # 数据一致性检查
            f.write("数据一致性检查:\n")
            f.write("-" * 40 + "\n")
            consistency = analysis_results['consistency_check']
            f.write(f"列表与缓存数量一致: {consistency['list_vs_cache_count']}\n")
            f.write(f"缓存中缺失币种: {len(consistency['missing_in_cache'])} 个\n")
            f.write(f"缓存中多余币种: {len(consistency['extra_in_cache'])} 个\n")
            
            if consistency['missing_in_cache']:
                f.write(f"缺失币种: {consistency['missing_in_cache'][:10]}\n")
            
            if consistency['extra_in_cache']:
                f.write(f"多余币种: {consistency['extra_in_cache'][:10]}\n")
            
            # 评分分布
            score_dist = consistency['score_distribution']
            if score_dist:
                f.write(f"\n评分分布:\n")
                f.write(f"  总币种数: {score_dist['total_count']}\n")
                f.write(f"  已评分: {score_dist['scored_count']}\n")
                f.write(f"  未评分: {score_dist['unscored_count']}\n")
                f.write(f"  高评分(≥7分): {score_dist['high_score_count']}\n")
                f.write(f"  平均评分: {score_dist['avg_score']:.2f}\n")
                f.write(f"  最高评分: {score_dist['max_score']:.2f}\n")
                f.write(f"  最低评分: {score_dist['min_score']:.2f}\n")
            
            # 潜在问题
            f.write(f"\n潜在问题:\n")
            f.write("-" * 40 + "\n")
            if consistency['potential_issues']:
                for i, issue in enumerate(consistency['potential_issues'], 1):
                    f.write(f"{i}. {issue}\n")
            else:
                f.write("未发现明显问题\n")
            
            # 关键发现和建议
            f.write(f"\n关键发现:\n")
            f.write("-" * 40 + "\n")
            f.write("1. 实时系统使用 cand_cache (字典格式) 进行状态显示\n")
            f.write("2. 增量脚本使用 candidates.pkl (列表格式) 进行分析\n")
            f.write("3. 两个数据源可能存在同步问题\n")
            f.write("4. 实时系统显示的候选池数量基于 len(cand_cache)\n")
            f.write("5. 如果 cand_cache 为空或不存在，会显示候选池为0\n\n")
            
            f.write("建议:\n")
            f.write("-" * 40 + "\n")
            f.write("1. 检查 cand_cache.pkl 文件是否正常生成和更新\n")
            f.write("2. 确保实时系统正确初始化和维护 cand_cache\n")
            f.write("3. 添加数据同步机制，确保两个数据源一致\n")
            f.write("4. 在实时系统中添加更详细的状态日志\n")
            f.write("5. 考虑统一使用一个数据源进行候选池管理\n")
        
        self.logger.info(f"状态分析报告已生成: {report_file}")

def main():
    """主函数"""
    analyzer = CandidatePoolStatusAnalyzer()
    analyzer.analyze_candidate_pool_discrepancy()

if __name__ == "__main__":
    main()