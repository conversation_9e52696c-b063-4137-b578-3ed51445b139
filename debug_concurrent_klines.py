#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发K线数据获取调试脚本
模拟策略的实际运行环境，测试并发请求和symbols列表状态
"""

import sys
import json
import time
import asyncio
import concurrent.futures
from datetime import datetime

def debug_concurrent_klines():
    """调试并发K线数据获取"""
    print("开始调试并发K线数据获取...")
    
    try:
        # 1. 导入必要模块
        print("1. 导入模块...")
        from binance_trader import BinanceTrader
        
        # 2. 加载配置
        print("2. 加载配置...")
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 3. 初始化交易器
        print("3. 初始化BinanceTrader...")
        trader = BinanceTrader(config)
        
        # 4. 检查symbols列表状态
        print(f"4. 检查symbols列表状态...")
        print(f"   总交易对数量: {len(trader.symbols)}")
        
        # 检查问题币种是否在symbols列表中
        problem_symbols = ['ALPINEUSDT', 'BCHUSDT', 'EGLDUSDT', 'ARUSDT', 'MYXUSDT']
        print(f"   检查问题币种:")
        for symbol in problem_symbols:
            in_symbols = symbol in trader.symbols
            print(f"   - {symbol}: {'✅' if in_symbols else '❌'} {'在列表中' if in_symbols else '不在列表中'}")
        
        # 5. 测试单个请求
        print(f"\n5. 测试单个请求...")
        for symbol in problem_symbols[:3]:  # 只测试前3个
            try:
                start_time = time.time()
                klines = trader.get_klines(symbol, '15m', 20)
                end_time = time.time()
                
                print(f"   {symbol}: {type(klines)}, 长度: {len(klines) if klines else 0}, 耗时: {end_time-start_time:.2f}s")
                
            except Exception as e:
                print(f"   {symbol}: 异常 - {e}")
        
        # 6. 测试并发请求（模拟策略的并发场景）
        print(f"\n6. 测试并发请求...")
        
        def get_klines_with_info(symbol):
            """获取K线数据并返回详细信息"""
            try:
                start_time = time.time()
                
                # 检查symbol是否在列表中
                in_symbols = symbol in trader.symbols
                
                # 获取K线数据
                klines = trader.get_klines(symbol, '15m', 20)
                
                end_time = time.time()
                
                return {
                    'symbol': symbol,
                    'in_symbols': in_symbols,
                    'result_type': type(klines).__name__,
                    'result_length': len(klines) if klines else 0,
                    'duration': end_time - start_time,
                    'success': klines is not None and len(klines) > 0,
                    'error': None
                }
            except Exception as e:
                return {
                    'symbol': symbol,
                    'in_symbols': False,
                    'result_type': 'Exception',
                    'result_length': 0,
                    'duration': 0,
                    'success': False,
                    'error': str(e)
                }
        
        # 并发测试
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            # 提交所有任务
            futures = {executor.submit(get_klines_with_info, symbol): symbol for symbol in problem_symbols}
            
            # 收集结果
            results = []
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                results.append(result)
        
        # 显示并发测试结果
        print(f"   并发测试结果:")
        for result in sorted(results, key=lambda x: x['symbol']):
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['symbol']}: 在列表={result['in_symbols']}, "
                  f"类型={result['result_type']}, 长度={result['result_length']}, "
                  f"耗时={result['duration']:.2f}s")
            if result['error']:
                print(f"      错误: {result['error']}")
        
        # 7. 测试高频请求（模拟策略的高频场景）
        print(f"\n7. 测试高频请求...")
        test_symbol = 'BTCUSDT'
        success_count = 0
        fail_count = 0
        
        for i in range(10):
            try:
                klines = trader.get_klines(test_symbol, '15m', 20)
                if klines and len(klines) > 0:
                    success_count += 1
                else:
                    fail_count += 1
                    print(f"   第{i+1}次请求失败: {type(klines)}, 长度: {len(klines) if klines else 0}")
            except Exception as e:
                fail_count += 1
                print(f"   第{i+1}次请求异常: {e}")
            
            time.sleep(0.1)  # 短暂间隔
        
        print(f"   高频测试结果: 成功{success_count}次, 失败{fail_count}次")
        
        # 8. 检查HTTP客户端状态
        print(f"\n8. 检查HTTP客户端状态...")
        try:
            # 测试服务器时间
            server_time = trader.http.get('/fapi/v1/time')
            print(f"   服务器时间: {server_time}")
            
            # 测试交易对信息
            exchange_info = trader.http.get('/fapi/v1/exchangeInfo')
            if isinstance(exchange_info, dict) and 'symbols' in exchange_info:
                api_symbols_count = len(exchange_info['symbols'])
                print(f"   API返回交易对数量: {api_symbols_count}")
                print(f"   本地缓存交易对数量: {len(trader.symbols)}")
            else:
                print(f"   获取交易对信息失败: {type(exchange_info)}")
                
        except Exception as e:
            print(f"   HTTP客户端检查异常: {e}")
        
        print("\n🔍 并发调试完成！")
        
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_concurrent_klines()