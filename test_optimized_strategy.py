#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化策略测试脚本
测试监控系统、策略优化器和增强评分系统的集成效果
"""

import sys
import os
import time
import logging
import json
from datetime import datetime, timedelta
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要模块
from binance_trader import BinanceTrader
from strategy.maker_channel import MakerChannelStrategy
from 核心指标监控系统 import MetricsCollector
from strategy_optimizer import StrategyOptimizer, OptimizationConfig
from enhanced_score_calculator import EnhancedScoreCalculator

class StrategyTester:
    """策略测试器"""
    
    def __init__(self):
        self.log = logging.getLogger('StrategyTester')
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('test_results.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
    def test_config_manager(self):
        """测试配置管理器"""
        self.log.info("=== 测试配置管理器 ===")
        try:
            from config_manager import ConfigManager
            
            # 创建配置管理器实例
            config_manager = ConfigManager()
            
            # 测试配置加载
            config = config_manager.get_config('main')
            if config:
                self.log.info(f"主配置加载成功: {len(config)} 个配置项")
            else:
                self.log.info("主配置为空，使用默认配置")
            
            # 测试配置验证
            validation_result = config_manager.validate_all_configs()
            self.log.info(f"配置验证结果: {validation_result}")
            
            return True
            
        except Exception as e:
            self.log.error(f"配置管理器测试失败: {e}")
            return False
    
    def test_enhanced_score_calculator(self):
        """测试增强评分计算器"""
        self.log.info("=== 测试增强评分计算器 ===")
        try:
            calculator = EnhancedScoreCalculator()
            
            # 创建模拟K线数据
            dates = pd.date_range(start='2024-01-01', periods=100, freq='15min')
            mock_df = pd.DataFrame({
                'o': [100 + i * 0.1 for i in range(100)],
                'h': [101 + i * 0.1 for i in range(100)],
                'l': [99 + i * 0.1 for i in range(100)],
                'c': [100.5 + i * 0.1 for i in range(100)],
                'v': [1000 + i * 10 for i in range(100)]
            }, index=dates)
            
            # 测试评分计算
            result = calculator.calculate_comprehensive_score('BTCUSDT', mock_df, depth_data=1000.0)
            self.log.info(f"综合评分: {result.total_score:.2f}")
            
            # 测试各组件评分
            for component, value in result.component_scores.items():
                self.log.info(f"{component}: {value:.2f}")
            
            # 测试数据质量评估
            self.log.info(f"数据质量评分: {result.data_quality:.2f}")
            
            return True
            
        except Exception as e:
            self.log.error(f"增强评分计算器测试失败: {e}")
            return False
    
    def test_metrics_collector(self):
        """测试监控系统"""
        self.log.info("=== 测试监控系统 ===")
        try:
            config = {
                'monitoring_interval': 5,
                'alert_thresholds': {
                    'order_success_rate': 0.9,
                    'api_success_rate': 0.95,
                    'max_drawdown': 0.1,
                    'elimination_frequency': 5.0
                }
            }
            
            collector = MetricsCollector(config)
            collector.start_monitoring()
            
            # 模拟记录一些指标
            collector.record_order({
                'symbol': 'BTCUSDT',
                'side': 'BUY',
                'type': 'LIMIT',
                'status': 'FILLED',
                'execution_time': 100
            })
            
            collector.record_order({
                'symbol': 'ETHUSDT',
                'side': 'SELL',
                'type': 'LIMIT',
                'status': 'CANCELLED',
                'execution_time': 50
            })
            
            collector.record_position({
                'symbol': 'BTCUSDT',
                'side': 'LONG',
                'size': 0.1,
                'entry_price': 50000,
                'exit_price': 50150,
                'pnl': 15.0,
                'is_profitable': True
            })
            
            collector.record_elimination({
                'symbol': 'ADAUSDT',
                'reason': 'low_score',
                'score': 3.5
            })
            
            # 记录API调用
            collector.record_api_call(True, 50)
            collector.record_api_call(False, 200)
            
            # 等待一段时间让监控系统处理数据
            time.sleep(2)
            
            # 获取当前指标
            metrics = collector.get_current_metrics()
            self.log.info(f"当前指标: {json.dumps(metrics, indent=2, ensure_ascii=False)}")
            
            # 生成摘要
            summary = collector.get_metrics_summary()
            self.log.info(f"监控摘要: {summary}")
            
            collector.stop_monitoring()
            return True
            
        except Exception as e:
            self.log.error(f"监控系统测试失败: {e}")
            return False
    
    def test_strategy_optimizer(self):
        """测试策略优化器"""
        self.log.info("=== 测试策略优化器 ===")
        try:
            config = OptimizationConfig(
                max_concurrent_tasks=5,
                max_worker_threads=3,
                enable_caching=True,
                cache_ttl=60,
                batch_size=10,
                enable_profiling=True
            )
            
            optimizer = StrategyOptimizer(config)
            optimizer.start()
            
            # 测试缓存功能
            optimizer.cache_manager.set('test_key', {'data': 'test_value'})
            cached_data = optimizer.cache_manager.get('test_key')
            self.log.info(f"缓存测试: {cached_data}")
            
            # 测试批处理功能
            future = optimizer.submit_batch_task('test_task', {'test': 'data'})
            result = future.result(timeout=5)
            self.log.info(f"批处理结果: {result}")
            
            # 获取性能指标
            performance = optimizer.get_performance_metrics()
            self.log.info(f"优化器性能指标: {json.dumps(performance, indent=2, ensure_ascii=False)}")
            
            optimizer.stop()
            return True
            
        except Exception as e:
            self.log.error(f"策略优化器测试失败: {e}")
            return False
    
    def test_integration(self):
        """测试系统集成"""
        self.log.info("=== 测试系统集成 ===")
        try:
            # 模拟配置
            config = {
                'first_nominal': 100,
                'symbol_limit': 50,
                'min_score': 7,
                'monitoring_interval': 30,
                'min_order_success_rate': 0.9,
                'min_api_success_rate': 0.95,
                'max_drawdown_threshold': 0.1,
                'max_elimination_frequency': 5.0,
                'max_concurrent_tasks': 10,
                'max_worker_threads': 5,
                'enable_optimization_caching': True,
                'optimization_cache_ttl': 300,
                'optimization_batch_size': 50,
                'enable_profiling': True
            }
            
            # 创建模拟交易器（不连接真实API）
            class MockTrader:
                def __init__(self):
                    self.http = self.MockHttpClient()
                    
                class MockHttpClient:
                    def get(self, endpoint, params=None):
                        # 返回模拟数据
                        if endpoint == '/fapi/v1/ping':
                            return {}
                        elif endpoint == '/fapi/v1/ticker/24hr':
                            return [
                                {
                                    'symbol': 'BTCUSDT',
                                    'priceChangePercent': '2.5',
                                    'quoteVolume': '1000000',
                                    'lastPrice': '50000'
                                },
                                {
                                    'symbol': 'ETHUSDT',
                                    'priceChangePercent': '1.8',
                                    'quoteVolume': '800000',
                                    'lastPrice': '3000'
                                }
                            ]
                        elif endpoint == '/fapi/v1/exchangeInfo':
                            return {
                                'symbols': [
                                    {
                                        'symbol': 'BTCUSDT',
                                        'contractType': 'PERPETUAL',
                                        'status': 'TRADING'
                                    },
                                    {
                                        'symbol': 'ETHUSDT',
                                        'contractType': 'PERPETUAL',
                                        'status': 'TRADING'
                                    }
                                ]
                            }
                        elif endpoint == '/fapi/v2/positionRisk':
                            return []
                        return None
            
            mock_trader = MockTrader()
            
            # 创建策略实例
            strategy = MakerChannelStrategy(mock_trader, config)
            
            # 测试预热功能
            strategy.warmup()
            self.log.info("策略预热完成")
            
            # 测试异步打分
            strategy._async_first_score_batch()
            self.log.info("异步打分测试完成")
            
            # 测试网络状态检查
            strategy._check_network_status()
            self.log.info(f"网络状态: {strategy.network_status}")
            
            # 测试订单生命周期管理
            strategy._manage_order_lifecycle()
            self.log.info("订单生命周期管理测试完成")
            
            # 获取监控指标
            metrics = strategy.metrics_collector.get_current_metrics()
            self.log.info(f"策略监控指标: {json.dumps(metrics, indent=2, ensure_ascii=False)}")
            
            # 停止监控和优化器
            strategy.metrics_collector.stop_monitoring()
            strategy.strategy_optimizer.stop()
            
            return True
            
        except Exception as e:
            self.log.error(f"系统集成测试失败: {e}")
            return False
    
    def run_performance_test(self):
        """运行性能测试"""
        self.log.info("=== 性能测试 ===")
        try:
            start_time = time.time()
            
            # 测试增强评分计算器性能
            calculator = EnhancedScoreCalculator()
            
            # 创建大量模拟数据
            dates = pd.date_range(start='2024-01-01', periods=1000, freq='1min')
            large_df = pd.DataFrame({
                'o': [100 + i * 0.01 for i in range(1000)],
                'h': [101 + i * 0.01 for i in range(1000)],
                'l': [99 + i * 0.01 for i in range(1000)],
                'c': [100.5 + i * 0.01 for i in range(1000)],
                'v': [1000 + i for i in range(1000)]
            }, index=dates)
            
            # 批量评分测试
            scores = []
            for i in range(100):
                result = calculator.calculate_comprehensive_score(f'TEST{i}USDT', large_df, depth_data=1000.0)
                scores.append(result.total_score)
            
            end_time = time.time()
            duration = end_time - start_time
            
            self.log.info(f"性能测试完成:")
            self.log.info(f"- 处理了100次大数据集评分")
            self.log.info(f"- 每个数据集包含1000个数据点")
            self.log.info(f"- 总耗时: {duration:.2f}秒")
            self.log.info(f"- 平均每次评分耗时: {duration/100:.4f}秒")
            self.log.info(f"- 评分范围: {min(scores):.2f} - {max(scores):.2f}")
            
            return True
            
        except Exception as e:
            self.log.error(f"性能测试失败: {e}")
            return False
    
    def generate_test_report(self, results):
        """生成测试报告"""
        self.log.info("=== 生成测试报告 ===")
        
        report = {
            'test_time': datetime.now().isoformat(),
            'test_results': results,
            'summary': {
                'total_tests': len(results),
                'passed_tests': sum(1 for r in results.values() if r),
                'failed_tests': sum(1 for r in results.values() if not r),
                'success_rate': sum(1 for r in results.values() if r) / len(results) * 100
            }
        }
        
        # 保存报告
        with open('test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.log.info(f"测试报告已保存到 test_report.json")
        self.log.info(f"测试总结:")
        self.log.info(f"- 总测试数: {report['summary']['total_tests']}")
        self.log.info(f"- 通过测试: {report['summary']['passed_tests']}")
        self.log.info(f"- 失败测试: {report['summary']['failed_tests']}")
        self.log.info(f"- 成功率: {report['summary']['success_rate']:.1f}%")
        
        return report
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log.info("开始运行优化策略测试套件...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['config_manager'] = self.test_config_manager()
        test_results['enhanced_score_calculator'] = self.test_enhanced_score_calculator()
        test_results['metrics_collector'] = self.test_metrics_collector()
        test_results['strategy_optimizer'] = self.test_strategy_optimizer()
        test_results['integration'] = self.test_integration()
        test_results['performance'] = self.run_performance_test()
        
        # 生成测试报告
        report = self.generate_test_report(test_results)
        
        return report

def main():
    """主函数"""
    tester = StrategyTester()
    report = tester.run_all_tests()
    
    if report['summary']['success_rate'] >= 80:
        print("\n✅ 测试通过！优化策略系统运行正常")
        return 0
    else:
        print("\n❌ 测试失败！需要检查和修复问题")
        return 1

if __name__ == '__main__':
    exit(main())