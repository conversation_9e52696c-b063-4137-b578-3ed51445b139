#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查COAIUSDT数据问题
"""

import json
from binance_trader import BinanceTrader
import pandas as pd

def check_coai_data():
    """检查COAIUSDT数据"""
    try:
        # 加载配置
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        trader = BinanceTrader(config)
        
        print("=== 检查COAIUSDT数据 ===")
        
        # 1. 检查交易对是否存在
        try:
            exchange_info = trader.get_exchange_info()
            if exchange_info and 'symbols' in exchange_info:
                symbols = [s for s in exchange_info['symbols'] if s['symbol'] == 'COAIUSDT']
                print(f"交易对存在: {len(symbols) > 0}")
                if symbols:
                    symbol_info = symbols[0]
                    print(f"交易对状态: {symbol_info['status']}")
                    print(f"基础资产: {symbol_info['baseAsset']}")
                    print(f"报价资产: {symbol_info['quoteAsset']}")
            else:
                print("无法获取交易所信息")
        except Exception as e:
            print(f"检查交易对失败: {e}")
        
        # 2. 获取K线数据
        try:
            klines = trader.get_klines('COAIUSDT', '15m', limit=100)
            print(f"K线数据条数: {len(klines)}")
            
            if len(klines) > 0:
                print(f"最新数据时间: {klines.iloc[-1]['t']}")
                print(f"最新价格: {klines.iloc[-1]['c']}")
                print(f"数据列: {list(klines.columns)}")
            else:
                print("没有K线数据")
                
        except Exception as e:
            print(f"获取K线数据失败: {e}")
        
        # 3. 检查深度数据
        try:
            depth = trader.get_order_book('COAIUSDT', limit=10)
            print(f"深度数据: 买单{len(depth['bids'])}条, 卖单{len(depth['asks'])}条")
            if depth['bids']:
                print(f"最高买价: {depth['bids'][0][0]}")
            if depth['asks']:
                print(f"最低卖价: {depth['asks'][0][0]}")
        except Exception as e:
            print(f"获取深度数据失败: {e}")
            
        # 4. 检查24小时统计
        try:
            ticker = trader.get_ticker('COAIUSDT')
            if ticker:
                print(f"24小时统计:")
                print(f"  价格: {ticker['lastPrice']}")
                print(f"  成交量: {ticker['volume']}")
                print(f"  涨跌幅: {ticker['priceChangePercent']}%")
            else:
                print("无法获取24小时统计")
        except Exception as e:
            print(f"获取24小时统计失败: {e}")
            
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_coai_data()