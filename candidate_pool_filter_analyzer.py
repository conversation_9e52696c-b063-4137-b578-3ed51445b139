#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
候选池过滤条件分析脚本
深入分析实时系统候选池管理中的过滤条件和淘汰机制
"""

import pickle
import json
import os
import pandas as pd
import numpy as np
from datetime import datetime
from enhanced_score_calculator import EnhancedScoreCalculator
import logging

class CandidatePoolFilterAnalyzer:
    """候选池过滤条件分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = logging.getLogger('candidate_pool_filter_analyzer')
        self.logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        self.score_calculator = EnhancedScoreCalculator()
        self.cache_dir = 'cache'
        
    def load_candidate_cache(self):
        """加载候选池缓存"""
        cache_file = os.path.join(self.cache_dir, 'candidates.pkl')
        if not os.path.exists(cache_file):
            self.logger.error(f"候选池缓存文件不存在: {cache_file}")
            return []
        
        try:
            with open(cache_file, 'rb') as f:
                candidates = pickle.load(f)
            self.logger.info(f"成功加载候选池缓存: {len(candidates)} 个币种")
            return candidates
        except Exception as e:
            self.logger.error(f"加载候选池缓存失败: {e}")
            return []
    
    def simulate_candidate_pool_management(self, candidates_data):
        """模拟候选池管理逻辑"""
        self.logger.info("开始模拟候选池管理逻辑...")
        
        # 模拟cand_cache结构（字典格式）
        simulated_cand_cache = {}
        
        # 如果candidates是列表，转换为字典格式
        if isinstance(candidates_data, list):
            for symbol in candidates_data:
                # 为每个币种创建模拟数据
                simulated_cand_cache[symbol] = {
                    'score': 0,  # 待计算
                    'depth': 0,  # 待获取
                    'age': 30,   # 默认30天
                    'fail_count': 0,
                    'last_check': datetime.now().timestamp()
                }
        elif isinstance(candidates_data, dict):
            simulated_cand_cache = candidates_data.copy()
        
        self.logger.info(f"模拟候选池包含 {len(simulated_cand_cache)} 个币种")
        
        # 为每个币种计算实际评分
        for symbol in simulated_cand_cache:
            try:
                # 加载K线数据
                df = self.load_kline_data(symbol)
                if df is None or len(df) < 50:
                    simulated_cand_cache[symbol]['score'] = 0
                    continue
                
                # 加载深度数据
                depth = self.load_depth_data(symbol)
                if depth is None:
                    depth = 0
                
                # 计算评分
                score_result = self.score_calculator.calculate_comprehensive_score(
                    symbol, 
                    df, 
                    depth_data=depth,
                    additional_data={'age_days': 30}
                )
                
                total_score = score_result.total_score if hasattr(score_result, 'total_score') else score_result
                
                # 更新模拟数据
                simulated_cand_cache[symbol]['score'] = total_score
                simulated_cand_cache[symbol]['depth'] = depth
                
                self.logger.info(f"{symbol}: 评分={total_score:.2f}, 深度={depth:.2f}")
                
            except Exception as e:
                self.logger.error(f"{symbol} 评分计算失败: {e}")
                simulated_cand_cache[symbol]['score'] = 0
        
        return simulated_cand_cache
    
    def apply_filter_conditions(self, cand_cache):
        """应用过滤条件"""
        self.logger.info("应用候选池过滤条件...")
        
        filter_results = {
            'original_count': len(cand_cache),
            'score_filter': {'passed': 0, 'failed': 0, 'failed_symbols': []},
            'fail_count_filter': {'passed': 0, 'failed': 0, 'failed_symbols': []},
            'capacity_filter': {'passed': 0, 'failed': 0, 'failed_symbols': []},
            'final_count': 0,
            'final_symbols': []
        }
        
        # 1. 评分过滤（≥7分）
        score_qualified = {}
        for symbol, data in cand_cache.items():
            score = data.get('score', 0)
            if score >= 7:
                score_qualified[symbol] = data
                filter_results['score_filter']['passed'] += 1
            else:
                filter_results['score_filter']['failed'] += 1
                filter_results['score_filter']['failed_symbols'].append({
                    'symbol': symbol,
                    'score': score,
                    'reason': f'评分{score:.2f}低于7分阈值'
                })
        
        self.logger.info(f"评分过滤: {filter_results['score_filter']['passed']} 通过, {filter_results['score_filter']['failed']} 失败")
        
        # 2. 失败计数过滤（连续3次不达标才移除）
        fail_count_qualified = {}
        for symbol, data in score_qualified.items():
            fail_count = data.get('fail_count', 0)
            if fail_count < 3:
                fail_count_qualified[symbol] = data
                filter_results['fail_count_filter']['passed'] += 1
            else:
                filter_results['fail_count_filter']['failed'] += 1
                filter_results['fail_count_filter']['failed_symbols'].append({
                    'symbol': symbol,
                    'fail_count': fail_count,
                    'reason': f'连续{fail_count}次不达标'
                })
        
        self.logger.info(f"失败计数过滤: {filter_results['fail_count_filter']['passed']} 通过, {filter_results['fail_count_filter']['failed']} 失败")
        
        # 3. 容量限制过滤（最大50个）
        max_candidates = 50
        if len(fail_count_qualified) > max_candidates:
            # 应用智能淘汰机制
            final_qualified = self.apply_smart_elimination(fail_count_qualified, max_candidates)
            eliminated_count = len(fail_count_qualified) - len(final_qualified)
            filter_results['capacity_filter']['failed'] = eliminated_count
            filter_results['capacity_filter']['passed'] = len(final_qualified)
            
            # 记录被淘汰的币种
            eliminated_symbols = set(fail_count_qualified.keys()) - set(final_qualified.keys())
            for symbol in eliminated_symbols:
                data = fail_count_qualified[symbol]
                filter_results['capacity_filter']['failed_symbols'].append({
                    'symbol': symbol,
                    'score': data.get('score', 0),
                    'reason': '容量限制智能淘汰'
                })
        else:
            final_qualified = fail_count_qualified
            filter_results['capacity_filter']['passed'] = len(final_qualified)
        
        self.logger.info(f"容量限制过滤: {filter_results['capacity_filter']['passed']} 通过, {filter_results['capacity_filter']['failed']} 失败")
        
        filter_results['final_count'] = len(final_qualified)
        filter_results['final_symbols'] = list(final_qualified.keys())
        
        return filter_results, final_qualified
    
    def apply_smart_elimination(self, cand_cache, target_size):
        """应用智能淘汰机制"""
        self.logger.info(f"应用智能淘汰机制，目标容量: {target_size}")
        
        # 计算综合评分
        scored_candidates = []
        for symbol, data in cand_cache.items():
            # 这里简化处理，假设没有持仓币种
            is_position = False
            priority_bonus = 100 if is_position else 0
            
            # 归一化各项指标
            score_norm = min(data.get('score', 0) / 10, 1.0)  # 评分满分10分
            depth_norm = min(data.get('depth', 0) / 50000, 1.0)  # 深度满分5万美元
            age_norm = min(data.get('age', 0) / 365, 1.0)  # 币龄满分365天
            
            # 加权综合评分 + 持仓优先级
            composite_score = (score_norm * 0.4 + depth_norm * 0.35 + age_norm * 0.25) + priority_bonus
            
            scored_candidates.append({
                'symbol': symbol,
                'composite_score': composite_score,
                'score': data.get('score', 0),
                'depth': data.get('depth', 0),
                'age': data.get('age', 0)
            })
        
        # 按综合评分排序
        scored_candidates.sort(key=lambda x: x['composite_score'], reverse=True)
        
        # 保留前N名
        keep_symbols = [item['symbol'] for item in scored_candidates[:target_size]]
        
        # 返回保留的币种
        return {symbol: cand_cache[symbol] for symbol in keep_symbols}
    
    def load_kline_data(self, symbol):
        """加载K线数据"""
        possible_files = [
            os.path.join(self.cache_dir, f'klines_{symbol}_15m_200.pkl'),
            os.path.join(self.cache_dir, f'klines_{symbol}_15m_50.pkl'),
            os.path.join(self.cache_dir, f'klines_{symbol}_1d_200.pkl'),
            os.path.join(self.cache_dir, f'klines_{symbol}.json')
        ]
        
        for kline_file in possible_files:
            if os.path.exists(kline_file):
                try:
                    if kline_file.endswith('.pkl'):
                        df = pd.read_pickle(kline_file)
                        if 'timestamp' not in df.columns and df.index.name == 'timestamp':
                            df = df.reset_index()
                        
                        column_mapping = {
                            'open': 'o', 'high': 'h', 'low': 'l', 'close': 'c', 'volume': 'v'
                        }
                        df = df.rename(columns=column_mapping)
                        
                        required_cols = ['o', 'h', 'l', 'c', 'v']
                        if all(col in df.columns for col in required_cols):
                            return df
                    else:
                        with open(kline_file, 'r') as f:
                            klines = json.load(f)
                        df = pd.DataFrame(klines, columns=['timestamp', 'o', 'h', 'l', 'c', 'v', 'close_time', 'quote_volume', 'count', 'taker_buy_volume', 'taker_buy_quote_volume', 'ignore'])
                        df = df[['timestamp', 'o', 'h', 'l', 'c', 'v']].astype(float)
                        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                        return df
                except Exception as e:
                    continue
        
        return None
    
    def load_depth_data(self, symbol):
        """加载深度数据"""
        depth_file = os.path.join(self.cache_dir, f'depth_{symbol}.json')
        if not os.path.exists(depth_file):
            return 0.0
        
        try:
            with open(depth_file, 'r') as f:
                depth_data = json.load(f)
            
            if isinstance(depth_data, (int, float)):
                return float(depth_data)
            elif isinstance(depth_data, dict) and 'bids' in depth_data and 'asks' in depth_data:
                bids = depth_data.get('bids', [])
                asks = depth_data.get('asks', [])
                
                if not bids or not asks:
                    return 0.0
                
                best_bid = float(bids[0][0])
                best_ask = float(asks[0][0])
                mid_price = (best_bid + best_ask) / 2
                
                bid_threshold = mid_price * 0.999
                ask_threshold = mid_price * 1.001
                
                bid_depth = sum(float(price) * float(qty) for price, qty in bids if float(price) >= bid_threshold)
                ask_depth = sum(float(price) * float(qty) for price, qty in asks if float(price) <= ask_threshold)
                
                return min(bid_depth, ask_depth)
            else:
                return 0.0
                
        except Exception as e:
            return 0.0
    
    def generate_filter_analysis_report(self, filter_results, final_qualified):
        """生成过滤条件分析报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'candidate_pool_filter_analysis_{timestamp}.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("候选池过滤条件分析报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("过滤流程统计:\n")
            f.write("-" * 80 + "\n")
            f.write(f"原始候选池数量: {filter_results['original_count']}\n")
            f.write(f"评分过滤(≥7分): {filter_results['score_filter']['passed']} 通过, {filter_results['score_filter']['failed']} 失败\n")
            f.write(f"失败计数过滤(<3次): {filter_results['fail_count_filter']['passed']} 通过, {filter_results['fail_count_filter']['failed']} 失败\n")
            f.write(f"容量限制过滤(≤50个): {filter_results['capacity_filter']['passed']} 通过, {filter_results['capacity_filter']['failed']} 失败\n")
            f.write(f"最终候选池数量: {filter_results['final_count']}\n\n")
            
            # 评分过滤失败详情
            if filter_results['score_filter']['failed_symbols']:
                f.write("评分过滤失败币种详情:\n")
                f.write("-" * 80 + "\n")
                for item in sorted(filter_results['score_filter']['failed_symbols'], key=lambda x: x['score'], reverse=True):
                    f.write(f"{item['symbol']}: {item['score']:.2f}分 - {item['reason']}\n")
                f.write("\n")
            
            # 失败计数过滤失败详情
            if filter_results['fail_count_filter']['failed_symbols']:
                f.write("失败计数过滤失败币种详情:\n")
                f.write("-" * 80 + "\n")
                for item in filter_results['fail_count_filter']['failed_symbols']:
                    f.write(f"{item['symbol']}: {item['reason']}\n")
                f.write("\n")
            
            # 容量限制过滤失败详情
            if filter_results['capacity_filter']['failed_symbols']:
                f.write("容量限制过滤失败币种详情:\n")
                f.write("-" * 80 + "\n")
                for item in sorted(filter_results['capacity_filter']['failed_symbols'], key=lambda x: x['score'], reverse=True):
                    f.write(f"{item['symbol']}: {item['score']:.2f}分 - {item['reason']}\n")
                f.write("\n")
            
            # 最终通过的币种
            if final_qualified:
                f.write("最终通过过滤的币种:\n")
                f.write("-" * 80 + "\n")
                sorted_final = sorted(final_qualified.items(), key=lambda x: x[1].get('score', 0), reverse=True)
                for symbol, data in sorted_final:
                    f.write(f"{symbol}: 评分={data.get('score', 0):.2f}, 深度={data.get('depth', 0):.2f}, 失败次数={data.get('fail_count', 0)}\n")
                f.write("\n")
            
            f.write("关键发现:\n")
            f.write("-" * 80 + "\n")
            f.write("1. 实时系统使用三层过滤机制:\n")
            f.write("   - 评分过滤: 要求≥7分\n")
            f.write("   - 失败计数过滤: 连续3次不达标才移除\n")
            f.write("   - 容量限制过滤: 最大50个币种，超出时智能淘汰\n")
            f.write("2. 智能淘汰机制考虑:\n")
            f.write("   - 持仓币种优先级(+100分)\n")
            f.write("   - 评分权重40%\n")
            f.write("   - 深度权重35%\n")
            f.write("   - 币龄权重25%\n")
            f.write("3. 过滤条件可能导致高评分币种被排除的原因:\n")
            f.write("   - 历史失败计数≥3次\n")
            f.write("   - 容量限制下的智能淘汰\n")
            f.write("   - 深度或币龄权重影响综合排名\n")
        
        self.logger.info(f"过滤条件分析报告已保存: {report_file}")
    
    def analyze_candidate_pool_filters(self):
        """分析候选池过滤条件"""
        self.logger.info("开始分析候选池过滤条件...")
        
        # 1. 加载候选池
        candidates = self.load_candidate_cache()
        if not candidates:
            self.logger.error("无法加载候选池数据")
            return
        
        # 2. 模拟候选池管理
        simulated_cache = self.simulate_candidate_pool_management(candidates)
        
        # 3. 应用过滤条件
        filter_results, final_qualified = self.apply_filter_conditions(simulated_cache)
        
        # 4. 生成分析报告
        self.generate_filter_analysis_report(filter_results, final_qualified)
        
        return filter_results, final_qualified

def main():
    """主函数"""
    analyzer = CandidatePoolFilterAnalyzer()
    analyzer.analyze_candidate_pool_filters()

if __name__ == "__main__":
    main()