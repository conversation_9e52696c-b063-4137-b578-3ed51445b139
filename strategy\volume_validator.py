"""
双重成交量验证模块
实现相对成交量(≥1.5倍20日均值)和绝对成交量(≥100万USDT)双重验证
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple


class VolumeValidator:
    """双重成交量验证器"""
    
    def __init__(self, trader, config: Dict[str, Any]):
        """
        初始化成交量验证器
        
        Args:
            trader: 交易器实例
            config: 成交量验证配置
        """
        self.trader = trader
        self.config = config
        self.log = logging.getLogger(self.__class__.__name__)
        
        # 配置参数
        self.min_absolute_volume = config.get('min_absolute_volume', 1000000)  # 100万USDT
        self.min_relative_ratio = config.get('min_relative_ratio', 1.5)  # 1.5倍
        self.volume_period = config.get('volume_period', 20)  # 20日均值
        self.enable_cache = config.get('enable_cache', True)
        
        # 成交量缓存
        self.volume_cache = {}
        self.cache_ttl = config.get('cache_ttl', 300)  # 5分钟缓存
        
        self.log.info(f"成交量验证器初始化完成 - 绝对成交量阈值: {self.min_absolute_volume:,} USDT, "
                     f"相对成交量倍数: {self.min_relative_ratio}x, 均值周期: {self.volume_period}日")
    
    def validate_volume(self, symbol: str, df_data: pd.DataFrame) -> Tuple[bool, Dict[str, Any]]:
        """
        执行双重成交量验证
        
        Args:
            symbol: 交易对符号
            df_data: K线数据DataFrame
            
        Returns:
            Tuple[bool, Dict]: (是否通过验证, 验证详情)
        """
        try:
            # 获取成交量数据
            volume_col = 'volume' if 'volume' in df_data.columns else 'v'
            quote_volume_col = 'quote_volume' if 'quote_volume' in df_data.columns else 'qv'
            
            if len(df_data) < self.volume_period:
                return False, {
                    'reason': 'insufficient_data',
                    'message': f'数据不足，需要至少{self.volume_period}根K线',
                    'data_length': len(df_data)
                }
            
            # 获取当前成交量数据
            current_volume = df_data[volume_col].iloc[-1]
            current_quote_volume = df_data[quote_volume_col].iloc[-1] if quote_volume_col in df_data.columns else None
            
            # 如果没有报价成交量，使用价格估算
            if current_quote_volume is None:
                close_col = 'close' if 'close' in df_data.columns else 'c'
                current_price = df_data[close_col].iloc[-1]
                current_quote_volume = current_volume * current_price
            
            # 1. 绝对成交量验证
            absolute_pass = current_quote_volume >= self.min_absolute_volume
            
            # 2. 相对成交量验证
            volume_mean = df_data[volume_col].rolling(self.volume_period).mean().iloc[-1]
            relative_ratio = current_volume / volume_mean if volume_mean > 0 else 0
            relative_pass = relative_ratio >= self.min_relative_ratio
            
            # 综合验证结果
            validation_pass = absolute_pass and relative_pass
            
            # 构建验证详情
            validation_details = {
                'symbol': symbol,
                'absolute_pass': absolute_pass,
                'relative_pass': relative_pass,
                'validation_pass': validation_pass,
                'current_quote_volume': current_quote_volume,
                'min_absolute_volume': self.min_absolute_volume,
                'current_volume': current_volume,
                'volume_mean': volume_mean,
                'relative_ratio': relative_ratio,
                'min_relative_ratio': self.min_relative_ratio,
                'volume_period': self.volume_period
            }
            
            # 记录验证结果
            if validation_pass:
                self.log.info(f"{symbol} 成交量验证通过 - "
                             f"绝对成交量: {current_quote_volume:,.0f} USDT (≥{self.min_absolute_volume:,}), "
                             f"相对成交量: {relative_ratio:.2f}x (≥{self.min_relative_ratio}x)")
            else:
                reasons = []
                if not absolute_pass:
                    reasons.append(f"绝对成交量不足: {current_quote_volume:,.0f} < {self.min_absolute_volume:,} USDT")
                if not relative_pass:
                    reasons.append(f"相对成交量不足: {relative_ratio:.2f}x < {self.min_relative_ratio}x")
                
                self.log.debug(f"{symbol} 成交量验证失败 - {'; '.join(reasons)}")
                validation_details['failure_reasons'] = reasons
            
            return validation_pass, validation_details
            
        except Exception as e:
            self.log.error(f"{symbol} 成交量验证异常: {e}")
            return False, {
                'reason': 'validation_error',
                'message': str(e),
                'symbol': symbol
            }
    
    def get_volume_statistics(self, symbol: str, df_data: pd.DataFrame) -> Dict[str, Any]:
        """
        获取成交量统计信息
        
        Args:
            symbol: 交易对符号
            df_data: K线数据DataFrame
            
        Returns:
            Dict: 成交量统计信息
        """
        try:
            volume_col = 'volume' if 'volume' in df_data.columns else 'v'
            quote_volume_col = 'quote_volume' if 'quote_volume' in df_data.columns else 'qv'
            
            if len(df_data) < self.volume_period:
                return {'error': 'insufficient_data'}
            
            # 基础统计
            current_volume = df_data[volume_col].iloc[-1]
            volume_mean = df_data[volume_col].rolling(self.volume_period).mean().iloc[-1]
            volume_std = df_data[volume_col].rolling(self.volume_period).std().iloc[-1]
            volume_median = df_data[volume_col].rolling(self.volume_period).median().iloc[-1]
            
            # 报价成交量统计
            if quote_volume_col in df_data.columns:
                current_quote_volume = df_data[quote_volume_col].iloc[-1]
                quote_volume_mean = df_data[quote_volume_col].rolling(self.volume_period).mean().iloc[-1]
            else:
                close_col = 'close' if 'close' in df_data.columns else 'c'
                current_price = df_data[close_col].iloc[-1]
                current_quote_volume = current_volume * current_price
                quote_volume_mean = volume_mean * current_price
            
            # 计算百分位数
            volume_data = df_data[volume_col].tail(self.volume_period)
            volume_percentiles = {
                'p25': volume_data.quantile(0.25),
                'p50': volume_data.quantile(0.50),
                'p75': volume_data.quantile(0.75),
                'p90': volume_data.quantile(0.90),
                'p95': volume_data.quantile(0.95)
            }
            
            return {
                'symbol': symbol,
                'current_volume': current_volume,
                'current_quote_volume': current_quote_volume,
                'volume_mean': volume_mean,
                'volume_std': volume_std,
                'volume_median': volume_median,
                'quote_volume_mean': quote_volume_mean,
                'relative_ratio': current_volume / volume_mean if volume_mean > 0 else 0,
                'volume_percentiles': volume_percentiles,
                'volume_z_score': (current_volume - volume_mean) / volume_std if volume_std > 0 else 0,
                'period': self.volume_period
            }
            
        except Exception as e:
            self.log.error(f"{symbol} 成交量统计计算异常: {e}")
            return {'error': str(e)}
    
    def batch_validate_volumes(self, symbols_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict[str, Any]]:
        """
        批量验证成交量
        
        Args:
            symbols_data: {symbol: DataFrame} 格式的数据字典
            
        Returns:
            Dict: {symbol: validation_result} 格式的验证结果
        """
        results = {}
        
        for symbol, df_data in symbols_data.items():
            try:
                validation_pass, validation_details = self.validate_volume(symbol, df_data)
                results[symbol] = validation_details
            except Exception as e:
                self.log.error(f"{symbol} 批量成交量验证异常: {e}")
                results[symbol] = {
                    'validation_pass': False,
                    'reason': 'batch_validation_error',
                    'message': str(e)
                }
        
        # 统计批量验证结果
        total_count = len(results)
        passed_count = sum(1 for r in results.values() if r.get('validation_pass', False))
        
        self.log.info(f"批量成交量验证完成 - 总数: {total_count}, 通过: {passed_count}, "
                     f"通过率: {passed_count/total_count*100:.1f}%")
        
        return results
    
    def update_config(self, new_config: Dict[str, Any]):
        """
        更新配置参数
        
        Args:
            new_config: 新的配置参数
        """
        old_config = self.config.copy()
        self.config.update(new_config)
        
        # 更新实例变量
        self.min_absolute_volume = self.config.get('min_absolute_volume', 1000000)
        self.min_relative_ratio = self.config.get('min_relative_ratio', 1.5)
        self.volume_period = self.config.get('volume_period', 20)
        
        self.log.info(f"成交量验证器配置已更新 - "
                     f"绝对成交量阈值: {old_config.get('min_absolute_volume', 1000000):,} → {self.min_absolute_volume:,}, "
                     f"相对成交量倍数: {old_config.get('min_relative_ratio', 1.5)}x → {self.min_relative_ratio}x")