#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速候选池分析工具 - 基于缓存数据
模仿旧版策略的快速评分输出机制
"""

import logging
import pickle
import json
from pathlib import Path
from datetime import datetime, timezone
import pandas as pd
from cache_manager import CacheManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fast_candidate_analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('FastCandidateAnalyzer')

class FastCandidateAnalyzer:
    def __init__(self):
        self.cache_manager = CacheManager()
        self.logger = logger
        
    def load_cached_klines(self, symbol, interval='15m', limit=200):
        """从缓存加载K线数据"""
        try:
            # 构建缓存文件路径，与旧版策略保持一致
            cache_path = Path(f"cache/klines_{symbol}_{interval}_{limit}.pkl")
            
            if not cache_path.exists():
                # 尝试其他可能的缓存文件
                for alt_limit in [50, 100, 200]:
                    alt_path = Path(f"cache/klines_{symbol}_{interval}_{alt_limit}.pkl")
                    if alt_path.exists():
                        cache_path = alt_path
                        break
                else:
                    return None
            
            # 加载缓存数据
            with open(cache_path, 'rb') as f:
                df = pickle.load(f)
            
            if df is not None and not df.empty:
                self.logger.debug(f"成功加载缓存K线数据: {symbol} {interval} ({len(df)}条)")
                return df
            else:
                return None
                
        except Exception as e:
            self.logger.warning(f"加载缓存K线数据失败 {symbol}: {e}")
            return None
    
    def get_symbol_age_days(self, symbol):
        """获取币种年龄（天数）"""
        try:
            # 从缓存的symbols.json获取上线时间
            symbols_path = Path("cache/symbols.json")
            if symbols_path.exists():
                with open(symbols_path, 'r', encoding='utf-8') as f:
                    symbols_data = json.load(f)
                    
                for s in symbols_data:
                    if s['symbol'] == symbol and 'age_days' in s:
                        return s['age_days']
            
            # 默认返回一个较大的值（老币）
            return 1000
        except Exception as e:
            self.logger.warning(f"获取{symbol}币龄失败: {e}")
            return 1000
    
    def calculate_dynamic_timeframe(self, age_days):
        """根据币龄计算动态时间周期"""
        if age_days <= 7:
            return "1m"
        elif age_days <= 30:
            return "5m"
        elif age_days <= 90:
            return "15m"
        elif age_days <= 365:
            return "1h"
        elif age_days <= 730:
            return "4h"
        else:
            return "6h"
    
    def load_cached_depth(self, symbol):
        """从缓存加载深度数据"""
        try:
            cache_path = Path(f"cache/depth_{symbol}.json")
            if not cache_path.exists():
                return None
            
            with open(cache_path, 'r', encoding='utf-8') as f:
                depth_data = json.load(f)
            
            self.logger.debug(f"成功加载缓存深度数据: {symbol}")
            return depth_data
            
        except Exception as e:
            self.logger.warning(f"加载缓存深度数据失败 {symbol}: {e}")
            return None
    
    def calculate_score(self, symbol):
        """计算币种评分（基于旧版策略的M、L、V、A、T评分系统）"""
        try:
            # 获取K线数据
            klines_15m = self.load_cached_klines(symbol, '15m', 200)
            if klines_15m is None or klines_15m.empty:
                self.logger.warning(f"{symbol}: 未找到缓存K线数据")
                return None
            
            # 获取深度数据
            depth_data = self.load_cached_depth(symbol)
            if depth_data is None:
                self.logger.warning(f"{symbol}: 未找到缓存深度数据")
                return None
            
            # 获取币龄
            age_days = self.get_symbol_age_days(symbol)
            
            # 1. 深度评分 (L)
            score_l = 0
            if depth_data is not None:
                # 深度数据在缓存中是float类型，直接使用
                if isinstance(depth_data, (int, float)):
                    depth_value = float(depth_data)
                    if depth_value > 1000000:  # 100万以上
                        score_l = 2
                    elif depth_value > 500000:  # 50万以上
                        score_l = 1
                else:
                    # 如果是字典格式，尝试提取深度值
                    if isinstance(depth_data, dict) and 'depth' in depth_data:
                        depth_value = float(depth_data['depth'])
                        if depth_value > 1000000:
                            score_l = 2
                        elif depth_value > 500000:
                            score_l = 1
            
            # 2. 成交量变化评分 (V)
            score_v = 0
            if len(klines_15m) >= 2:
                recent_volume = float(klines_15m['volume'].iloc[-1])
                prev_volume = float(klines_15m['volume'].iloc[-2])
                if recent_volume > prev_volume * 1.5:
                    score_v = 2
                elif recent_volume > prev_volume * 1.2:
                    score_v = 1
            
            # 3. 币龄评分 (A)
            score_a = 0
            if age_days <= 7:
                score_a = 2  # 新币
            elif age_days <= 30:
                score_a = 2  # 次新币
            elif age_days <= 365:
                score_a = 2  # 一年内
            else:
                score_a = 1  # 老币降权
            
            # 4. 通道评分 (T) - 简化版
            score_t = 2  # 默认给2分，因为是从候选池来的
            
            # 5. 动量评分 (M)
            score_m = 0
            if len(klines_15m) >= 5:
                # 计算短期价格动量
                current_price = float(klines_15m['close'].iloc[-1])
                prev_price = float(klines_15m['close'].iloc[-5])
                price_change = (current_price - prev_price) / prev_price
                
                if price_change > 0.05:  # 5%以上涨幅
                    score_m = 2
                elif price_change > 0.02:  # 2%以上涨幅
                    score_m = 1
            
            # 计算总分
            total_score = score_m * 3 + score_l * 2 + score_v * 1 + score_a * 2 + score_t * 2
            
            # 基础保护机制
            if total_score < 7:
                total_score = max(total_score, 6)
            
            return {
                'symbol': symbol,
                'total_score': total_score,
                'score_m': score_m,
                'score_l': score_l,
                'score_v': score_v,
                'score_a': score_a,
                'score_t': score_t,
                'age_days': age_days,
                'current_price': float(klines_15m['close'].iloc[-1]) if not klines_15m.empty else 0
            }
            
        except Exception as e:
            self.logger.error(f"计算{symbol}评分失败: {e}")
            return None
    
    def analyze_cached_candidates(self):
        """分析缓存中的候选池数据"""
        try:
            # 直接读取候选池缓存文件
            candidates_path = Path("cache/candidates.pkl")
            if not candidates_path.exists():
                self.logger.warning("未找到候选池缓存文件")
                return []
            
            try:
                with open(candidates_path, 'rb') as f:
                    candidates = pickle.load(f)
                self.logger.info(f"从缓存加载了 {len(candidates)} 个候选币种")
            except Exception as e:
                self.logger.error(f"读取候选池缓存失败: {e}")
                return []
            
            if not candidates:
                self.logger.warning("候选池缓存为空")
                return []
            
            self.logger.info(f"从缓存加载候选池: {len(candidates)}个币种")
            
            results = []
            success_count = 0
            
            for symbol in candidates:
                try:
                    score_result = self.calculate_score(symbol)
                    if score_result:
                        results.append(score_result)
                        success_count += 1
                        
                        # 输出评分详情（模仿旧版策略格式）
                        score_detail = f"{symbol}评分:{{M({score_result['score_m']}*3={score_result['score_m']*3}),L({score_result['score_l']}*2={score_result['score_l']*2}),V({score_result['score_v']}*1={score_result['score_v']*1}),A({score_result['score_a']}*2={score_result['score_a']*2}),T({score_result['score_t']}*2={score_result['score_t']*2})}},总分:{score_result['total_score']:.2f}"
                        self.logger.info(score_detail)
                    else:
                        self.logger.warning(f"{symbol}: 评分计算失败")
                        
                except Exception as e:
                    self.logger.error(f"分析{symbol}失败: {e}")
            
            self.logger.info(f"快速分析完成: {success_count}个成功/{len(candidates)}个处理")
            
            # 按评分排序
            results.sort(key=lambda x: x['total_score'], reverse=True)
            
            return results
            
        except Exception as e:
            self.logger.error(f"分析候选池失败: {e}")
            return []
    
    def generate_report(self, results):
        """生成分析报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"fast_analysis_report_{timestamp}.txt"
            
            # 统计数据
            total_count = len(results)
            high_score_count = len([r for r in results if r['total_score'] >= 10])
            
            report_content = []
            report_content.append("=" * 80)
            report_content.append("快速候选池分析报告（基于缓存数据）")
            report_content.append("=" * 80)
            report_content.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_content.append("")
            report_content.append("📊 总体统计:")
            report_content.append(f"  成功分析: {total_count}")
            report_content.append(f"  高评分币种(≥10分): {high_score_count}")
            report_content.append("")
            
            if results:
                report_content.append("📋 评分详情（按评分降序）:")
                report_content.append("-" * 80)
                report_content.append(f"{'币种':<15} | {'总分':<6} | {'M':<3} | {'L':<3} | {'V':<3} | {'A':<3} | {'T':<3} | {'币龄(天)':<8} | {'价格':<12}")
                report_content.append("-" * 80)
                
                for result in results:
                    line = f"{result['symbol']:<15} | {result['total_score']:<6.1f} | {result['score_m']:<3} | {result['score_l']:<3} | {result['score_v']:<3} | {result['score_a']:<3} | {result['score_t']:<3} | {result['age_days']:<8} | {result['current_price']:<12.6f}"
                    report_content.append(line)
                
                report_content.append("")
                report_content.append("📈 评分分布:")
                score_ranges = {
                    "12分以上": len([r for r in results if r['total_score'] >= 12]),
                    "10-12分": len([r for r in results if 10 <= r['total_score'] < 12]),
                    "8-10分": len([r for r in results if 8 <= r['total_score'] < 10]),
                    "6-8分": len([r for r in results if 6 <= r['total_score'] < 8]),
                    "6分以下": len([r for r in results if r['total_score'] < 6])
                }
                
                for range_name, count in score_ranges.items():
                    report_content.append(f"  {range_name}: {count} 个币种")
            
            report_content.append("")
            report_content.append("=" * 80)
            
            # 保存报告
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_content))
            
            # 输出到控制台
            for line in report_content:
                print(line)
            
            print(f"\n报告已保存到: {report_file}")
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")

def main():
    """主函数"""
    analyzer = FastCandidateAnalyzer()
    
    print("开始快速候选池分析（基于缓存数据）...")
    
    # 分析候选池
    results = analyzer.analyze_cached_candidates()
    
    # 生成报告
    analyzer.generate_report(results)

if __name__ == "__main__":
    main()