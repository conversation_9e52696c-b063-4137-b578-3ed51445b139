#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml
import time
from strategy.maker_channel import MakerChannelStrategy
from binance_trader import BinanceTrader

def global_orphan_cleanup():
    """全局孤儿订单清理工具"""
    try:
        # 读取配置
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 初始化交易器
        trader = BinanceTrader(config)
        
        # 初始化策略（用于访问清理方法）
        strategy = MakerChannelStrategy(trader, config)
        
        print("=== 开始全局孤儿订单检查和清理 ===")
        
        # 1. 检查所有交易对的孤儿止损/止盈单
        print("正在检查所有交易对的孤儿止损/止盈单...")
        all_orphan_orders = strategy.check_orphan_stop_orders()  # 不传symbol参数，检查所有
        
        if not all_orphan_orders:
            print("✅ 未发现任何孤儿止损/止盈单")
            return
        
        print(f"发现 {len(all_orphan_orders)} 个孤儿止损/止盈单:")
        
        # 按交易对分组显示
        orphan_by_symbol = {}
        for orphan in all_orphan_orders:
            symbol = orphan['symbol']
            if symbol not in orphan_by_symbol:
                orphan_by_symbol[symbol] = []
            orphan_by_symbol[symbol].append(orphan)
        
        for symbol, orders in orphan_by_symbol.items():
            print(f"\n{symbol} ({len(orders)}个孤儿订单):")
            for order in orders:
                print(f"  订单ID: {order['orderId']}, 类型: {order['type']}, 方向: {order['side']}, 止损价: {order['stopPrice']}")
        
        # 2. 执行清理
        print("\n=== 开始清理孤儿订单 ===")
        total_cleaned = 0
        
        for symbol, orders in orphan_by_symbol.items():
            print(f"\n正在清理 {symbol} 的 {len(orders)} 个孤儿订单...")
            
            # 使用策略的清理方法
            cleaned_count = strategy.clean_orphan_orders(symbol, include_stop_orders=True)
            total_cleaned += cleaned_count
            
            if cleaned_count > 0:
                print(f"✅ {symbol}: 成功清理 {cleaned_count} 个孤儿订单")
            else:
                print(f"❌ {symbol}: 清理失败或无订单被清理")
            
            time.sleep(1)  # 避免频繁操作
        
        # 3. 验证清理结果
        print("\n=== 验证清理结果 ===")
        time.sleep(3)  # 等待订单取消生效
        
        remaining_orphans = strategy.check_orphan_stop_orders()
        
        if not remaining_orphans:
            print(f"✅ 全局孤儿订单清理完成！总共清理了 {total_cleaned} 个订单")
        else:
            print(f"⚠️ 仍有 {len(remaining_orphans)} 个孤儿订单未清理:")
            for orphan in remaining_orphans:
                print(f"  {orphan['symbol']}: 订单ID {orphan['orderId']} ({orphan['type']})")
        
        # 4. 显示当前活跃持仓
        print("\n=== 当前活跃持仓 ===")
        all_positions = trader.get_positions()
        active_positions = [p for p in all_positions if abs(float(p.get('positionAmt', 0))) > 0.001]
        
        if active_positions:
            print(f"当前有 {len(active_positions)} 个活跃持仓:")
            for pos in active_positions:
                symbol = pos.get('symbol')
                qty = pos.get('positionAmt')
                unrealized_pnl = pos.get('unRealizedProfit', '0')
                print(f"  {symbol}: {qty} (未实现盈亏: {unrealized_pnl})")
        else:
            print("当前无活跃持仓")
            
    except Exception as e:
        print(f"全局清理过程发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    global_orphan_cleanup()