#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终递归修复验证测试
测试所有可能导致递归的场景
"""

import sys
import os
import json
import logging
import traceback

def test_final_recursion_fix():
    """最终递归修复验证测试"""
    print("开始最终递归修复验证测试...")
    
    try:
        # 1. 测试基础导入
        print("1. 测试基础导入...")
        from binance_trader import BinanceTrader
        from strategy.maker_channel import MakerChannelStrategy
        print("   ✅ 基础导入成功")
        
        # 2. 测试配置加载
        print("2. 测试配置加载...")
        with open('config/config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 添加策略需要的配置参数
        config['first_nominal'] = 100  # 添加缺失的配置
        config['order_ttl'] = 300
        config['network_check_interval'] = 60
        config['max_symbol_failures'] = 5
        config['failure_cooldown'] = 3600
        config['network_error_cooldown'] = 300
        
        print("   ✅ 配置加载成功")
        
        # 3. 测试BinanceTrader初始化
        print("3. 测试BinanceTrader初始化...")
        trader = BinanceTrader(config)
        print("   ✅ BinanceTrader初始化成功")
        
        # 4. 测试K线数据获取（最容易触发递归的操作）
        print("4. 测试K线数据获取...")
        klines = trader.get_klines('BTCUSDT', '15m', 5)
        print(f"   ✅ 成功获取 {len(klines)} 条K线数据")
        
        # 5. 测试策略初始化
        print("5. 测试策略初始化...")
        strategy = MakerChannelStrategy(trader, config)
        print("   ✅ 策略初始化成功")
        
        # 6. 测试策略K线获取
        print("6. 测试策略K线获取...")
        strategy_klines = strategy.get_klines('BTCUSDT', '15m', 5)
        print(f"   ✅ 策略成功获取 {len(strategy_klines)} 条K线数据")
        
        # 7. 测试深度数据获取
        print("7. 测试深度数据获取...")
        depth = strategy.get_depth01pct('BTCUSDT')
        print(f"   ✅ 成功获取深度数据: {depth}")
        
        print("\n🎉 所有测试通过！递归问题已完全解决！")
        return True
        
    except RecursionError as e:
        print(f"\n❌ 递归错误仍然存在: {e}")
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"\n⚠️  其他错误（非递归）: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_recursion_fix()
    sys.exit(0 if success else 1)