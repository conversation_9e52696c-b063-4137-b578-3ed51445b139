import os
import json
import pickle
import time
import pandas as pd
from pathlib import Path

CACHE_DIR = Path("cache")
CACHE_DIR.mkdir(exist_ok=True)

class CacheManager:
    def __init__(self):
        self.cache_dir = CACHE_DIR
        
    # ---------- L0 全币种基础信息 ----------
    def load_symbols(self, trader):
        """加载全币种基础信息，每日更新一次"""
        path = self.cache_dir / "symbols.json"
        if path.exists() and time.time() - path.stat().st_mtime < 86400:
            try:
                return json.load(open(path, 'r', encoding='utf-8'))
            except:
                pass
        
        # 冷启动拉一次全量数据
        info = trader.http.get('/fapi/v1/exchangeInfo')
        if not info or 'symbols' not in info:
            return []
            
        symbols = []
        for s in info['symbols']:
            if (s['contractType'] == 'PERPETUAL' and 
                s['status'] == 'TRADING' and 
                s['symbol'].endswith('USDT')):
                
                symbol_data = {
                    'symbol': s['symbol'],
                    'baseAsset': s['baseAsset'],
                    'quoteAsset': s['quoteAsset'],
                    'status': s['status']
                }
                
                # 处理价格精度
                price_filter = next((f for f in s['filters'] if f['filterType'] == 'PRICE_FILTER'), None)
                if price_filter:
                    symbol_data['tick_size'] = float(price_filter['tickSize'])
                    
                # 处理数量精度
                lot_filter = next((f for f in s['filters'] if f['filterType'] == 'LOT_SIZE'), None)
                if lot_filter:
                    symbol_data['step_size'] = float(lot_filter['stepSize'])
                    symbol_data['min_qty'] = float(lot_filter['minQty'])
                    
                # 处理上线时间
                if 'onboardDate' in s:
                    onboard_timestamp = s['onboardDate']
                    launch_ts = pd.to_datetime(onboard_timestamp, unit='ms')
                    if launch_ts.tz is None:
                        launch_ts = launch_ts.tz_localize('UTC')
                    symbol_data['onboard_date'] = launch_ts.isoformat()
                    symbol_data['age_days'] = (pd.Timestamp.utcnow() - launch_ts).days
                
                symbols.append(symbol_data)
        
        # 保存到缓存
        json.dump(symbols, open(path, 'w', encoding='utf-8'), default=str, indent=2)
        return symbols

    # ---------- L1 候选池 ----------
    def load_candidates(self):
        """加载候选池，15分钟更新一次"""
        path = self.cache_dir / "candidates.pkl"
        if path.exists() and time.time() - path.stat().st_mtime < 900:
            try:
                return pickle.load(open(path, 'rb'))
            except:
                return []
        return []

    def save_candidates(self, candidates):
        """保存候选池"""
        path = self.cache_dir / "candidates.pkl"
        pickle.dump(candidates, open(path, 'wb'))

    # ---------- 币种基础信息 ----------
    def save_symbols(self, symbols):
        """保存币种基础信息"""
        path = self.cache_dir / "symbols.pkl"
        pickle.dump(symbols, open(path, 'wb'))

    # ---------- L2 持仓 ----------
    def load_positions(self):
        """加载持仓信息"""
        path = self.cache_dir / "positions.pkl"
        if path.exists():
            try:
                return pickle.load(open(path, 'rb'))
            except:
                return {}
        return {}

    def save_positions(self, positions):
        """保存持仓信息"""
        path = self.cache_dir / "positions.pkl"
        pickle.dump(positions, open(path, 'wb'))

    # ---------- L3 深度 ----------
    def load_depth(self, symbol):
        """加载深度数据，5分钟更新一次"""
        path = self.cache_dir / f"depth_{symbol}.json"
        if path.exists() and time.time() - path.stat().st_mtime < 300:
            try:
                return json.load(open(path, 'r', encoding='utf-8'))
            except:
                return None
        return None

    def save_depth(self, symbol, depth):
        """保存深度数据"""
        path = self.cache_dir / f"depth_{symbol}.json"
        json.dump(depth, open(path, 'w', encoding='utf-8'), default=str)

    # ---------- K线数据缓存 ----------
    def load_klines(self, symbol, interval, limit):
        """加载K线数据缓存"""
        cache_key = f"klines_{symbol}_{interval}_{limit}"
        path = self.cache_dir / f"{cache_key}.pkl"
        if path.exists() and time.time() - path.stat().st_mtime < 300:  # 5分钟缓存
            try:
                data = pickle.load(open(path, 'rb'))
                # 确保加载的数据有时间戳时区处理
                if hasattr(data, 'index') and not data.empty:
                    try:
                        # 统一时区处理：如果数据没有时区，设置为UTC
                        if data.index.tz is None:
                            data.index = data.index.tz_localize('UTC')
                        # 如果已经有时区，确保是UTC时区
                        elif str(data.index.tz) != 'UTC':
                            data.index = data.index.tz_convert('UTC')
                    except Exception as e:
                        print(f"缓存时区处理失败 {symbol}: {e}")
                        return None
                return data
            except Exception as e:
                print(f"加载缓存失败 {symbol}: {e}")
                return None
        return None

    def save_klines(self, symbol, interval, limit, klines_data):
        """保存K线数据缓存"""
        cache_key = f"klines_{symbol}_{interval}_{limit}"
        path = self.cache_dir / f"{cache_key}.pkl"
        pickle.dump(klines_data, open(path, 'wb'))

    # ---------- 清理过期缓存 ----------
    def cleanup_expired_cache(self, max_age_hours=24):
        """清理过期缓存文件"""
        current_time = time.time()
        for cache_file in self.cache_dir.glob("*"):
            if cache_file.is_file():
                file_age = current_time - cache_file.stat().st_mtime
                if file_age > max_age_hours * 3600:
                    try:
                        cache_file.unlink()
                    except:
                        pass