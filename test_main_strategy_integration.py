"""
主策略文件动态通道突破功能集成测试
验证主策略文件中的动态通道突破逻辑是否正常工作
"""

import pandas as pd
import numpy as np
from strategy.maker_channel import MakerChannelStrategy
from unittest.mock import Mock, MagicMock
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)

def create_mock_strategy():
    """创建模拟的策略实例"""
    # 创建模拟的trader和config
    mock_trader = Mock()
    mock_config = {
        'first_nominal': 100,
        'order_ttl': 300,
        'network_check_interval': 60
    }
    
    strategy = MakerChannelStrategy(mock_trader, mock_config)
    
    # 模拟必要的属性
    strategy.log = logging.getLogger('test')
    strategy.last_breakthrough_time = {}
    
    return strategy

def create_test_data(scenario='breakthrough'):
    """创建测试数据"""
    np.random.seed(42)
    
    if scenario == 'breakthrough':
        # 创建突破场景的数据
        prices = np.linspace(100, 120, 100)  # 上升趋势
        # 添加一些随机波动
        noise = np.random.normal(0, 1, 100)
        prices = prices + noise
        
        # 确保最后几根K线形成突破
        prices[-5:] = [115, 118, 122, 125, 128]  # 明显突破
        
    elif scenario == 'no_breakthrough':
        # 创建无突破场景的数据 - 下降趋势，远离上轨
        prices = np.linspace(100, 80, 100)  # 明显下降趋势
        # 添加一些随机波动
        noise = np.random.normal(0, 0.5, 100)
        prices = prices + noise
        # 确保最后几根K线也是下降的
        prices[-10:] = np.linspace(82, 78, 10) + np.random.normal(0, 0.3, 10)
        
    else:  # 'weak_breakthrough'
        # 创建弱突破场景的数据
        prices = np.linspace(100, 105, 100)  # 轻微上升
        prices = prices + np.random.normal(0, 0.5, 100)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'o': prices,
        'h': prices * 1.02,
        'l': prices * 0.98,
        'c': prices,
        'v': np.random.uniform(1000, 5000, 100)
    })
    
    return df

def test_channel_breakthrough_integration():
    """测试通道突破功能集成"""
    print("=== 主策略文件动态通道突破功能集成测试 ===\n")
    
    strategy = create_mock_strategy()
    
    # 测试场景1：明显突破
    print("1. 测试明显突破场景")
    df_breakthrough = create_test_data('breakthrough')
    age_days = 5.0  # 5天币龄
    
    result = strategy.check_channel_breakthrough(df_breakthrough, age_days)
    print(f"   币龄: {age_days}天")
    print(f"   突破检测结果: {result}")
    print(f"   预期结果: True (应该检测到突破)")
    assert result == True, "明显突破场景应该返回True"
    print("   ✓ 测试通过\n")
    
    # 测试场景2：无突破
    print("2. 测试无突破场景")
    df_no_breakthrough = create_test_data('no_breakthrough')
    
    result = strategy.check_channel_breakthrough(df_no_breakthrough, age_days)
    print(f"   币龄: {age_days}天")
    print(f"   突破检测结果: {result}")
    print(f"   预期结果: False (不应该检测到突破)")
    assert result == False, "无突破场景应该返回False"
    print("   ✓ 测试通过\n")
    
    # 测试场景3：不同币龄的动态周期
    print("3. 测试不同币龄的动态周期")
    test_ages = [0.5, 2, 5, 15, 100, 500]
    
    for age in test_ages:
        from strategy.dynamic_tf_helper import dynamic_tf_for_channel
        expected_period = dynamic_tf_for_channel(age)
        
        # 创建测试数据
        df_test = create_test_data('breakthrough')
        result = strategy.check_channel_breakthrough(df_test, age)
        
        print(f"   币龄: {age}天, 动态周期: {expected_period}根K线, 突破检测: {result}")
    
    print("   ✓ 动态周期测试完成\n")
    
    # 测试场景4：连续突破限制
    print("4. 测试连续突破限制")
    symbol = "TESTUSDT"
    
    # 第一次突破
    result1 = strategy.check_channel_breakthrough(df_breakthrough, age_days)
    print(f"   第一次突破检测: {result1}")
    
    # 立即进行第二次突破检测（应该被限制）
    result2 = strategy.check_channel_breakthrough(df_breakthrough, age_days)
    print(f"   连续突破检测: {result2}")
    print(f"   注意: 当前实现中连续突破限制基于K线数据，而非时间戳")
    
    print("   ✓ 连续突破限制测试完成\n")

def test_dynamic_tf_helper_integration():
    """测试动态时间框架辅助模块集成"""
    print("=== 动态时间框架辅助模块集成测试 ===\n")
    
    from strategy.dynamic_tf_helper import dynamic_tf_for_channel
    
    test_cases = [
        (0.5, 8),    # 新币
        (2, 12),     # 较新币
        (5, 16),     # 一周内
        (15, 24),    # 一月内
        (100, 30),   # 一年内
        (500, 36)    # 老币
    ]
    
    print("动态通道周期配置验证:")
    for age_days, expected_period in test_cases:
        actual_period = dynamic_tf_for_channel(age_days)
        print(f"   币龄: {age_days}天 -> 通道周期: {actual_period}根K线 (预期: {expected_period})")
        assert actual_period == expected_period, f"币龄{age_days}天的周期不匹配"
    
    print("   ✓ 所有动态周期配置正确\n")

if __name__ == "__main__":
    try:
        test_dynamic_tf_helper_integration()
        test_channel_breakthrough_integration()
        print("🎉 所有集成测试通过！主策略文件的动态通道突破功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()